# Prompt Evaluation Quick Wins Implementation Plan

## Executive Summary

This document outlines immediate, low-impact, highly automated implementations for prompt evaluation in your workflow system. These quick wins require minimal setup, leverage existing infrastructure, and provide immediate value with low risk.

## Quick Win Strategy

**Philosophy:** Start with the simplest possible implementations that provide immediate value and build momentum for larger initiatives.

**Criteria:**

- ✅ **Automated** - Minimal manual intervention required
- ✅ **Low Impact** - No changes to existing workflows
- ✅ **High Value** - Immediate insights and improvements
- ✅ **Low Risk** - Non-breaking, additive changes only

## Quick Win #1: Prompt Quality Scoring (1-2 hours)

### Implementation

Create a simple activity that can be called from any workflow to score prompt outputs.

```typescript
// packages/temporal/src/workflows/eval/prompt-quality-scorer/activities.ts
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { quickQualityPrompt } from './prompts.js';
import type { QuickQualityScore } from './types.js';
import { QuickQualityScoreSchema } from './types.js';

const { openaiChatService } = workerModule;

export async function scorePromptOutput(
  promptContent: string,
  output: string,
  context?: string
): Promise<QuickQualityScore> {
  const messages = await threadTemplate(quickQualityPrompt, {
    prompt: promptContent,
    output,
    context: context || 'No specific context provided',
  });

  return openaiChatService.request<QuickQualityScore>({
    messages,
    model: 'gpt-4o-mini', // Use cheaper model for quick scoring
    schema: QuickQualityScoreSchema,
    schemaName: 'quick_quality_score',
  });
}

export default WorkflowScope.register(import.meta.url, {
  scorePromptOutput,
});
```

```typescript
// types.ts
import { z } from 'zod';

export const QuickQualityScoreSchema = z.object({
  score: z.number().min(1).max(10),
  category: z.enum(['excellent', 'good', 'fair', 'poor']),
  reasoning: z.string(),
  topIssue: z.string().optional(),
  quickFix: z.string().optional(),
});

export type QuickQualityScore = z.infer<typeof QuickQualityScoreSchema>;
```

```typescript
// prompts.ts
export const quickQualityPrompt = [
  {
    role: 'system',
    content: `You are a prompt quality evaluator. Score outputs 1-10 based on:

    ## Scoring Criteria
    - 9-10: Excellent - Clear, complete, accurate
    - 7-8: Good - Mostly correct with minor issues  
    - 5-6: Fair - Adequate but has problems
    - 3-4: Poor - Significant issues
    - 1-2: Very Poor - Unusable

    ## Categories
    - excellent: 9-10 points
    - good: 7-8 points  
    - fair: 5-6 points
    - poor: 1-4 points

    Be concise and specific in your feedback.`,
  },
  {
    role: 'user',
    content: `
    <prompt>{{prompt}}</prompt>
    <output>{{output}}</output>
    <context>{{context}}</context>
    `,
  },
];
```

### Usage

Add to any existing workflow:

```typescript
// In any workflow activity
import { scorePromptOutput } from '@flow/temporal/src/workflows/eval/prompt-quality-scorer/activities.js';

// After generating content
const quality = await scorePromptOutput(
  JSON.stringify(myPrompt),
  generatedContent,
  'Content generation for blog post'
);

// Log quality score
console.log(`Quality score: ${quality.score}/10 (${quality.category})`);
if (quality.topIssue) {
  console.log(`Top issue: ${quality.topIssue}`);
}
```

**Benefits:**

- Instant quality feedback for any prompt
- No workflow changes required
- Cheap to run (gpt-4o-mini)
- Can be added to existing workflows incrementally

## Quick Win #2: Prompt Performance Logger (30 minutes)

### Implementation

Simple logging utility that tracks prompt performance over time.

```typescript
// packages/temporal/src/workflows/eval/prompt-logger/activities.ts
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';

const { logger } = workerModule;

export async function logPromptPerformance(
  workflowName: string,
  promptName: string,
  executionTime: number,
  outputLength: number,
  qualityScore?: number
): Promise<void> {
  const performanceData = {
    timestamp: new Date().toISOString(),
    workflowName,
    promptName,
    executionTime,
    outputLength,
    qualityScore,
    environment: process.env.NODE_ENV || 'development',
  };

  logger.info('prompt_performance', performanceData);
}

export default WorkflowScope.register(import.meta.url, {
  logPromptPerformance,
});
```

### Usage

Wrap existing LLM calls:

```typescript
// Before
const result = await openaiChatService.request<Type>({
  messages,
  model: 'gpt-4o',
  schema: TypeSchema,
  schemaName: 'schema_name',
});

// After
const startTime = Date.now();
const result = await openaiChatService.request<Type>({
  messages,
  model: 'gpt-4o',
  schema: TypeSchema,
  schemaName: 'schema_name',
});
const executionTime = Date.now() - startTime;

// Log performance
await logPromptPerformance(
  'content-utils/archetype-detector',
  'writingStylePrompt',
  executionTime,
  JSON.stringify(result).length
);
```

**Benefits:**

- Zero-setup performance tracking
- Historical performance data
- Easy to add to any workflow
- Works with existing logging infrastructure

## Quick Win #3: Automated Prompt Regression Detection (45 minutes)

### Implementation

Simple comparison tool that detects when prompts start performing differently.

```typescript
// packages/temporal/src/workflows/eval/prompt-regression/activities.ts
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';

const { logger } = workerModule;

interface PromptBaseline {
  averageScore: number;
  averageLength: number;
  sampleCount: number;
  lastUpdated: string;
}

export async function checkPromptRegression(
  workflowName: string,
  promptName: string,
  currentScore: number,
  currentLength: number
): Promise<boolean> {
  const baselineKey = `prompt_baseline:${workflowName}:${promptName}`;

  // Get baseline from simple file storage (start simple)
  const baseline = await loadBaseline(baselineKey);

  if (!baseline) {
    // Create initial baseline
    await saveBaseline(baselineKey, {
      averageScore: currentScore,
      averageLength: currentLength,
      sampleCount: 1,
      lastUpdated: new Date().toISOString(),
    });
    return false;
  }

  // Check for significant regression
  const scoreDrop = baseline.averageScore - currentScore;
  const lengthChange = Math.abs(baseline.averageLength - currentLength);

  const isRegression = scoreDrop > 1.5 || lengthChange > baseline.averageLength * 0.3;

  if (isRegression) {
    logger.warn('prompt_regression_detected', {
      workflowName,
      promptName,
      scoreDrop,
      lengthChange,
      baseline: baseline.averageScore,
      current: currentScore,
    });
  }

  // Update baseline with exponential moving average
  const alpha = 0.1; // Weight for new data
  const updatedBaseline = {
    averageScore: baseline.averageScore * (1 - alpha) + currentScore * alpha,
    averageLength: baseline.averageLength * (1 - alpha) + currentLength * alpha,
    sampleCount: baseline.sampleCount + 1,
    lastUpdated: new Date().toISOString(),
  };

  await saveBaseline(baselineKey, updatedBaseline);
  return isRegression;
}

async function loadBaseline(key: string): Promise<PromptBaseline | null> {
  try {
    const fs = await import('fs/promises');
    const path = `/tmp/prompt_baselines/${key}.json`;
    const data = await fs.readFile(path, 'utf-8');
    return JSON.parse(data);
  } catch {
    return null;
  }
}

async function saveBaseline(key: string, baseline: PromptBaseline): Promise<void> {
  try {
    const fs = await import('fs/promises');
    const path = `/tmp/prompt_baselines/${key}.json`;
    await fs.mkdir('/tmp/prompt_baselines', { recursive: true });
    await fs.writeFile(path, JSON.stringify(baseline, null, 2));
  } catch (error) {
    console.error('Failed to save baseline:', error);
  }
}

export default WorkflowScope.register(import.meta.url, {
  checkPromptRegression,
});
```

### Usage

Add to quality scoring workflow:

```typescript
// After scoring prompt output
const quality = await scorePromptOutput(promptContent, output, context);

// Check for regression
const isRegression = await checkPromptRegression(
  'content-utils/archetype-detector',
  'writingStylePrompt',
  quality.score,
  output.length
);

if (isRegression) {
  console.log('⚠️  Prompt regression detected!');
}
```

**Benefits:**

- Automatic regression detection
- No external dependencies
- Simple file-based storage
- Alerts when prompts degrade

## Quick Win #4: One-Command Prompt Testing (15 minutes)

### Implementation

Create a simple script that can test any prompt in your system.

```bash
# bin/test-prompt.sh
#!/bin/bash

WORKFLOW_NAME=$1
PROMPT_NAME=$2
TEST_INPUT=$3

if [ -z "$WORKFLOW_NAME" ] || [ -z "$PROMPT_NAME" ] || [ -z "$TEST_INPUT" ]; then
    echo "Usage: ./bin/test-prompt.sh <workflow-name> <prompt-name> <test-input>"
    echo "Example: ./bin/test-prompt.sh content-utils/archetype-detector writingStylePrompt '{\"content\":\"test content\"}'"
    exit 1
fi

echo "Testing prompt: $PROMPT_NAME in workflow: $WORKFLOW_NAME"
echo "Input: $TEST_INPUT"
echo "---"

# Run the prompt evaluation workflow
yarn run-workflow promptQualityTester "{
  \"workflowName\": \"$WORKFLOW_NAME\",
  \"promptName\": \"$PROMPT_NAME\",
  \"testInput\": $TEST_INPUT
}"
```

```typescript
// packages/temporal/src/workflows/eval/prompt-tester/workflow.ts
import { handleWorkflowError, validateWorkflowInput } from '@flow/sdk/lib/workflow-utils.js';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type * as activities from './activities.js';
import type { PromptTestInput, PromptTestOutput } from './types.js';
import { PromptTestInputSchema } from './types.js';

const { testPromptQuality } = WorkflowScope.use<typeof activities>(import.meta.url);

export async function promptQualityTester(rawInput: PromptTestInput): Promise<PromptTestOutput> {
  const input = validateWorkflowInput(rawInput, PromptTestInputSchema);

  try {
    return await testPromptQuality(input);
  } catch (error) {
    handleWorkflowError(error);
  }
}
```

### Usage

```bash
# Test any prompt from command line
./bin/test-prompt.sh content-utils/archetype-detector writingStylePrompt '{"content":"Sample blog post content here"}'

# Output:
# Testing prompt: writingStylePrompt in workflow: content-utils/archetype-detector
# Input: {"content":"Sample blog post content here"}
# ---
# Quality Score: 8/10 (good)
# Issues: Minor formatting inconsistencies
# Quick Fix: Add more specific formatting instructions
```

**Benefits:**

- Instant prompt testing from command line
- No workflow changes needed
- Can test any prompt in your system
- Fast feedback loop for prompt development

## Quick Win #5: Prompt Health Dashboard (1 hour)

### Implementation

Simple HTML dashboard that shows prompt performance overview.

```typescript
// packages/temporal/src/workflows/eval/prompt-dashboard/activities.ts
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';

const { logger } = workerModule;

export async function generatePromptHealthReport(): Promise<string> {
  const reports = await loadAllPromptReports();

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Prompt Health Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .prompt-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .score-excellent { background-color: #d4edda; }
            .score-good { background-color: #fff3cd; }
            .score-fair { background-color: #f8d7da; }
            .score-poor { background-color: #f5c6cb; }
        </style>
    </head>
    <body>
        <h1>Prompt Health Dashboard</h1>
        <p>Generated: ${new Date().toLocaleString()}</p>
        
        ${reports
          .map(
            (report) => `
            <div class="prompt-card score-${report.category}">
                <h3>${report.workflowName}/${report.promptName}</h3>
                <p><strong>Average Score:</strong> ${report.averageScore}/10</p>
                <p><strong>Samples:</strong> ${report.sampleCount}</p>
                <p><strong>Last Updated:</strong> ${report.lastUpdated}</p>
                ${report.topIssue ? `<p><strong>Top Issue:</strong> ${report.topIssue}</p>` : ''}
            </div>
        `
          )
          .join('')}
    </body>
    </html>
  `;

  // Save dashboard to file
  const fs = await import('fs/promises');
  await fs.writeFile('/tmp/prompt-health-dashboard.html', html);

  console.log('📊 Prompt health dashboard generated: /tmp/prompt-health-dashboard.html');
  return html;
}

async function loadAllPromptReports(): Promise<any[]> {
  try {
    const fs = await import('fs/promises');
    const files = await fs.readdir('/tmp/prompt_baselines');

    const reports = await Promise.all(
      files.map(async (file) => {
        const data = await fs.readFile(`/tmp/prompt_baselines/${file}`, 'utf-8');
        const baseline = JSON.parse(data);
        const [workflowName, promptName] = file.replace('.json', '').split(':').slice(1);

        return {
          workflowName,
          promptName,
          averageScore: baseline.averageScore.toFixed(1),
          sampleCount: baseline.sampleCount,
          lastUpdated: new Date(baseline.lastUpdated).toLocaleString(),
          category:
            baseline.averageScore >= 9
              ? 'excellent'
              : baseline.averageScore >= 7
                ? 'good'
                : baseline.averageScore >= 5
                  ? 'fair'
                  : 'poor',
        };
      })
    );

    return reports.sort((a, b) => b.averageScore - a.averageScore);
  } catch {
    return [];
  }
}

export default WorkflowScope.register(import.meta.url, {
  generatePromptHealthReport,
});
```

### Usage

```bash
# Generate dashboard
yarn run-workflow promptHealthDashboard '{}'

# Open in browser
open /tmp/prompt-health-dashboard.html
```

**Benefits:**

- Visual overview of all prompt performance
- No external dependencies
- Simple HTML file output
- Easy to share with team

## Implementation Timeline

### Week 1: Core Infrastructure (3 hours total)

- ✅ **Day 1:** Implement Quick Win #1 (Prompt Quality Scorer) - 1-2 hours
- ✅ **Day 2:** Implement Quick Win #2 (Performance Logger) - 30 minutes
- ✅ **Day 3:** Implement Quick Win #3 (Regression Detection) - 45 minutes
- ✅ **Day 4:** Implement Quick Win #4 (One-Command Testing) - 15 minutes
- ✅ **Day 5:** Implement Quick Win #5 (Health Dashboard) - 1 hour

### Week 2: Integration & Testing (1 hour total)

- ✅ **Day 1:** Add quality scoring to 3 high-priority workflows - 30 minutes
- ✅ **Day 2:** Test regression detection with sample data - 15 minutes
- ✅ **Day 3:** Generate first health dashboard - 15 minutes

### Week 3: Monitoring & Optimization (30 minutes total)

- ✅ **Day 1:** Review collected data and identify patterns - 15 minutes
- ✅ **Day 2:** Optimize low-performing prompts - 15 minutes

## Success Metrics

### Immediate (Week 1)

- [ ] Can score any prompt output in under 2 seconds
- [ ] Performance data is automatically logged
- [ ] Regression detection is working for test prompts
- [ ] Command-line testing is functional
- [ ] Health dashboard generates successfully

### Short-term (Month 1)

- [ ] 10+ workflows have quality scoring integrated
- [ ] Regression detection catches 1+ real performance drops
- [ ] Health dashboard shows trends over time
- [ ] Team uses command-line testing regularly

### Medium-term (Month 3)

- [ ] Baseline performance established for all critical prompts
- [ ] 5+ prompt optimizations based on scoring data
- [ ] Automated alerts for significant regressions
- [ ] Dashboard shows clear ROI from improvements

## Risk Mitigation

### Technical Risks

- **File system storage limitations** → Start simple, upgrade to Redis later
- **Performance impact** → Use cheap models (gpt-4o-mini) for scoring
- **Integration complexity** → Additive changes only, no workflow modifications

### Operational Risks

- **Team adoption** → Start with command-line tools, add to workflows gradually
- **Data quality** → Begin with simple metrics, expand based on learnings
- **Resource usage** → Monitor costs, optimize model usage

## Next Steps After Quick Wins

### Natural Extensions

1. **Upgrade storage** → Move from file system to Redis
2. **Add Slack alerts** → Notify team of regressions
3. **Expand metrics** → Add hallucination detection
4. **A/B testing** → Compare prompt variants automatically

### Integration Opportunities

1. **CI/CD integration** → Run prompt tests on deployment
2. **Agentic optimization** → Automatically improve low-scoring prompts
3. **Brand alignment** → Use existing brand context workflows
4. **Production monitoring** → Real-time quality tracking

This quick wins approach provides immediate value while building the foundation for more sophisticated prompt evaluation capabilities. Each implementation is designed to be minimal, safe, and provide actionable insights from day one.
