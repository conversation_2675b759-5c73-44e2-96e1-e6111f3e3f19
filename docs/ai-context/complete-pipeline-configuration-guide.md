# Complete Atlas Pipeline Configuration Guide for Flow App

## 🚨 **CRITICAL: STRICT YAML PARSING RULES**

### **DO NOT INVENT YAML FEATURES**

⚠️ **WARNING**: Atlas pipeline YAML parsing is **extremely strict**. Any deviation from documented syntax will cause pipeline failures.

#### **PROHIBITED ACTIONS**

- ❌ **NEVER** invent new YAML expressions or syntax
- ❌ **NEVER** use JavaScript-style expressions like `${Date.now()}`
- ❌ **NEVER** create custom interpolation patterns
- ❌ **NEVER** use undefined liquid filters
- ❌ **NEVER** assume features exist without verification

#### **REQUIRED VALIDATION**

- ✅ **ALWAYS** verify syntax against working examples
- ✅ **ALWAYS** test pipelines in development before production
- ✅ **ALWAYS** use only documented interpolation patterns
- ✅ **ALWAYS** check that workflow functions exist in the system
- ✅ **ALWAYS** validate liquid filter syntax

#### **SAFE PATTERNS ONLY**

```yaml
# ✅ CORRECT - Documented interpolation
name: ${pipeline.input.vendor_name}
category: ${steps.research.output.category}
content: ${pipeline.env.company_context}

# ❌ WRONG - Invented syntax
publishedAt: ${Date.now()}
customField: ${pipeline.input.name.toUpperCase()}
```

---

## 📋 **Documentation Update Instructions**

### How to Keep This Guide Current

This guide combines pipeline configuration documentation with liquid filters. To update:

1. **Source Files to Monitor**:
   - `/Users/<USER>/growthx/atlas/docs/ai-context/task-pipeline-configuration.md` - Core pipeline syntax
   - `/Users/<USER>/growthx/atlas/doc/ai-context/yaml-pipelines-liquid-filters.md` - Liquid filters
   - `/Users/<USER>/growthx/atlas/docs/pipeline-examples/` - Working examples

2. **Update Process**:

   ```bash
   # From Atlas project root
   cd /Users/<USER>/growthx/atlas

   # Check for changes in source files
   git log --oneline --since="1 week ago" -- docs/ai-context/task-pipeline-configuration.md
   git log --oneline --since="1 week ago" -- doc/ai-context/yaml-pipelines-liquid-filters.md
   git log --oneline --since="1 week ago" -- docs/pipeline-examples/
   ```

3. **When to Update**:
   - New pipeline features are added
   - New liquid filters are implemented
   - Pipeline syntax changes
   - New workflow functions are available
   - New UI types are supported

---

## Foundation Context (Tier 1)

### System Overview

Task pipelines in the Atlas platform are defined using YAML configuration files that specify automated sequences of AI-powered workflows. This documentation serves as the authoritative reference for pipeline configuration, validation, and integration patterns within the Rails 8.0 + React + TypeScript architecture.

### Technology Stack Integration

- **Backend**: Rails 8.0 with Temporal workflow orchestration
- **Frontend**: React 18 + TypeScript + Inertia.js for pipeline management UI
- **Storage**: PostgreSQL for pipeline metadata, file system for YAML configurations
- **Security**: Pundit authorization for pipeline access control
- **Filtering**: Liquid template engine v5.5 for data transformation

### Core Architecture Principles

Task pipelines follow a declarative configuration approach where each pipeline includes:

- Input/output schemas with type validation
- Environment variables for configuration management
- Step definitions with explicit data flow dependencies
- Human review integration points for quality control
- **Liquid filters for inline data transformation** (STRICT syntax only)

## Component Context (Tier 2)

### Pipeline Configuration System

#### File Location & Management

- **Location**: `config/pipelines/*.yml` (Atlas) / `packages/temporal/src/workflows/clients/*/` (Flow)
- **Naming**: Snake_case with descriptive suffixes (e.g., `content_generation_pipeline.yml`)
- **Validation**: Automatic validation on file load via `TaskPipelineValidator` service
- **Version Control**: All pipeline files are tracked in git with proper change review

#### Integration Points

- **Backend**: `TaskPipeline` model with ActiveRecord integration
- **Frontend**: `TaskPipelineForm` React component for editing
- **Execution**: Temporal workflow orchestration via `PipelineExecutionWorkflow`
- **Monitoring**: Built-in execution tracking and error reporting
- **Filtering**: `StepInterpolationResolver` service with liquid filter processing

## Core Structure

### Root Level Properties

```yaml
name: 'Pipeline Name'
environment:
  key: value
input_schema:
  # Input field definitions
output_schema:
  # Output field definitions
steps:
  # Step definitions
```

#### Required Properties

- `name`: String - Human-readable name for the pipeline
- `input_schema`: Object - Defines expected input fields and their types
- `output_schema`: Object - Defines output fields and their sources
- `steps`: Array - List of step definitions

#### Optional Properties

- `environment`: Object - Static environment variables available to all steps

## Environment Variables

Environment variables are static values available throughout the pipeline execution:

```yaml
environment:
  company_name: 'ExampleCorp'
  target_audience: 'B2B SaaS companies'
  full_context: '## Company Context: ${artifacts.company_context} ## Personas: ${artifacts.audience_personas}'
  max_length: 200
  processing_mode: enhanced
```

Environment variables can:

- Be static strings or numbers
- Reference artifacts using `${artifacts.artifact_name}` syntax
- Be interpolated in step inputs as `${pipeline.env.variable_name}`
- **Be used in liquid filters**: `${pipeline.env.company_name | upcase}` (validated syntax only)

## Liquid Filters - Complete Guide

### 🚨 **CRITICAL: VALIDATED SYNTAX ONLY**

⚠️ **WARNING**: Only use documented liquid filters. Undocumented filters will cause pipeline failures.

### What Are Liquid Filters?

Liquid filters enhance the existing interpolation syntax to support data transformation:

**Before (Basic Interpolation)**:

```yaml
name: ${pipeline.input.title}
```

**After (With Filters)**:

```yaml
name: ${pipeline.input.title | upcase}
```

### Key Benefits

1. **Data Transformation**: Transform data inline without requiring additional workflow steps
2. **Backward Compatibility**: All existing interpolations continue to work unchanged
3. **Chainable Operations**: Multiple filters can be chained together
4. **Rich Filter Library**: Access to Liquid's standard filters plus custom filters
5. **Output Schema Support**: Filters can now be used in output schema default values

### Filter Syntax Structure

```yaml
# Basic interpolation (unchanged)
${expression}

# Single filter
${expression | filter}

# Multiple filters (chained)
${expression | filter1 | filter2: 'argument'}

# Filters with arguments
${expression | filter: 'arg1', 'arg2'}
```

### 🔒 **VERIFIED FILTER CATEGORIES**

#### 1. String Manipulation Filters

```yaml
# Case transformation
name: ${pipeline.input.title | upcase}
email: ${pipeline.input.email | downcase}
title: ${pipeline.input.text | capitalize}

# String modification
clean_text: ${pipeline.input.content | strip}
no_dashes: ${pipeline.input.code | remove: '-'}
with_suffix: ${pipeline.input.name | append: '_processed'}
with_prefix: ${pipeline.input.name | prepend: 'processed_'}

# String replacement
updated: ${pipeline.input.text | replace: 'old', 'new'}

# String truncation
summary: ${pipeline.input.description | truncate: 100}
```

#### 2. Array Operations

```yaml
# Array filtering
unique_tags: ${pipeline.input.tags | uniq}
joined_list: ${pipeline.input.items | join: ', '}
first_item: ${pipeline.input.array | first}
last_item: ${pipeline.input.array | last}

# Array access
domain: ${pipeline.input.url | split: '/' | at: 2}
username: ${pipeline.input.email | split: '@' | first}

# Array size
count: ${pipeline.input.items | size}
```

#### 3. Numeric Operations

```yaml
# Mathematical operations
total: ${pipeline.input.price | times: 5}
discounted: ${pipeline.input.price | minus: 10}
with_tax: ${pipeline.input.price | plus: 5.50}
rounded: ${pipeline.input.value | round: 2}

# Example chaining
final_price: ${pipeline.input.price | times: 1.5 | round: 2}
```

#### 4. Custom Filters

##### `at` Filter

Access array elements by index:

```yaml
second_item: ${pipeline.input.items | at: 1}
third_part: ${pipeline.input.url | split: '/' | at: 2}
```

##### `default` Filter

Provide fallback values for null/empty data:

```yaml
name: ${pipeline.input.nickname | default: 'Anonymous'}
config: ${pipeline.input.setting | default: 'default_value'}
```

### Filter Integration Sources

Liquid filters work with all interpolation sources:

#### Pipeline Inputs

```yaml
processed_topic: ${pipeline.input.topic | upcase | strip}
```

#### Step Outputs

```yaml
formatted_result: ${steps.research.output.content | truncate: 200}
```

#### Environment Variables

```yaml
env_upper: ${pipeline.env.company_name | upcase}
```

#### Artifacts

```yaml
guidelines: 'Use this voice: ${artifacts.brand_voice | upcase}'
```

#### **Output Schema Defaults**

```yaml
output_schema:
  summary:
    type: string
    default: ${steps.analyze.output.result | truncate: 150 | strip}
    position: 0

  formatted_title:
    type: string
    default: ${pipeline.input.title | upcase | append: ' - PROCESSED'}
    position: 1
```

### Chaining Filters

Multiple filters can be chained together, processed left to right:

```yaml
# Complex transformation chain
processed_name: ${pipeline.input.user_name | strip | upcase | remove: 'DR.' | append: ' (PROCESSED)'}

# URL processing
domain: ${pipeline.input.website | remove: 'https://' | remove: 'www.' | split: '/' | first}

# Email processing
username: ${pipeline.input.email | split: '@' | first | upcase}
```

### Mixed Interpolation with Filters

```yaml
# Multiple filtered expressions in one string
greeting: "Hello ${pipeline.input.name | upcase}, your code is ${pipeline.input.code | remove: '-'}"

# Complex mixed content
message: |
  Welcome ${pipeline.input.name | capitalize}!
  Your account ${pipeline.input.account_id | upcase} has been processed.
  Total items: ${pipeline.input.items | size}
```

## Input Schema

Defines the expected inputs for the pipeline:

```yaml
input_schema:
  topic:
    type: string
    position: 0
    required: true
    description: 'Main topic for content generation'
    display_name: 'Content Topic'

  assignment_direction:
    ui: text
    type: string
    position: 1
    default: 'Generate comprehensive content'
    description: 'Custom instructions for content structure'

  validate_relevance:
    type: boolean
    default: true
    position: 2
    description: 'Whether to validate content relevance'

  category:
    type: string
    position: 3
    enum: ['technology', 'business', 'marketing']
    description: 'Content category'

  tags:
    type: array
    items:
      type: string
    position: 4
    default: ['ruby', 'rails', 'javascript']
    description: 'Content tags'
```

### Input Field Properties

#### Required

- `type`: Data type - `string`, `number`, `integer`, `boolean`, `object`, `array`, `null`

#### Optional

- `required`: Boolean - Whether field is mandatory
- `default`: Any - Default value (can be static or interpolated with verified filters)
- `description`: String - Human-readable description
- `display_name`: String - UI display name (overrides field key)
- `position`: Integer - Display order in UI
- `ui`: String - UI component type (`string`, `text`, `rich_text`, `single_choice`, `multiple_choice`)
- `enum`: Array - For dropdown/choice selection

#### Default Value Interpolation

Input defaults can reference:

- Environment variables: `${pipeline.env.variable_name}`
- Environment variables with filters: `${pipeline.env.company_name | upcase}`
- Static values: Direct values

**Note**: Input defaults cannot reference step outputs since inputs are set before execution.

## Output Schema

Defines the outputs produced by the pipeline:

```yaml
output_schema:
  article:
    ui: rich_text
    type: string
    default: ${steps.internalLinks.output.linkedContent | strip}
    position: 3
    required: true
    description: 'Final article content'
    display_name: 'Generated Article'

  meta_title:
    type: string
    default: ${steps.seoMetaTags.output.metaTitle | upcase}
    position: 0
    required: true
    display_name: 'SEO Meta Title'

  search_volume:
    type: number
    default: ${steps.research.output.search_volume | round: 2}
    position: 1
    description: 'Monthly search volume data'

  summary_stats:
    ui: text
    type: string
    default: "Content: ${steps.generate.output.content | size} chars | Company: ${pipeline.env.company_name | upcase}"
    position: 2
    description: 'Processing statistics'
```

### Output Field Properties

#### Required

- `type`: Data type
- `default`: Must reference a step output using `${steps.stepId.output.fieldName}` (can include verified filters)

#### Optional

- `required`: Boolean - Whether field is mandatory
- `description`: String - Human-readable description
- `display_name`: String - UI display name
- `position`: Integer - Display order in UI
- `ui`: String - UI component type (`string`, `text`, `rich_text`, `image`)

#### Output Value Sources

Output defaults can reference step outputs with filters:

- `${steps.stepId.output.fieldName}` - Reference specific step output field
- `${steps.stepId.output.fieldName | filter}` - Apply single filter
- `${steps.stepId.output.fieldName | filter1 | filter2}` - Chain filters
- Mixed interpolation: `"Stats: ${steps.step1.output.count} items processed by ${pipeline.env.company_name | upcase}"`

## Steps

Steps define the workflow operations to execute:

```yaml
steps:
  - id: assignmentBrief
    function_name: seoAssignmentsCreationWorkflow
    display_name: 'Create Assignment Brief'
    inputs:
      keyword: ${pipeline.input.topic}
      direction: ${pipeline.input.assignment_direction}
      companyName: ${pipeline.env.company_name | upcase}
      validateRelevance: ${pipeline.input.validate_relevance}
      # Using verified filters in step inputs
      processedTags: ${pipeline.input.tags | uniq | join: ', '}
      cleanedContent: ${pipeline.input.content | strip | truncate: 500}
    outputs:
      brief:
        ui: rich_text
        type: string
        value: ${step.result.markdownBrief}
        description: 'Generated assignment brief'
      search_volume:
        type: number
        value: ${step.result.searchVolume}
        description: 'SEO search volume data'
      keywords:
        ui: text
        type: array
        value: ${step.result.keywords}
        items:
          type: string
    human_review: true
```

### Step Properties

#### Required

- `id`: String - Unique identifier within pipeline (alphanumeric, underscores, hyphens)
- `function_name`: String - **MUST exist in the system** - Workflow function to execute
- `inputs`: Object - Input values for the step
- `outputs`: Object - Output field definitions

#### Optional

- `display_name`: String - Human-readable name for UI
- `position`: Integer - Execution order (auto-assigned if not specified)
- `step_type_version`: String - Version of step type (defaults to "0.0.1")
- `human_review`: Boolean - Whether step requires human review before proceeding

### Step Input Values

Step inputs can reference all sources with verified liquid filters:

```yaml
inputs:
  # Pipeline inputs with filters
  topic: ${pipeline.input.topic | upcase | strip}

  # Environment variables with filters
  companyName: ${pipeline.env.company_name | upcase}

  # Previous step outputs with filters
  brief: ${steps.assignmentBrief.output.brief | truncate: 200}
  keywordList: ${steps.assignmentBrief.output.keywords | join: ', '}

  # Artifacts with filters
  context: ${artifacts.company_context | strip}

  # Static values
  maxLinks: 5
  validateRelevance: true

  # Mixed interpolation with filters
  summary: "Processing ${pipeline.input.topic | upcase} for ${pipeline.env.company_name | upcase}"
```

#### Input Value Types

- **Pipeline inputs**: `${pipeline.input.fieldName | filter}`
- **Environment variables**: `${pipeline.env.variableName | filter}`
- **Previous step outputs**: `${steps.stepId.output.fieldName | filter}`
- **Artifacts**: `${artifacts.artifactName | filter}`
- **Static values**: Direct values (strings, numbers, booleans)

### Step Output Definitions

Step outputs define how workflow results are captured:

```yaml
outputs:
  brief:
    ui: rich_text
    type: string
    value: ${step.result.markdownBrief}
    description: 'Generated assignment brief'
    required: true

  search_volume:
    type: number
    value: ${step.result.searchVolume}
    description: 'Monthly search volume data'

  keywords:
    ui: text
    type: array
    value: ${step.result.keywords}
    items:
      type: string

  metadata:
    type: object
    value: ${step.result.metadata}

  selected_image:
    ui: image
    type: string
    value: ${step.result.image_url}
    description: 'Selected image for content'
```

#### Output Field Properties

##### Required

- `value`: String - Reference to step result using `${step.result}` or `${step.result.fieldName}`
- `type`: String - Data type

##### Optional

- `ui`: String - UI component type
- `description`: String - Human-readable description
- `required`: Boolean - Whether field is mandatory
- `items`: Object - For array types, defines item structure

#### Output Value References

- `${step.result}` - Entire step result (for scalar outputs)
- `${step.result.fieldName}` - Specific field from step result
- `${step.result.nested.field}` - Nested field access

## Flow-Specific Workflow Functions

### Verified Available Functions

Based on Flow's workflow catalog, these functions are **verified to exist**:

#### Content Generation

- `deepResearcherWorkflow` - Research and analyze topics
- `seoContentTemplatedArticleWorkflow` - Generate templated articles
- `contentTransformerWorkflow` - Transform and enhance content
- `researchImprovementWorkflow` - Enhance research with targeted scraping

#### Ramp Client Workflows

- `rampVendorWorkflow` - Generate vendor overview content
- `rampVendorAlternativeWorkflow` - Generate vendor alternative content
- `rampVendorComparisonWorkflow` - Generate vendor comparison content

#### Utility Workflows

- `debugEchoWorkflow` - Echo inputs for testing and debugging
- `sanityPublisherWorkflow` - Publish content to Sanity CMS
- `contextBrandWorkflow` - Retrieve brand information

### Function Verification

⚠️ **CRITICAL**: Always verify workflow function names exist in the system:

```bash
# Check available workflows
yarn run-workflow --list

# Verify specific function
yarn run-workflow functionName --dry-run
```

## Flow-Specific Pipeline Patterns

### Ramp Client Pipeline Example

```yaml
name: Ramp Vendor Overview Generation
environment:
  company_context: 'Ramp is a corporate expense management platform'
  brand_voice: 'Professional, data-driven, business-focused'

input_schema:
  name:
    type: string
    position: 0
    required: true
    description: 'Vendor name'

  category:
    type: string
    position: 1
    required: true
    enum: ['Generative AI', 'Email marketing', 'Project management', 'CRM']
    description: 'Product category'

  website_url:
    type: string
    position: 2
    description: 'Vendor website URL'

output_schema:
  vendor_name:
    type: string
    default: ${steps.vendorContent.output.vendorName}
    position: 0
    display_name: 'Vendor Name'

  hero_title:
    type: string
    default: ${steps.vendorContent.output.heroTitle}
    position: 1
    display_name: 'Hero Title'

  overview_content:
    ui: rich_text
    type: string
    default: ${steps.vendorContent.output.overviewContent}
    position: 2
    display_name: 'Overview Content'

steps:
  - id: research
    function_name: deepResearcherWorkflow
    inputs:
      context: '${pipeline.env.company_context}'
      questions: ['What is ${pipeline.input.name}? What are its key features and benefits?']
      domainsToSearch: ['${pipeline.input.website_url}']
    outputs:
      research_data:
        ui: rich_text
        type: string
        value: ${step.result}

  - id: vendorContent
    function_name: rampVendorWorkflow
    inputs:
      template: '${artifacts.ramp_vendor_json_template}'
      researchData: ${steps.research.output.research_data}
      context: '${pipeline.env.brand_voice}'
      vendorName: ${pipeline.input.name}
    outputs:
      vendorName:
        type: string
        value: ${step.result.vendorName}
      heroTitle:
        type: string
        value: ${step.result.heroTitle}
      overviewContent:
        ui: rich_text
        type: string
        value: ${step.result.overviewContent}
    human_review: true
```

## Human Review Integration

Steps can be configured to require human review:

```yaml
- id: assignmentBrief
  function_name: seoAssignmentsCreationWorkflow
  inputs:
    keyword: ${pipeline.input.topic}
  outputs:
    brief:
      ui: rich_text
      type: string
      value: ${step.result.markdownBrief}
  human_review: true # Pauses pipeline for human review
```

When `human_review: true`:

- Pipeline execution pauses after step completion
- Human reviewer can examine and modify outputs
- Pipeline continues after approval

## Validation Rules

### Pipeline Level

- Must have `name`, `input_schema`, `output_schema`, and `steps`
- Output schema defaults must reference valid step outputs
- Environment variables must exist if referenced
- Liquid filter syntax must be verified and valid

### Step Level

- `id` must be unique within pipeline
- `function_name` **MUST exist in the system**
- Input references must be valid (previous steps, pipeline inputs, environment)
- Output references must match step result structure
- Steps can only reference outputs from previous steps
- Liquid filters must be verified and supported

### Reference Validation

- Pipeline input references: `${pipeline.input.fieldName}`
- Environment references: `${pipeline.env.variableName}`
- Step output references: `${steps.stepId.output.fieldName}`
- Artifact references: `${artifacts.artifactName}`
- **All references can include verified liquid filters**: `${reference | filter}`

## Error Handling

### Common Errors

1. **Unknown Filter**: `ArgumentError: Unknown filter: unknown_filter`
2. **Invalid Syntax**: `ArgumentError: Invalid filter syntax: empty filter`
3. **Invalid Function**: `WorkflowNotFoundError: Function 'functionName' not found`
4. **Invalid Arguments**: Liquid syntax errors for malformed filter arguments
5. **Type Mismatches**: Verify input/output type consistency
6. **Step Dependencies**: Ensure proper execution order and data flow

### 🚨 **CRITICAL ERROR PREVENTION**

#### Before Creating Any Pipeline:

1. ✅ **Verify all workflow functions exist** in the system
2. ✅ **Test all liquid filter syntax** in development
3. ✅ **Validate all interpolation patterns** against working examples
4. ✅ **Use only documented YAML features**
5. ✅ **Run catalog validation** after changes

#### Development Workflow:

```bash
# 1. Verify function exists
yarn run-workflow functionName --dry-run

# 2. Test pipeline syntax
yarn g:catalog

# 3. Validate in development
# Test with real data before production
```

## Performance Considerations

### Filter Performance

1. **Filter Complexity**: Complex filter chains add processing time
2. **Memory Usage**: Large arrays with filters may increase memory usage
3. **Caching**: Filtered results are not cached - recalculated on each resolution

### Pipeline Performance

1. **Step granularity**: Balance between too many small steps and too few large steps
2. **Data size**: Consider the size of data passed between steps
3. **Human review**: Use sparingly to avoid workflow bottlenecks
4. **Caching**: Leverage step result caching where appropriate

## Best Practices

### 🔒 **SAFETY FIRST**

1. **Validate Everything**: Always verify syntax against working examples
2. **Test Incrementally**: Add one feature at a time
3. **Use Verified Patterns**: Only use documented interpolation syntax
4. **Function Verification**: Always verify workflow functions exist
5. **Documentation**: Document any complex filter chains

### Pipeline Design

1. **Clear naming**: Use descriptive names for pipelines and steps
2. **Logical flow**: Order steps logically with clear dependencies
3. **Error handling**: Include validation and review steps where appropriate
4. **Documentation**: Add descriptions to all fields and steps
5. **Filter usage**: Use verified filters to transform data inline when appropriate

### Data Flow

1. **Minimal coupling**: Steps should be as independent as possible
2. **Clear interfaces**: Well-defined inputs and outputs
3. **Validation**: Validate data at pipeline boundaries
4. **Type safety**: Use appropriate data types throughout
5. **Filter chains**: Keep filter chains readable and maintainable

## Troubleshooting

### Common Issues

1. **Filter Not Applied**: Check filter syntax and spacing
2. **Function Not Found**: Verify workflow function exists in system
3. **Invalid YAML**: Never invent new YAML syntax
4. **Type Errors**: Ensure filters are appropriate for data types
5. **Performance Issues**: Review complex filter chains

### Debug Tips

1. **Start Simple**: Begin with basic interpolation before adding filters
2. **Verify Functions**: Always check that workflow functions exist
3. **Test Incrementally**: Add filters one at a time
4. **Use Working Examples**: Copy patterns from existing pipelines
5. **Validate Early**: Run catalog validation after every change

---

## 🚨 **FINAL SAFETY CHECKLIST**

Before deploying any pipeline:

- [ ] All workflow functions verified to exist in system
- [ ] All liquid filters tested and verified
- [ ] All interpolation syntax matches documented patterns
- [ ] No invented YAML features or syntax
- [ ] Pipeline tested in development environment
- [ ] Catalog validation passes
- [ ] Human review steps added where appropriate
- [ ] Error handling considered for all steps

**Remember**: Atlas pipeline parsing is strict. When in doubt, use simpler, verified syntax rather than attempting complex or innovative solutions.

---

This comprehensive guide covers all aspects of Atlas pipeline configuration for Flow, emphasizing safety, verification, and strict adherence to documented syntax patterns.
