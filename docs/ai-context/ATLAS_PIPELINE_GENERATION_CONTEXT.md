<!-- AUTO-GENERATED from Atlas - Do not edit manually -->
<!-- Last updated: 2025-07-08 -->

# AI Pipeline Generation Context

Based on actual Atlas pipeline output schemas from production systems.

## Real Pipeline Output Schemas

### Article Generation Pattern

Most common pattern for content generation:

```yaml
output_schema:
  type: object
  required:
    - article
    - meta_title
    - meta_description
  properties:
    meta_title:
      type: string
      default: '${steps.seoMetaTags.output.metaTitle}'
      position: 0
      display_name: 'Meta Title'
    url_path:
      type: string
      default: '${steps.seoMetaTags.output.urlPath}'
      position: 1
      display_name: 'Suggested URL Path'
    meta_description:
      type: string
      default: '${steps.seoMetaTags.output.metaDescription}'
      position: 2
      display_name: 'Meta Description'
    article:
      ui: rich_text
      type: string
      default: '${steps.internalLinks.output.linkedContent}'
      position: 3
```

### Article Refresher Pattern

For updating existing content:

```yaml
output_schema:
  type: object
  required:
    - article
  properties:
    article:
      ui: rich_text
      type: string
      default: '${steps.article_refresh.output.result}'
      position: 0
    faqs:
      ui: rich_text
      type: string
      default: '${steps.faq_generation.output.result}'
      position: 1
```

### Assignments Pattern

For creating content assignments:

```yaml
output_schema:
  type: object
  required:
    - assignment
    - short_description
  properties:
    output_keyword:
      type: string
      default: '${steps.assignment_brief.output.output_keyword}'
      position: 1
      description: 'Final target keyword identified for SEO optimization'
    search_volume:
      type: number
      default: '${steps.assignment_brief.output.search_volume}'
      position: 2
      description: 'Monthly search volume for the target keyword'
    relevance_score:
      type: string
      default: '${steps.assignment_brief.output.relevance_score}'
      position: 3
      description: 'Relevance score showing how well content aligns with business goals'
    short_description:
      ui: rich_text
      type: string
      default: '${steps.assignment_brief.output.short_description}'
      position: 4
      description: 'Brief summary of the assignment and content goals'
    assignment:
      ui: rich_text
      type: string
      default: '${steps.assignment_brief.output.brief}'
      position: 5
      description: 'Complete assignment brief with SEO strategy and content outline'
```

### Simple Article Pattern

Basic content generation:

```yaml
output_schema:
  type: object
  required:
    - article
  properties:
    article:
      ui: rich_text
      type: string
      default: '${steps.internalLinks.output.linkedContent}'
```

### Job Openings Pattern

For HR/hiring content:

```yaml
output_schema:
  type: object
  required:
    - expectations
  properties:
    expectations:
      type: string
      default: '${steps.hiringJobExpectationsWorkflow.output.content}'
    screeningQuestions:
      ui: text
      type: string
      default: '${steps.hiringJobInterviewProcessWorkflow.output.screeningQuestions}'
    takeHomeAssignment:
      ui: text
      type: string
      default: '${steps.hiringJobInterviewProcessWorkflow.output.takeHomeAssignment}'
    videoAnalysisPrompt:
      ui: text
      type: string
      default: '${steps.hiringJobInterviewProcessWorkflow.output.videoAnalysisPrompt}'
    resumeAnalysisPrompt:
      ui: text
      type: string
      default: '${steps.hiringJobInterviewProcessWorkflow.output.resumeAnalysisPrompt}'
    liveInterviewQuestions:
      ui: text
      type: string
      default: '${steps.hiringJobInterviewProcessWorkflow.output.liveInterviewQuestions}'
    applicationQuestionsAnalysisPrompt:
      ui: text
      type: string
      default: '${steps.hiringJobInterviewProcessWorkflow.output.applicationQuestionsAnalysisPrompt}'
```

### Podcast Article Pattern

For podcast content conversion:

```yaml
output_schema:
  type: object
  required:
    - article
  properties:
    article:
      type: string
      default: '${steps.linking.output.article}'
```

### P/SEO Vendor Page Pattern

For programmatic SEO pages:

```yaml
output_schema:
  type: object
  required:
    - generated_content
    - research_data
    - publishing_success
  properties:
    vendor_id:
      type: string
      default: '${steps.vendorPublishing.output.vendor_id}'
      position: 0
      description: 'Sanity CMS vendor document ID'
      display_name: 'Vendor ID'
    generated_content:
      ui: rich_text
      type: string
      default: '${steps.generateContent.output.content}'
      position: 1
      description: 'Complete vendor page content'
      display_name: 'Generated Content'
    research_data:
      ui: rich_text
      type: string
      default: '${steps.research.output.research_data}'
      position: 2
      description: 'Comprehensive research about the vendor'
      display_name: 'Research Data'
    sanity_studio_url:
      ui: text
      type: string
      default: '${steps.vendorPublishing.output.sanity_url}'
      position: 3
      description: 'Direct link to edit the vendor in Sanity Studio'
      display_name: 'Sanity Studio URL'
    publishing_success:
      type: boolean
      default: '${steps.vendorPublishing.output.success}'
      position: 4
      description: 'Whether the vendor was successfully published to Sanity CMS'
      display_name: 'Publishing Status'
```

## Common Step Output References

Based on actual production pipelines:

### Article Generation Steps

- `${steps.assignmentBrief.output.brief}` - Assignment content
- `${steps.research.output.answer}` - Research data
- `${steps.outline.output.result}` - Content outline
- `${steps.articleDraft.output.content}` - Draft article
- `${steps.factChecking.output.verifiedContent}` - Fact-checked content
- `${steps.styleAdaptation.output.content}` - Style-adapted content
- `${steps.internalLinks.output.linkedContent}` - Final article with links
- `${steps.seoMetaTags.output.metaTitle}` - SEO title
- `${steps.seoMetaTags.output.metaDescription}` - SEO description
- `${steps.seoMetaTags.output.urlPath}` - URL path

### Article Refresher Steps

- `${steps.article_refresh.output.result}` - Refreshed article
- `${steps.faq_generation.output.result}` - Generated FAQs

### Assignment Steps

- `${steps.assignment_brief.output.brief}` - Assignment brief
- `${steps.assignment_brief.output.output_keyword}` - Target keyword
- `${steps.assignment_brief.output.search_volume}` - Search volume
- `${steps.assignment_brief.output.relevance_score}` - Relevance score
- `${steps.assignment_brief.output.short_description}` - Brief description

### Podcast Steps

- `${steps.linking.output.article}` - Final podcast article

### Job Opening Steps

- `${steps.hiringJobExpectationsWorkflow.output.content}` - Job expectations
- `${steps.hiringJobInterviewProcessWorkflow.output.screeningQuestions}` - Screening questions
- `${steps.hiringJobInterviewProcessWorkflow.output.takeHomeAssignment}` - Take-home assignment
- `${steps.hiringJobInterviewProcessWorkflow.output.videoAnalysisPrompt}` - Video analysis prompt
- `${steps.hiringJobInterviewProcessWorkflow.output.resumeAnalysisPrompt}` - Resume analysis prompt
- `${steps.hiringJobInterviewProcessWorkflow.output.liveInterviewQuestions}` - Interview questions
- `${steps.hiringJobInterviewProcessWorkflow.output.applicationQuestionsAnalysisPrompt}` - Application analysis prompt

### P/SEO Steps

- `${steps.vendorPublishing.output.vendor_id}` - Vendor ID
- `${steps.generateContent.output.content}` - Generated content
- `${steps.research.output.research_data}` - Research data
- `${steps.vendorPublishing.output.sanity_url}` - Sanity Studio URL
- `${steps.vendorPublishing.output.success}` - Publishing status

## UI Types Used in Production

### Text/Content Types

- `ui: rich_text` - Rich text editor (most common for articles)
- `ui: text` - Multi-line textarea
- No ui specified - Simple text input (default)

### Selection Types

- `ui: single_choice` - Single selection from options (used for image selection)
- `ui: multi_choice` - Multiple selections from options

### Media Types

- `ui: image` - Image display/selection (used within choice options)

## Field Types Used in Production

### Basic Types

- `type: string` - Text content (most common)
- `type: number` - Numeric values (search volume, scores)
- `type: boolean` - True/false flags (publishing status)

### Complex Types

- `type: array` - Arrays of items (used for multiple selections)
- `type: object` - Structured objects with properties

### Array/Object Configuration

For arrays with object items:

```yaml
type: array
items:
  type: object
  properties:
    field1:
      type: string
    field2:
      ui: image
      type: string
```

## Common Required Fields

- `article` - Main content output
- `meta_title` - SEO title
- `meta_description` - SEO description
- `assignment` - Assignment content
- `short_description` - Brief description
- `expectations` - Job expectations
- `generated_content` - Generated content
- `research_data` - Research information
- `publishing_success` - Status flags

## Position Ordering

Fields use `position` for UI ordering:

- `position: 0` - Meta title (highest priority)
- `position: 1` - URL path or primary keyword
- `position: 2` - Meta description or search volume
- `position: 3` - Main article content
- `position: 4+` - Additional fields

## Image Selection Pattern

### Workflow Output Structure

Image search workflows typically return arrays of image objects:

```typescript
WorkflowOutput = Array<{
  topic: string;
  imageUrl: string;
  sourceUrl: string;
  height?: number;
  width?: number;
}>;
```

### Atlas Configuration for Image Selection

For single image selection from workflow results:

```yaml
outputs:
  foundImages:
    ui: single_choice
    type: array
    items:
      type: object
      properties:
        topic:
          type: string
        imageUrl:
          ui: image
          type: string
        sourceUrl:
          type: string
        height:
          type: number
        width:
          type: number
    value: ${step.result}
human_review: true
```

### Common Pitfalls

- **Don't use array indexing**: `${step.result[0]}` is not supported in Atlas interpolations
- **Outputs require `value` field**: Using `options` without `value` will cause validation errors
- **Match workflow output structure**: Ensure `properties` match the actual workflow output fields

## Step Output Value Patterns

### Image Search Steps

- `${steps.imageSearch.output.foundImages}` - Selected image object (after human review)
- `${steps.imageSearch.output.foundImages.imageUrl}` - Selected image URL
- `${steps.imageSearch.output.foundImages.topic}` - Selected image topic
