# Flow API Structure Documentation

## Overview

The Flow codebase provides a comprehensive API for managing Temporal workflows through HTTP endpoints. This system enables workflow execution, status monitoring, and result retrieval through a well-structured REST API.

## API Endpoints

### 1. Workflow Execution - `/api/workflow`

**POST** - Execute or start a workflow

**Request Body:**

```json
{
  "workflowName": "string", // Required: Name of the workflow function
  "workflowId": "string", // Optional: Custom workflow ID (UUID generated if not provided)
  "args": {}, // Required: Workflow input parameters
  "operation": "execute|start", // Optional: Default is "start"
  "taskQueue": "string" // Optional: Custom task queue (auto-determined if not provided)
}
```

**Response (operation: "start"):**

```json
{
  "workflowId": "string",
  "runId": "string",
  "status": "started",
  "workflowName": "string"
}
```

**Response (operation: "execute"):**

```json
{
  "workflowId": "string",
  "runId": "string",
  "status": "completed",
  "workflowName": "string",
  "result": {} // Workflow output
}
```

**Example Request:**

```json
{
  "workflowName": "contextCompanyWorkflow",
  "args": {
    "rawInfo": "rawInfo",
    "website": "julius.ai",
    "companyName": "Julius"
  },
  "operation": "execute"
}
```

### 2. Workflow Status Check - `/api/workflows/[workflowId]`

**GET** - Get workflow status and information

**Response:**

```json
{
  "workflowId": "string",
  "runId": "string",
  "status": "string", // Temporal workflow status
  "historyUrl": "string" // URL to get workflow history
}
```

**DELETE** - Cancel or terminate a workflow

**Query Parameters:**

- `action`: "cancel" (default) or "terminate"
- `reason`: Reason for cancellation/termination
- `runId`: Optional specific run ID

**Response:**

```json
{
  "workflowId": "string",
  "action": "cancelled|terminated",
  "reason": "string",
  "message": "string"
}
```

### 3. Workflow Output - `/api/workflows/[workflowId]/[runId]/output`

**GET** - Retrieve workflow input and output

**Response:**

```json
{
  "workflow": {
    "input": {},                    // Original workflow input
    "output": {},                   // Workflow result
    "name": "string",
    "id": "string",
    "runId": "string",
    "startTime": "ISO string",
    "endTime": "ISO string",
    "executionTime": "ISO string",
    "status": "string",
    "taskQueue": "string",
    "historyLength": "string",
    "isRunning": boolean,
    "searchAttributes": {},
    "memo": {}
  }
}
```

### 4. Workflow Listing - `/api/workflows`

**GET** - List all workflows with optional filtering

**Query Parameters:**

- `query`: Optional search query (URL encoded)

**Response:**

```json
{
  "workflows": [
    {
      "name": "string",
      "id": "string",
      "runId": "string",
      "startTime": "ISO string",
      "endTime": "ISO string",
      "executionTime": "ISO string",
      "status": "string",
      "taskQueue": "string",
      "historyLength": "string",
      "isRunning": boolean,
      "searchAttributes": {},
      "memo": {}
    }
  ],
  "nextPageToken": "string",
  "error": "string"
}
```

### 5. Workflow Catalog - `/api/workflows-catalog`

**GET** - Retrieve available workflows and their schemas

**Response:**

```json
{
  "catalog": [
    {
      "path": "string",
      "functionName": "string",
      "displayName": "string",
      "overview": "string",
      "inputSchema": {}, // JSON Schema for workflow input
      "outputSchema": {} // JSON Schema for workflow output
    }
  ]
}
```

### 6. Workflow History - `/api/workflows/[workflowId]/[runId]/history`

**GET** - Get detailed workflow execution history

## Workflow Input/Output Structure

### Input Validation

All workflows use Zod schemas for input validation. The input structure varies by workflow but follows this pattern:

```typescript
// Example: contextCompanyWorkflow
const WorkflowInputSchema = z.object({
  companyName: z.string(),
  website: z.string(),
  rawInfo: z.string().optional().nullable(),
});
```

### Output Structure

Workflow outputs are strongly typed and vary by workflow functionality:

```typescript
// Example: contextCompanyWorkflow output
type WorkflowOutput = {
  companyName: string;
  website: string;
  elevatorPitch: string;
  executiveSummary: string;
  overview: string;
  products: string;
  personas: string;
  jobsToBeDone: string;
  competitors: string;
  aiContext?: string;
  searchQueries?: {
    personaQueries: { context: string; queries: string[] }[];
    jtbdQueries: { context: string; queries: string[] }[];
  };
  markdown: string;
};
```

## Error Handling

### HTTP Status Codes

- **200**: Success
- **202**: Workflow started (async operation)
- **400**: Bad request (invalid input, missing required fields)
- **404**: Workflow not found
- **500**: Internal server error

### Error Response Format

```json
{
  "error": "string", // Error message
  "workflowId": "string" // Optional: workflow ID if applicable
}
```

### Common Error Types

- `workflowName is required`: Missing workflow name in request
- `Workflow not found`: Invalid workflow ID or workflow doesn't exist
- `Invalid input`: Input validation failed against workflow schema
- `BAD_REQUEST`: Temporal-specific validation errors

## Task Queue Management

The system automatically determines task queues based on workflow paths:

- **Default**: `TASK_QUEUE_DEFAULT` for most workflows
- **Internal**: `TASK_QUEUE_INTERNAL` for hiring-related workflows

## Authentication & Rate Limiting

The API currently runs with:

- **Maximum Duration**: 600 seconds (10 minutes) for long-running workflows
- **Keep-Alive**: Configured for long-running requests
- **Connection**: Persistent connections supported

## Usage Examples

### Starting a Workflow Asynchronously

```bash
curl -X POST http://localhost:3000/api/workflow \
  -H "Content-Type: application/json" \
  -d '{
    "workflowName": "contextCompanyWorkflow",
    "args": {
      "companyName": "Julius",
      "website": "julius.ai",
      "rawInfo": "AI-powered platform"
    }
  }'
```

### Executing a Workflow Synchronously

```bash
curl -X POST http://localhost:3000/api/workflow \
  -H "Content-Type: application/json" \
  -d '{
    "workflowName": "contextCompanyWorkflow",
    "args": {
      "companyName": "Julius",
      "website": "julius.ai",
      "rawInfo": "AI-powered platform"
    },
    "operation": "execute"
  }'
```

### Checking Workflow Status

```bash
curl -X GET http://localhost:3000/api/workflows/{workflowId}
```

### Getting Workflow Results

```bash
curl -X GET http://localhost:3000/api/workflows/{workflowId}/{runId}/output
```

### Canceling a Workflow

```bash
curl -X DELETE "http://localhost:3000/api/workflows/{workflowId}?action=cancel&reason=User%20requested"
```

## Integration Notes

### Temporal Integration

- Built on Temporal.io for workflow orchestration
- Supports workflow signals and queries
- Handles retries and failure scenarios automatically
- Provides complete audit trail through workflow history

### Client Libraries

The system provides TypeScript client libraries for easy integration:

- `@flow/sdk/lib/worker-module.js` - Core worker module
- `@flow/sdk/lib/workflow-client.js` - Workflow client utilities
- `@flow/sdk/lib/workflow-event-history.js` - Event history parsing

### Development Tools

- `yarn g:workflow-doc` - Generate workflow documentation
- `yarn g:catalog` - Generate workflow catalog
- Comprehensive test suite with examples in `__tests__/` directories

This API structure provides a robust foundation for building workflow-driven applications with comprehensive monitoring, error handling, and integration capabilities.
