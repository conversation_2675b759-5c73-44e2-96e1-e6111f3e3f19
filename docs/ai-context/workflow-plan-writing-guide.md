# Workflow Plan Writing Guide

_This file is generated and provides comprehensive guidance for writing effective workflow plans._

## Purpose of Workflow Plans

Workflow plans serve as detailed blueprints that:

1. **Guide Implementation**: Provide clear step-by-step instructions for developers
2. **Prevent Mistakes**: Specify exact patterns, imports, and conventions to follow
3. **Ensure Consistency**: Maintain code quality and architectural patterns
4. **Document Decisions**: Record technical choices and reasoning
5. **Enable Review**: Allow stakeholders to validate approach before implementation

## Plan Structure Template

### 1. Overview Section

```markdown
## Overview

- **Purpose**: What problem does this workflow solve?
- **Use Case**: When and why would this workflow be used?
- **Key Features**: 3-5 bullet points of main capabilities
```

### 2. Technical Specifications

#### Input/Output Schemas

````markdown
## Input Schema (WorkflowInput)

```typescript
export const WorkflowInputSchema = z.object({
  // Specify exact field names, types, and validation rules
  field1: z.string().min(1),
  field2: z.boolean().optional(),
  field3: z.array(z.string()).optional(),
});
```
````

## Output Schema (WorkflowOutput)

```typescript
export type WorkflowOutput = {
  // Specify exact return structure
  result: string;
  metadata: {
    processedAt: string;
    status: 'success' | 'partial';
  };
};
```

#### Activity Schemas

````markdown
## Activity Schemas

For each activity that uses OpenAI/LLM services:

```typescript
export const ActivityResultSchema = z.object({
  field1: z.string(),
  field2: z.array(z.string()),
});
```
````

### 3. Activity Definitions

```markdown
## Activities

### Activity 1: `activityName`

- **Purpose**: What this activity does
- **Input**: Specific input parameters
- **Output**: Exact output format
- **External Services**: OpenAI model, API endpoints
- **Error Handling**: Specific failure scenarios
- **Implementation Notes**:
  - Use `workerModule.openaiChatService`
  - Model: `gpt-4o` with `strict: true`
  - Schema: `ActivityResultSchema`

### Activity 2: `secondActivity`

- **Purpose**:
- **Dependencies**: Requires output from Activity 1
- **Processing Logic**: Step-by-step transformation
```

### 4. Prompt Engineering Specifications

````markdown
## Prompt Templates

### Prompt 1: `primaryPrompt`

```typescript
export const primaryPrompt = [
  {
    role: 'system',
    content: `
      ## Role
      You are [specific role definition]

      ## Task
      [Specific task description]

      ## Output Format
      [Exact format requirements]
    `,
  },
  {
    role: 'user',
    content: `
      <input>
      {{inputVariable}}
      </input>

      Context: {{contextVariable}}
    `,
  },
];
```
````

**Template Variables:**

- `{{inputVariable}}`: Description and example
- `{{contextVariable}}`: Description and example

````

### 5. Workflow Orchestration

```markdown
## Workflow Logic

### Step-by-Step Flow
1. **Input Validation**: `validateWorkflowInput(rawInput, WorkflowInputSchema)`
2. **Activity 1**: Call `activityName(input.field1)`
3. **Conditional Logic**: If condition X, then...
4. **Activity 2**: Process results from step 2
5. **Output Assembly**: Construct final result

### Error Handling Strategy
- **Input Validation Errors**: Use `INVALID_INPUT` type
- **Activity Failures**: Specify retry vs non-retry scenarios
- **Timeout Handling**: Specify timeout values

### Retry Configuration
```typescript
const activityOptions = {
  startToCloseTimeout: '5 minutes',
  retry: {
    initialInterval: '1 second',
    maximumInterval: '30 seconds',
    backoffCoefficient: 2,
    maximumAttempts: 3,
    nonRetryableErrorTypes: ['INVALID_INPUT', 'AUTHENTICATION_ERROR']
  }
};
````

### 6. Implementation Checklist

```markdown
## File-by-File Implementation Guide

### types.ts

- [ ] Import `z` from 'zod'
- [ ] Define `WorkflowInputSchema` with exact validation rules
- [ ] Export `WorkflowInput` type using `z.infer<typeof WorkflowInputSchema>`
- [ ] Define `WorkflowOutput` type (not schema, just TypeScript type)
- [ ] Create activity-specific schemas for OpenAI responses
- [ ] Export all activity result types

### prompts.ts

- [ ] Export const arrays for each prompt
- [ ] Use system/user role structure
- [ ] Include all required template variables
- [ ] Specify exact output format in system message
- [ ] Use semantic tags for content organization

### activities.ts

- [ ] Import required services from `@flow/sdk/lib/worker-module.js`
- [ ] Import types and schemas from `./types.js`
- [ ] Import prompts from `./prompts.js`
- [ ] Implement each activity function with proper error handling
- [ ] Use `ApplicationFailure.nonRetryable()` for permanent failures
- [ ] Export default `WorkflowScope.register()` with all activities

### workflow.ts

- [ ] Import workflow utilities from `@flow/sdk/lib/workflow-utils.js`
- [ ] Import `WorkflowScope` from `@flow/sdk/lib/workflow-scope.js`
- [ ] Use `WorkflowScope.use<typeof activities>(import.meta.url)` pattern
- [ ] Implement input validation as first step
- [ ] Use try/catch with `handleWorkflowError(error)`
- [ ] Return properly typed `WorkflowOutput`
```

### 7. Testing Strategy

````markdown
## Testing Plan

### Test Cases

1. **Happy Path**: Normal successful execution
   - Input: `{"field1": "value", "field2": true}`
   - Expected Output: Complete success response

2. **Edge Cases**:
   - Empty/minimal input
   - Maximum input sizes
   - Optional field variations

3. **Error Scenarios**:
   - Invalid input format
   - OpenAI API failures
   - Timeout conditions

### Test Commands

```bash
# Start worker first
yarn start:worker

# Test workflow
yarn run-workflow debugQuotesWorkflow '{"topic": "motivation", "includeHourlyQuote": true, "currentTime": "2024-01-15T09:30:00Z"}'
```
````

### Performance Expectations

- **Execution Time**: Expected duration
- **Token Usage**: Estimated OpenAI token consumption
- **Memory Usage**: Expected memory footprint

````

## Quality Criteria for Plans

### Completeness Checklist
- [ ] All input fields clearly defined with validation rules
- [ ] All output fields specified with exact types
- [ ] Every activity has clear purpose and implementation details
- [ ] All external service integrations specified (models, endpoints)
- [ ] Error handling strategy documented
- [ ] Prompt templates include all required variables
- [ ] Implementation checklist covers all files
- [ ] Testing strategy includes multiple scenarios

### Specificity Requirements
- **Avoid Generic Terms**: Instead of "process data", specify "extract quote and author from GPT-4 response"
- **Include Exact Values**: Specify exact model names (`gpt-4o`), timeout values (`5 minutes`), etc.
- **Define All Variables**: Every `{{variable}}` in prompts must be documented
- **Specify Dependencies**: Clear activity dependency chain

### Technical Accuracy
- **Import Paths**: Use correct `@flow/sdk/` paths
- **Service Names**: Use exact service names from `workerModule`
- **Schema Types**: Ensure Zod schemas match OpenAI requirements (must be objects)
- **Error Types**: Use standard error type names

## Anti-Patterns to Avoid

### Vague Descriptions
❌ "Create a prompt that generates quotes"
✅ "Create `dailyQuotePrompt` that uses GPT-4 to generate inspirational quotes with author attribution, considering time of day context"

### Missing Implementation Details
❌ "Use OpenAI to generate content"
✅ "Use `workerModule.openaiChatService` with model `gpt-4o`, `strict: true`, and `DailyQuoteSchema` for structured output"

### Incomplete Error Handling
❌ "Handle errors appropriately"
✅ "Use `ApplicationFailure.nonRetryable()` for invalid input, allow retries for OpenAI rate limits, timeout after 5 minutes"

### Generic Testing
❌ "Test the workflow"
✅ "Test with morning/afternoon/evening timestamps, verify schema validation, test with and without optional hourlyQuote"

## Example High-Quality Plan Section

```markdown
### Activity: `generateDailyQuote`

**Purpose**: Generate an inspirational quote of the day using GPT-4, considering current time context for relevance.

**Input Parameters**:
- `topic: string` - Theme for the quote (e.g., "motivation", "success", "creativity")
- `currentTime: string` - ISO timestamp for time-aware generation
- `timeOfDay: 'morning' | 'afternoon' | 'evening'` - Derived from currentTime

**Implementation**:
```typescript
export async function generateDailyQuote(input: {
  topic: string;
  currentTime: string;
  timeOfDay: string;
}): Promise<DailyQuote> {
  const messages = await threadTemplate(dailyQuotePrompt, {
    topic: input.topic,
    currentTime: input.currentTime,
    timeOfDay: input.timeOfDay,
  });

  return workerModule.openaiChatService.request<DailyQuote>({
    messages,
    model: 'gpt-4o',
    strict: true,
    schema: DailyQuoteSchema,
    schemaName: 'daily_quote_schema',
  });
}
````

**Schema**:

```typescript
export const DailyQuoteSchema = z.object({
  quote: z.string().min(10).max(500),
  author: z.string().min(2).max(100),
  context: z.string().min(10).max(200),
  timeRelevance: z.string().min(5).max(100),
});
```

**Error Handling**:

- OpenAI API failures: Allow 3 retries with exponential backoff
- Schema validation failures: Throw `ApplicationFailure.nonRetryable()`
- Rate limit errors: Allow retries with extended intervals

```

This level of detail ensures successful implementation on the first attempt.
```
