# Workflow Testing Scripts - AI Context

_This file is generated and provides comprehensive guidance for using workflow testing tools._

## Overview

The Flow codebase includes sophisticated workflow testing and debugging tools that are essential for developing and maintaining Temporal workflows. These tools provide real-time monitoring, error detection, and production payload testing capabilities.

**Key Features:**

- **Real-time Activity Monitoring**: Shows activity failures, retry attempts, and error messages (dev environment)
- **Configurable Timeouts**: Use `--max-timeout <seconds>` for precise control (default: 60s)
- **Environment-Aware**: Rich debugging in development, secure monitoring in production
- **Temporal UI Integration**: Queries local Temporal UI for real-time workflow status in dev mode
- **Production Payload Testing**: Download and test with real production data

## Quick Start Guide

### For Production Debugging

```bash
# Download failed workflow payload from production
yarn run-workflow download <workflowId> --env prod

# Run locally with full debugging (timeout in seconds)
yarn run-workflow <workflowName> --async --max-timeout 60
```

### For Development Testing

```bash
# Quick test with manual payload
yarn run-workflow contextBrandWorkflow '{"domain": "example.com"}'

# Fast debug workflow for testing polling
yarn run-workflow debugEchoWorkflow '{"message": "test", "delaySeconds": 3}' --async --max-timeout 10

# Test with file input and custom timeout
yarn run-workflow contextCompanyWorkflow --file payload.json --async --max-timeout 30
```

### For Deployment Validation

```bash
# Test in production after deployment
yarn run-workflow workflowName '{"args": "here"}' --env prod

# Test with downloaded production data
yarn run-workflow download <successfulWorkflowId> --env prod
yarn run-workflow workflowName --env prod --async --max-timeout 60
```

## Directory Structure

### Script Organization

**Location:** `bin/` (project root)

**Purpose:** Project-wide utility scripts and development tools

**Examples:**

- `run-workflow.ts` - Main workflow runner script (project-wide utility)

**Package-specific scripts:** `packages/temporal/src/scripts/tasks/`

- `test-slack-api.ts` - Slack API testing
- `test-stability-api.ts` - Stability API testing
- `test-you-api.ts` - You API testing

### File Naming Convention

- Use kebab-case: `run-workflow.ts`
- Include the purpose: `run-`, `test-`, `generate-`, `migrate-`
- Be descriptive: `test-slack-api.ts` vs `slack.ts`

## HTTP Client Standards

### Using Axios (Recommended)

**Why Axios:**

- Already available in `packages/temporal/package.json`
- Simpler error handling than fetch
- Built-in JSON parsing
- Better TypeScript support

**Import:**

```typescript
import axios, { AxiosError } from 'axios';
```

**Usage Pattern:**

```typescript
try {
  const response = await axios.post(url, data);
  return response.data;
} catch (error) {
  if (error instanceof AxiosError) {
    const errorMsg = error.response?.data || error.message;
    throw new Error(`HTTP ${error.response?.status || 'Error'}: ${JSON.stringify(errorMsg)}`);
  }
  throw error;
}
```

### SDK HTTP Client (Alternative)

**When to Use:**

- When you need advanced logging
- When working within workflow activities
- When you need consistent error handling with `ApiError`

**Usage:**

```typescript
import { HttpClient } from '@flow/sdk/lib/http-client.js';
import { Logger } from '@flow/sdk/lib/logger.js';

const httpClient = new HttpClient(new Logger());
```

## CLI Standards

### Using Commander.js

**Import:**

```typescript
import { Command } from 'commander';
```

**Available in:** `packages/temporal/package.json`

**Basic Structure:**

```typescript
const program = new Command();

program.name('script-name').description('What the script does').version('1.0.0');

program
  .argument('<required>', 'Description of required arg')
  .option('--env <environment>', 'Environment (dev|prod)', 'dev')
  .option('--flag', 'Boolean flag')
  .action(async (arg, options) => {
    // Implementation
  });

if (import.meta.url === `file://${process.argv[1]}`) {
  program.parse();
}
```

### Console Output Standards

**Use Simple Console.log:**

- No chalk dependency (not available in all packages)
- Use emojis for visual feedback
- Structured indentation for readability

**Examples:**

```typescript
console.log(`🚀 Starting workflow: ${workflowName}`);
console.log(`   Environment: ${env}`);
console.log(`   Args: ${JSON.stringify(args, null, 2)}`);

// Status updates
console.log(`✅ Success`);
console.log(`❌ Error`);
console.log(`⏳ In progress`);
console.log(`🔄 Polling...`);
```

## Workflow Testing Pattern

### Test Script Structure

```typescript
interface WorkflowTestConfig {
  baseUrl: string;
  name: string;
}

class WorkflowRunner {
  private config: WorkflowTestConfig;

  constructor(environment: 'dev' | 'prod') {
    this.config = ENVIRONMENTS[environment];
  }

  async executeWorkflow(workflowName: string, args: any, operation: 'start' | 'execute' = 'start') {
    // Implementation
  }

  async pollWorkflowStatus(workflowId: string, maxAttempts: number = 60) {
    // Implementation
  }

  async inspectError(workflowId: string, runId: string) {
    // Implementation
  }
}
```

### Environment Configuration

```typescript
const ENVIRONMENTS = {
  dev: {
    baseUrl: 'http://localhost:2000',
    name: 'Development',
  },
  prod: {
    baseUrl: 'https://run.growthx.ai',
    name: 'Production',
  },
} as const;
```

### API Endpoints

**Execute Workflow:**

- `POST /api/workflow`
- Body: `{ workflowName, args, operation }`

**Check Status:**

- `GET /api/workflows/{workflowId}`

**Get Output:**

- `GET /api/workflows/{workflowId}/{runId}/output`

**Auto-Discovery:**

- The download command first calls the status endpoint to get the runId
- Then uses the runId to fetch the workflow output and extract input payload

## Error Handling Standards

### HTTP Error Handling

```typescript
try {
  const response = await axios.post(url, data);
  return response.data;
} catch (error) {
  if (error instanceof AxiosError) {
    const errorMsg = error.response?.data || error.message;
    throw new Error(`HTTP ${error.response?.status || 'Error'}: ${JSON.stringify(errorMsg)}`);
  }
  throw error;
}
```

### Workflow Error Inspection

```typescript
async inspectError(workflowId: string, runId: string) {
  try {
    const output = await this.getWorkflowOutput(workflowId, runId);

    if (output.error) {
      console.log(`❌ Error Details:`);
      console.log(`   Message: ${output.error.message}`);
      console.log(`   Type: ${output.error.applicationFailureInfo?.type || 'Unknown'}`);

      if (output.error.stackTrace) {
        console.log(`   Stack Trace:`);
        console.log(output.error.stackTrace);
      }
    }
  } catch (error) {
    console.log(`Failed to inspect error: ${error}`);
  }
}
```

## Usage Examples

### Basic Workflow Testing

```bash
# Development environment (default: dev + sync)
yarn run-workflow contextBrandWorkflow '{"domain": "example.com"}'

# Production environment
yarn run-workflow contextCompanyWorkflow '{"website": "julius.ai", "companyName": "Julius"}' --env prod

# Using file input for large payloads
yarn run-workflow contextBrandWorkflow --file payload.json --async --max-timeout 30

# Long-running workflows (60 seconds timeout)
yarn run-workflow contextBrandWorkflow '{"domain": "example.com"}' --async --max-timeout 60

# Custom timeout for very long workflows
yarn run-workflow contextBrandWorkflow '{"domain": "example.com"}' --async --max-timeout 120

# Download payload from production workflow (saves to run-workflow-payload.json)
yarn run-workflow download workflowId --env prod
```

### Synchronous vs Asynchronous

```bash
# Synchronous (default - wait for result)
yarn run-workflow workflowName '{"arg": "value"}'

# Asynchronous (start and monitor with real-time updates)
yarn run-workflow workflowName '{"arg": "value"}' --async --max-timeout 30

# Using file input (default sync)
yarn run-workflow workflowName --file large-payload.json
```

## Timeout Options

### Timeout Management

The script supports flexible timeout configuration:

1. **Default (60 seconds)**: `--async` only
   - Best for most workflows and development testing

2. **Custom Timeouts**: `--async --max-timeout <seconds>`
   - User-defined timeout in seconds
   - Use appropriate timeout for workflow complexity

### Timeout Selection Guidelines

- **Use default (60 seconds)** for:
  - Development testing
  - Simple workflows
  - Quick validation after deployments

- **Use custom timeouts** for:
  - Complex workflows with multiple API calls
  - Workflows that process large datasets
  - Debugging production failures

## Auto-Loading Payload Files

### Default Payload File

The script automatically manages payload files for seamless workflow testing:

1. **Download**: `yarn run-workflow download workflowId --env prod`
   - Saves to `run-workflow-payload.json` by default
   - Automatically detects workflow name from history
   - Provides helpful tip for next command

2. **Run**: `yarn run-workflow workflowName`
   - Automatically loads `run-workflow-payload.json` if no args provided
   - No need to specify `--file` option
   - Fallback to manual args if no payload file exists

### File Priority

The script loads arguments in this order:

1. `--file <path>` (highest priority)
2. Inline JSON arguments
3. `run-workflow-payload.json` (auto-load)
4. Error if none found

### Usage Pattern

```bash
# Download production payload
yarn run-workflow download 78116aa5-e54b-408c-b974-b66ab3b69aa9 --env prod

# Run immediately with downloaded payload
yarn run-workflow contextCompanyWorkflow --async --max-timeout 30

# Or run with custom file
yarn run-workflow contextCompanyWorkflow --file custom-payload.json
```

## Developer Workflow Patterns

### 1. Production Issue Investigation

**Scenario**: A workflow failed in production and needs debugging

```bash
# Step 1: Download the failed workflow's payload
yarn run-workflow download 78116aa5-e54b-408c-b974-b66ab3b69aa9 --env prod

# Step 2: Run locally with full debugging
yarn run-workflow contextCompanyWorkflow --async --max-timeout 60
```

**Expected Output**:

- 📥 Downloads original production payload
- 🔍 Automatically finds runId from workflowId
- 📂 Auto-loads payload file for local testing
- 🔄 Monitors workflow status with real-time updates
- 🔍 Shows detailed error information and activity failures

### 2. Feature Development Testing

**Scenario**: Testing a new workflow during development

```bash
# Quick sync test with manual payload
yarn run-workflow newWorkflow '{"testData": "value"}'

# Async test with file input
echo '{"complex": "payload"}' > test-payload.json
yarn run-workflow newWorkflow --file test-payload.json --async --max-timeout 30
```

### 3. Deployment Validation

**Scenario**: Validating workflows after deployment

```bash
# Test critical workflows in production
yarn run-workflow contextBrandWorkflow '{"domain": "example.com"}' --env prod

# Test with production data
yarn run-workflow download <recentSuccessfulWorkflowId> --env prod
yarn run-workflow contextBrandWorkflow --env prod --async --max-timeout 60
```

## Troubleshooting Guide

### Common Issues and Solutions

1. **"No arguments provided and no payload file found"**

   ```bash
   # Solution: Download payload first or provide manual args
   yarn run-workflow download <workflowId> --env prod
   # OR
   yarn run-workflow workflowName '{"manual": "args"}'
   ```

2. **"Workflow status polling timed out"**

   ```bash
   # Solution: Use longer timeout
   yarn run-workflow workflowName '{"args": "here"}' --async --max-timeout 120
   ```

3. **"HTTP 500: Invalid URL format"**

   ```bash
   # Solution: Check URL validation in workflow types
   # This indicates Zod schema validation failure
   ```

4. **"HTTP Error: Connect ECONNREFUSED"**

   ```bash
   # Solution: Ensure development server is running
   # Check if http://localhost:2000 is accessible
   ```

5. **"Workflow not found (404) - likely server restarted"**
   ```bash
   # Solution: Development server clears workflow history on restart
   # Simply run the workflow again - this is normal behavior
   yarn run-workflow debugEchoWorkflow '{"message": "test", "delaySeconds": 2}' --async --max-timeout 10
   ```

### Debug Tips

- **Use async mode** for better error visibility and real-time monitoring
- **Check payload format** with `cat run-workflow-payload.json`
- **Verify workflow name** matches exactly (case-sensitive)
- **Test in dev first** before running in production
- **Monitor activity failures** in development mode

### Testing Workflows

#### debugEchoWorkflow - Development Testing Tool

The `debugEchoWorkflow` is specifically designed for testing the run-workflow script:

**Features:**

- Configurable delay to test monitoring behavior
- Simple echo functionality to verify input/output
- Fast execution for quick testing cycles

**Usage:**

```bash
# Quick test (1 second delay)
yarn run-workflow debugEchoWorkflow '{"message": "test"}'

# Test monitoring with custom delay
yarn run-workflow debugEchoWorkflow '{"message": "monitoring test", "delaySeconds": 10}' --async --max-timeout 15

# Test timeout behavior
yarn run-workflow debugEchoWorkflow '{"message": "long test", "delaySeconds": 70}' --async --max-timeout 80
```

**Input Schema:**

- `message` (string): Message to echo back
- `delaySeconds` (number, optional): Delay before completion (default: 1)

This workflow is perfect for:

- Testing script functionality
- Validating monitoring behavior
- Training team members on workflow testing
- Debugging timeout settings

## Best Practices

### 1. Environment Management

- **Development**: Always test locally first
- **Production**: Use for final validation and debugging
- **Payload Files**: Keep `run-workflow-payload.json` in `.gitignore`

### 2. Timeout Strategy

- **Default (60s)**: Most workflows, development testing
- **Custom**: Use appropriate timeout for workflow complexity
- **Monitor**: Use async mode to see real-time activity failures

### 3. Error Handling

- **Always use async mode** when debugging failures
- **Check both input and output** for validation errors
- **Monitor activity failures** in real-time during development

### 4. Team Collaboration

- **Share workflow IDs** for collaborative debugging
- **Use consistent payload files** for reproducible tests
- **Document workflow-specific test patterns**

### 5. Performance Optimization

- **Use sync mode** for quick validation
- **Batch similar tests** to reduce API calls
- **Cache successful payloads** for regression testing

## Dependencies

### Available in packages/temporal

- `commander` - CLI argument parsing
- `axios` - HTTP client
- `clipboardy` - Clipboard operations

### Available in packages/sdk

- `@flow/sdk/lib/http-client.js` - HTTP client with logging
- `@flow/sdk/lib/logger.js` - Structured logging
- `@flow/sdk/lib/api-error.js` - Error handling utilities

## Command Reference

### Core Commands

```bash
# Execute workflow (default: dev, sync)
yarn run-workflow <workflowName> [args]

# Enhanced debugging mode
yarn run-workflow <workflowName> [args] --async --max-timeout <seconds>

# Download workflow payload (default: prod)
yarn run-workflow download <workflowId> [runId]
```

### Options

```bash
--env <dev|prod>          # Environment (default: dev)
--file <path>             # Load args from JSON file
--async                   # Run asynchronously with monitoring
--max-timeout <seconds>   # Timeout in seconds (default: 60)
```

### Example Commands

```bash
# Basic test with 10-second timeout
yarn run-workflow workflowName '{"args": "here"}' --async --max-timeout 10

# Production testing with 60-second timeout
yarn run-workflow workflowName '{"args": "here"}' --env prod --async --max-timeout 60

# Download and test workflow
yarn run-workflow download workflow-id --env prod
yarn run-workflow workflowName --async --max-timeout 30
```

## Integration with Development Workflow

### Git Integration

Add to `.gitignore`:

```
run-workflow-payload.json
*-payload.json
```

### Worker Management

**CRITICAL**: After creating new workflows, always restart the worker:

```bash
# If using bin/dev (Overmind)
overmind restart worker

# If using yarn start:worker directly
# Stop (Ctrl+C) and restart
yarn start:worker
```

The `run-workflow` script detects this issue and provides helpful guidance.

### Documentation Updates

After workflow changes, update documentation:

```bash
# Generate workflow documentation
yarn g:workflow-doc

# Update workflow catalog
yarn g:catalog
```

This comprehensive testing infrastructure provides powerful debugging capabilities while maintaining security and performance standards.
