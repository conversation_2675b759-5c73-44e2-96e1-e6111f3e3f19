# OpenEvals Integration Patterns for Temporal Workflows

## Overview

This document outlines how to integrate OpenEvals evaluation patterns into our Temporal workflow system, adapting their TypeScript evaluation framework to work with our existing infrastructure.

## OpenEvals Analysis Summary

### Core Architecture

- **Modular design** with separate evaluation dimensions
- **Structured prompt templates** with clear rubrics
- **XML-tagged input organization** for clarity
- **Systematic scoring methodologies** (1-10 scales)
- **Type-safe TypeScript implementation**

### Key Evaluation Dimensions

#### 1. Correctness Evaluation

**Purpose:** Assess factual accuracy and completeness
**Criteria:**

- Factual accuracy of all claims
- Completeness of response
- Logical consistency
- Precise terminology usage

#### 2. Hallucination Detection

**Purpose:** Identify unsupported or fabricated information
**Criteria:**

- Claims directly supported by context
- No speculative or imagined details
- Accurate specific details (dates, numbers)
- Proper uncertainty indication

#### 3. RAG Groundedness

**Purpose:** Evaluate context alignment in retrieval-augmented generation
**Criteria:**

- Claims supported by retrieved context
- Staying within context scope
- Maintaining original meaning
- Avoiding external unsupported assertions

#### 4. Conciseness

**Purpose:** Assess response brevity and clarity
**Criteria:**

- Eliminates unnecessary information
- Maintains clarity while being brief
- Focuses on essential points
- Avoids redundancy

#### 5. Helpfulness

**Purpose:** Evaluate user satisfaction and utility
**Criteria:**

- Addresses user intent completely
- Provides actionable information
- Anticipates follow-up questions
- Offers appropriate level of detail

## Adaptation to Temporal Workflows

### 1. Prompt Template Structure

**OpenEvals Pattern:**

```typescript
export const evaluationPrompt = `
<rubric>
Assessment criteria and scoring guide
</rubric>

<instructions>
Step-by-step evaluation process
</instructions>

<reminder>
Key points to remember
</reminder>

{inputs}
{outputs}
{reference_outputs}
`;
```

**Temporal Adaptation:**

```typescript
export const correctnessEvaluationPrompt = [
  {
    role: 'system',
    content: `You are an expert prompt evaluator for content generation workflows.

    ## Scoring Rubric (1-10)
    - 9-10: Excellent - Factually accurate, complete, well-structured
    - 7-8: Good - Mostly accurate with minor gaps or formatting issues
    - 5-6: Fair - Partially accurate but missing key elements
    - 3-4: Poor - Significant inaccuracies or incomplete
    - 1-2: Very Poor - Mostly incorrect or irrelevant

    ## Evaluation Process
    1. Analyze the prompt for clarity and completeness
    2. Assess the output for factual accuracy
    3. Check alignment with expected results
    4. Identify specific issues and improvement areas
    5. Provide actionable feedback

    ## Key Criteria
    - Factual accuracy of all claims
    - Completeness of response to prompt
    - Logical flow and structure
    - Appropriate tone and style
    - Absence of contradictions or errors`,
  },
  {
    role: 'user',
    content: `
    <prompt>
    {{prompt}}
    </prompt>

    <input_data>
    {{input}}
    </input_data>

    <actual_output>
    {{output}}
    </actual_output>

    {%if expected%}
    <expected_output>
    {{expected}}
    </expected_output>
    {%endif%}

    {%if context%}
    <context>
    {{context}}
    </context>
    {%endif%}
    `,
  },
];
```

### 2. Evaluation Schema Design

**OpenEvals Pattern:**

```typescript
interface EvaluationResult {
  score: number;
  reasoning: string;
  criteria_scores: Record<string, number>;
}
```

**Temporal Adaptation:**

```typescript
export const EvaluationResultSchema = z.object({
  dimension: z.string(),
  score: z.number().min(1).max(10),
  category: z.enum(['excellent', 'good', 'fair', 'poor']),
  reasoning: z.string(),
  criteriaScores: z.object({
    accuracy: z.number().min(1).max(10),
    completeness: z.number().min(1).max(10),
    clarity: z.number().min(1).max(10),
    relevance: z.number().min(1).max(10),
  }),
  issues: z.array(z.string()),
  suggestions: z.array(z.string()),
  examples: z
    .array(
      z.object({
        issue: z.string(),
        location: z.string(),
        fix: z.string(),
      })
    )
    .optional(),
});

export type EvaluationResult = z.infer<typeof EvaluationResultSchema>;
```

### 3. Activity Implementation Patterns

**Correctness Evaluation:**

```typescript
export async function evaluateCorrectness(
  prompt: any[],
  input: Record<string, any>,
  output: string,
  expected?: string,
  context?: string
): Promise<EvaluationResult> {
  const messages = await threadTemplate(correctnessEvaluationPrompt, {
    prompt: JSON.stringify(prompt, null, 2),
    input: JSON.stringify(input, null, 2),
    output,
    expected,
    context,
  });

  return openaiChatService.request<EvaluationResult>({
    messages,
    model: 'gpt-4o',
    schema: EvaluationResultSchema,
    schemaName: 'correctness_evaluation',
  });
}
```

**Hallucination Detection:**

```typescript
export async function detectHallucinations(
  prompt: any[],
  context: string,
  output: string
): Promise<EvaluationResult> {
  const messages = await threadTemplate(hallucinationDetectionPrompt, {
    prompt: JSON.stringify(prompt, null, 2),
    context,
    output,
  });

  return anthropicChatService.request<EvaluationResult>({
    messages,
    model: 'claude-3-5-sonnet-20240620',
    schema: EvaluationResultSchema,
    schemaName: 'hallucination_detection',
  });
}
```

**RAG Groundedness:**

```typescript
export async function evaluateGroundedness(
  prompt: any[],
  retrievedContext: string,
  output: string
): Promise<EvaluationResult> {
  const messages = await threadTemplate(groundednessEvaluationPrompt, {
    prompt: JSON.stringify(prompt, null, 2),
    context: retrievedContext,
    output,
  });

  return openaiChatService.request<EvaluationResult>({
    messages,
    model: 'gpt-4o',
    schema: EvaluationResultSchema,
    schemaName: 'groundedness_evaluation',
  });
}
```

### 4. Multi-Dimensional Evaluation Workflow

```typescript
export async function comprehensivePromptEvaluation(
  input: ComprehensiveEvaluationInput
): Promise<ComprehensiveEvaluationOutput> {
  const validatedInput = validateWorkflowInput(input, ComprehensiveEvaluationInputSchema);

  try {
    // Execute the prompt to get output
    const promptExecution = await executePrompt(validatedInput.prompt, validatedInput.testInput);

    // Run evaluations in parallel
    const evaluationPromises = validatedInput.dimensions.map(async (dimension) => {
      switch (dimension) {
        case 'correctness':
          return evaluateCorrectness(
            validatedInput.prompt,
            validatedInput.testInput,
            promptExecution.output,
            validatedInput.expectedOutput,
            validatedInput.context
          );

        case 'hallucination':
          return detectHallucinations(
            validatedInput.prompt,
            validatedInput.context || '',
            promptExecution.output
          );

        case 'groundedness':
          return evaluateGroundedness(
            validatedInput.prompt,
            validatedInput.retrievedContext || '',
            promptExecution.output
          );

        case 'conciseness':
          return evaluateConciseness(
            validatedInput.prompt,
            promptExecution.output,
            validatedInput.targetLength
          );

        case 'helpfulness':
          return evaluateHelpfulness(
            validatedInput.prompt,
            validatedInput.testInput,
            promptExecution.output,
            validatedInput.userIntent
          );

        default:
          throw new Error(`Unknown evaluation dimension: ${dimension}`);
      }
    });

    const evaluationResults = await Promise.all(evaluationPromises);

    // Aggregate results
    const aggregatedScore =
      evaluationResults.reduce((sum, result) => sum + result.score, 0) / evaluationResults.length;

    return {
      overallScore: aggregatedScore,
      overallCategory: categorizeScore(aggregatedScore),
      dimensionResults: evaluationResults,
      executionTime: promptExecution.executionTime,
      outputLength: promptExecution.output.length,
      summary: await generateEvaluationSummary(evaluationResults),
    };
  } catch (error) {
    handleWorkflowError(error);
  }
}
```

### 5. Evaluation Prompt Templates

**Correctness Evaluation:**

```typescript
export const correctnessEvaluationPrompt = [
  {
    role: 'system',
    content: `You are an expert evaluator specializing in factual accuracy and completeness.

    ## Scoring Criteria (1-10)
    Evaluate based on:
    - Factual accuracy (no false claims)
    - Completeness (addresses all aspects)
    - Logical consistency (no contradictions)
    - Precise terminology (correct technical terms)

    ## Evaluation Process
    1. Identify all factual claims in the output
    2. Verify accuracy against provided context/reference
    3. Check for completeness against input requirements
    4. Assess logical flow and consistency
    5. Note any missing critical information

    ## Output Requirements
    - Provide specific scores for each criterion
    - Give detailed reasoning for the overall score
    - List specific issues found
    - Suggest concrete improvements`,
  },
  {
    role: 'user',
    content: `
    <prompt>{{prompt}}</prompt>
    <input_data>{{input}}</input_data>
    <actual_output>{{output}}</actual_output>
    {%if expected%}<expected_output>{{expected}}</expected_output>{%endif%}
    {%if context%}<context>{{context}}</context>{%endif%}
    `,
  },
];
```

**Hallucination Detection:**

```typescript
export const hallucinationDetectionPrompt = [
  {
    role: 'system',
    content: `You are an expert at detecting hallucinations and unsupported claims.

    ## Definition
    Hallucination = Any claim not directly supported by the provided context.

    ## Detection Process
    1. Thoroughly read the context
    2. Identify each claim in the output
    3. For each claim, check if it's supported by context
    4. Mark unsupported claims as hallucinations
    5. Consider severity and frequency

    ## Severity Levels
    - Critical: Factually incorrect information
    - Major: Unsupported but plausible claims
    - Minor: Speculative language without caveats
    - Acceptable: Reasonable inferences from context

    ## Scoring
    - 9-10: No hallucinations detected
    - 7-8: Minor speculative elements
    - 5-6: Some unsupported claims
    - 3-4: Multiple hallucinations
    - 1-2: Significant false information`,
  },
  {
    role: 'user',
    content: `
    <context>{{context}}</context>
    <prompt>{{prompt}}</prompt>
    <output>{{output}}</output>
    `,
  },
];
```

**RAG Groundedness:**

```typescript
export const groundednessEvaluationPrompt = [
  {
    role: 'system',
    content: `You are an expert at evaluating whether AI responses are properly grounded in retrieved context.

    ## Groundedness Criteria
    - Claims must be directly supported by retrieved context
    - Maintain original meaning and intent
    - Stay within context scope
    - Avoid external knowledge not in context

    ## Evaluation Process
    1. Compare output against retrieved context
    2. Identify specific claims and their sources
    3. Check for valid contextual inferences
    4. Note any external additions
    5. Assess overall context alignment

    ## Scoring Guidelines
    - 9-10: Fully grounded in context
    - 7-8: Mostly grounded with minor additions
    - 5-6: Partially grounded with some external info
    - 3-4: Poorly grounded with significant additions
    - 1-2: Minimally grounded, mostly external`,
  },
  {
    role: 'user',
    content: `
    <retrieved_context>{{context}}</retrieved_context>
    <prompt>{{prompt}}</prompt>
    <output>{{output}}</output>
    `,
  },
];
```

### 6. Integration with Existing Workflows

**Brand Alignment Integration:**

```typescript
export async function evaluateBrandAlignment(
  prompt: any[],
  output: string,
  companyName: string
): Promise<EvaluationResult> {
  // Use existing brand context workflow
  const brandContext = await brandContextWorkflow({ companyName });

  const messages = await threadTemplate(brandAlignmentPrompt, {
    prompt: JSON.stringify(prompt, null, 2),
    output,
    brandVoice: brandContext.voice,
    brandValues: brandContext.values,
    styleguide: brandContext.styleguide,
  });

  return openaiChatService.request<EvaluationResult>({
    messages,
    model: 'gpt-4o',
    schema: EvaluationResultSchema,
    schemaName: 'brand_alignment',
  });
}
```

**Content Quality Integration:**

```typescript
export async function evaluateContentQuality(
  prompt: any[],
  output: string,
  contentType: string
): Promise<EvaluationResult> {
  // Leverage existing content-quality-score patterns
  const messages = await threadTemplate(contentQualityPrompt, {
    prompt: JSON.stringify(prompt, null, 2),
    output,
    contentType,
  });

  return openaiChatService.request<EvaluationResult>({
    messages,
    model: 'gpt-4o',
    schema: EvaluationResultSchema,
    schemaName: 'content_quality',
  });
}
```

### 7. Testing and Validation Patterns

**Test Case Structure:**

```typescript
export const TestCaseSchema = z.object({
  name: z.string(),
  description: z.string(),
  input: z.record(z.any()),
  expectedOutput: z.string().optional(),
  context: z.string().optional(),
  retrievedContext: z.string().optional(),
  evaluationDimensions: z.array(
    z.enum([
      'correctness',
      'hallucination',
      'groundedness',
      'conciseness',
      'helpfulness',
      'brand_alignment',
    ])
  ),
  passingScore: z.number().default(7),
});
```

**Batch Evaluation:**

```typescript
export async function batchEvaluatePrompt(
  prompt: any[],
  testCases: TestCase[]
): Promise<BatchEvaluationResult> {
  const results = await Promise.all(
    testCases.map(async (testCase) => {
      const evaluation = await comprehensivePromptEvaluation({
        prompt,
        testInput: testCase.input,
        dimensions: testCase.evaluationDimensions,
        expectedOutput: testCase.expectedOutput,
        context: testCase.context,
      });

      return {
        testCase: testCase.name,
        passed: evaluation.overallScore >= testCase.passingScore,
        score: evaluation.overallScore,
        results: evaluation.dimensionResults,
      };
    })
  );

  return {
    totalTests: testCases.length,
    passedTests: results.filter((r) => r.passed).length,
    averageScore: results.reduce((sum, r) => sum + r.score, 0) / results.length,
    results,
  };
}
```

## Implementation Guidelines

### 1. Start with Core Dimensions

Begin with the most universally applicable evaluations:

- **Correctness** - For all content generation
- **Hallucination** - For factual content
- **Brand Alignment** - For client-facing content

### 2. Leverage Existing Infrastructure

- Use existing prompt template system
- Integrate with brand context workflows
- Extend content quality scoring patterns
- Utilize current logging and monitoring

### 3. Gradual Rollout

- Start with manual evaluation workflows
- Add to high-priority workflows incrementally
- Build confidence with simple metrics first
- Expand to comprehensive evaluation suites

### 4. Performance Optimization

- Use cheaper models for initial screening
- Cache evaluation results for repeated tests
- Parallel execution for multiple dimensions
- Batch processing for large test suites

This integration approach adapts OpenEvals' proven evaluation patterns to work seamlessly with your existing Temporal workflow infrastructure while maintaining the flexibility and robustness of the original framework.
