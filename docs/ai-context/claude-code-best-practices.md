# Claude Code Best Practices

> **Note:** This is a summary of the official Claude Code best practices from Anthropic.  
> For the complete and most up-to-date information, visit: https://www.anthropic.com/engineering/claude-code-best-practices

_Reference: https://www.anthropic.com/engineering/claude-code-best-practices_

## CLAUDE.md File Placement

You can place CLAUDE.md files in several locations:

- **The root of your repo**, or wherever you run claude from (the most common usage). Name it `CLAUDE.md` and check it into git so that you can share it across sessions and with your team (recommended), or name it `CLAUDE.local.md` and `.gitignore` it
- **Any parent of the directory** where you run claude. This is most useful for monorepos, where you might run claude from `root/foo`, and have `CLAUDE.md` files in both `root/CLAUDE.md` and `root/foo/CLAUDE.md`. Both of these will be pulled into context automatically
- **Any child of the directory** where you run claude. This is the inverse of the above, and in this case, <PERSON> will pull in `CLAUDE.md` files on demand when you work with files in child directories
- **Your home folder** (`~/.claude/CLAUDE.md`), which applies it to all your claude sessions

## Comprehensive Best Practices

### Setup and Customization

- Create detailed CLAUDE.md files for project-specific guidelines
- Define clear code conventions, patterns, and architectural decisions
- Document project structure, dependencies, and setup requirements
- Include specific commit message formats and PR guidelines
- Curate allowed tools to match your development workflow
- Use settings.json to control tool availability and behavior
- Install GitHub CLI for enhanced repository interactions
- Set up custom slash commands for frequently used workflows

### Tool Enhancement and Integration

- Leverage bash tools for system operations and automation
- Create custom scripts for repeated development tasks
- Integrate MCP (Model Context Protocol) servers for extended capabilities
- Store custom slash commands in `.claude/commands/` folder using Markdown format
- Use `$ARGUMENTS` placeholder for dynamic command content
- Share custom commands with team by checking into version control
- Set up development environment integrations and debugging tools

### Development Workflows

- Follow "Explore, Plan, Code, Commit" approach for systematic development
- Use test-driven development: write tests first, implement to pass, then refactor
- Implement visual design iteration with images and screenshots for UI work
- Use "Safe YOLO Mode" for well-defined tasks where Claude can work uninterrupted
- Leverage Claude for codebase Q&A and onboarding new team members
- Create comprehensive documentation for patterns and troubleshooting

### Optimization Strategies

- Provide specific instructions with clear requirements and constraints
- Include examples of expected outputs and error handling requirements
- Use visual references, screenshots, and design mockups for clarity
- Monitor Claude's work and provide feedback early to course correct
- Break complex tasks into smaller, manageable steps with checklists
- Leverage headless mode for automation, scripting, and CI/CD integration
- Track progress systematically and ensure all requirements are met

### Advanced Techniques

- Use multiple Claude instances for different specialized tasks
- Implement parallel processing of independent work streams
- Coordinate results effectively across multiple development efforts
- Use git worktrees for parallel development and testing different approaches
- Automate repetitive coding tasks and boilerplate generation
- Maintain consistency across projects through standardized patterns
- Create specialized roles for different team members and workflows

### Team Collaboration

- Share custom commands, workflows, and development patterns
- Establish and document coding standards and conventions
- Create comprehensive onboarding documentation and guides
- Conduct regular workflow reviews and optimization sessions
- Gather feedback from team members on effectiveness
- Iterate on processes and adopt new best practices continuously
- Use Claude for knowledge sharing and code review processes

### Project Management

- Maintain up-to-date CLAUDE.md files with current patterns
- Document architectural decisions and their rationale
- Include troubleshooting guides and common issue solutions
- Keep examples current and relevant to the codebase
- Create templates for common development scenarios
- Establish clear guidelines for when to use different tools
- Monitor effectiveness of workflows and adjust as needed

### Quality Assurance

- Implement proper error handling and validation patterns
- Use Claude for code review and quality improvement
- Establish testing strategies that integrate with Claude workflows
- Create validation checkpoints for complex implementations
- Document edge cases and their handling approaches
- Maintain clean working directories and organized file structures
- Use proper version control practices with Claude-assisted development

### Best Practices Summary

1. **Customize extensively** - Tailor Claude to your specific development needs and patterns
2. **Document comprehensively** - Create detailed project documentation and guidelines
3. **Iterate frequently** - Continuously improve workflows based on experience and feedback
4. **Collaborate effectively** - Share knowledge, tools, and workflows across the team
5. **Automate systematically** - Use Claude for routine coding tasks and pattern implementation
6. **Monitor and adjust** - Keep workflows optimized and effective through regular review
7. **Integrate seamlessly** - Make Claude a natural part of your development process
8. **Scale thoughtfully** - Build workflows that grow with your project and team

This comprehensive approach treats Claude as a collaborative coding partner, enabling more effective and productive development workflows across individual and team environments.
