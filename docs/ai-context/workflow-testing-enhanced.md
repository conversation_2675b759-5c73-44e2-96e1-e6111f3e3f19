# Enhanced Workflow Testing Tools - AI Context

_This file is generated and provides comprehensive guidance for using enhanced workflow testing tools._

## Overview

The Flow codebase includes sophisticated workflow testing and debugging tools that are essential for developing and maintaining Temporal workflows. These tools provide real-time monitoring, error detection, and production payload testing capabilities.

## Primary Testing Tool: run-workflow Script

**Location**: `/Users/<USER>/growthx/flow/bin/run-workflow.ts`

The `run-workflow` script is the primary tool for testing workflows locally and in production environments. It provides advanced debugging capabilities including real-time activity monitoring, timeout handling, and environment-aware features.

### Enhanced Features (Latest Update)

1. **Real-time Activity Monitoring**
   - Queries local Temporal UI for live workflow status (dev environment only)
   - Shows activity failures, retry attempts, and error messages during execution
   - Displays pending activity counts and failure details
   - Provides immediate feedback on workflow execution with actual error messages

2. **Environment-Aware Operation**
   - Development mode: Rich debugging with Temporal UI integration at `http://localhost:8233`
   - Production mode: Secure monitoring without exposing internal Temporal Cloud URLs
   - Automatic environment detection and appropriate security behavior

3. **Enhanced Timeout Management**
   - Precise timeout control in seconds (changed from minutes)
   - Individual operation timeouts using Promise.race() to prevent hanging
   - Total execution timeout with elapsed time tracking
   - No dependency on external timeout tools like gtimeout
   - Fail-fast operation that immediately detects activity failures

4. **Production Payload Testing**
   - Download real production payloads for local testing
   - Automatic payload file management and workflow reproduction
   - Safe handling of production data without exposing sensitive information

### Usage Patterns

#### Basic Workflow Testing

```bash
# Test with inline arguments
yarn run-workflow workflowName '{"field": "value"}'

# Test with payload file
yarn run-workflow workflowName --file payload.json

# Test with default payload (run-workflow-payload.json)
yarn run-workflow workflowName
```

#### Enhanced Debugging Mode (New)

```bash
# Run with real-time monitoring and fast timeout (10 seconds)
yarn run-workflow workflowName '{"args": "here"}' --async --max-timeout 10

# Environment-specific testing with proper timeouts
yarn run-workflow workflowName '{"args": "here"}' --env dev --async --max-timeout 30
yarn run-workflow workflowName '{"args": "here"}' --env prod --async --max-timeout 60
```

#### Production Payload Testing

```bash
# Download production payload
yarn run-workflow download e93f9cc2-3290-4f79-9eb5-3a31e985bf3b --env prod

# Test with downloaded payload (auto-loads run-workflow-payload.json)
yarn run-workflow workflowName --async --max-timeout 60
```

### Technical Implementation Details

#### Core Classes and Methods

**WorkflowRunner Class**:

- `executeWorkflow()`: Execute workflows with sync/async operation modes
- `pollWorkflowStatus()`: Monitor workflow execution with real-time updates
- `getTemporalUIStatus()`: Query local Temporal UI for development debugging (dev only)
- `analyzePendingActivities()`: Parse activity failures and retry information
- `analyzeWorkflowHistory()`: Extract retry counts and failed activity details
- `downloadWorkflowPayload()`: Download and save production payloads

**Key Technical Features**:

- Promise.race() timeout handling for individual operations
- Axios-based API communication with proper error handling
- Environment-specific behavior (dev vs prod)
- Comprehensive error detection and reporting

#### Error Handling and Debugging

**Timeout Management**:

- Total execution timeout with elapsed time tracking
- Individual operation timeouts (status checks, history queries)
- Graceful degradation when Temporal UI is unavailable
- Clear timeout reporting with elapsed time

**Activity Monitoring**:

- Real-time failure detection through Temporal UI queries
- Retry attempt counting and error message extraction
- Pending activity analysis with failure context
- Activity type identification and error categorization

**Error Scenarios Handled**:

- Workflow not found (404) - likely server restart
- Network timeouts and connection failures
- OpenAI API failures and rate limiting
- Worker not restarted after new workflow creation
- Invalid JSON arguments and missing payloads

### Security Considerations

**Environment Restrictions**:

- Temporal UI queries limited to development environment
- Production mode uses secure monitoring without exposing URLs
- No sensitive data exposure in production error messages
- Proper authentication handling for production API calls

**Safe Production Testing**:

- Payload downloading preserves data structure while removing sensitive info
- Production workflow testing without compromising security
- Environment-specific API endpoint handling

## Enhanced Debugging Examples

### Real-time Activity Monitoring Output

When running in development mode, you'll see detailed activity monitoring:

```bash
yarn run-workflow debugQuotesWorkflow '{"topic": "motivation", "currentTime": "2024-01-15T09:30:00Z"}' --async --max-timeout 30
```

**Expected Output**:

```
🚀 Executing workflow: debugQuotesWorkflow
   Environment: Development (http://localhost:2000)
   Operation: start
   Args: {
     "topic": "motivation",
     "currentTime": "2024-01-15T09:30:00Z"
   }
⏳ Workflow started
   Workflow ID: workflow-123
   Run ID: run-456
🔄 Polling workflow status (timeout: 30 seconds)...
   [1/6] Status: RUNNING (⚠️ Activity failing: generateDailyQuote, attempt 2)
      Error: OpenAI API rate limit exceeded
   [2/6] Status: RUNNING (🔄 2 retries in history, activities: generateDailyQuote)
   [3/6] Status: RUNNING (📋 1 pending activities)
   [4/6] Status: COMPLETED
✅ Workflow completed successfully after 2 retries
```

### Production Payload Testing Workflow

Complete workflow for testing production issues:

```bash
# Step 1: Download production payload
yarn run-workflow download e93f9cc2-3290-4f79-9eb5-3a31e985bf3b --env prod

# Output:
# 📥 Downloading workflow payload...
# 🔍 Finding runId for workflow: e93f9cc2-3290-4f79-9eb5-3a31e985bf3b
#    Found runId: run-789
# ✅ Payload saved to: run-workflow-payload.json
# 💡 Tip: Run 'yarn run-workflow contextCompanyWorkflow' to test with this payload

# Step 2: Test locally with enhanced debugging
yarn run-workflow contextCompanyWorkflow --async --max-timeout 60

# Output:
# 📂 Loading arguments from default file: run-workflow-payload.json
# 🚀 Executing workflow: contextCompanyWorkflow
#    Environment: Development (http://localhost:2000)
# ... (full debugging output)
```

## Best Practices for Enhanced Testing

### Development Testing Strategy

1. **Use Real-time Monitoring**: Always use `--async` mode for development testing to see activity failures immediately
2. **Set Appropriate Timeouts**: Use `--max-timeout 10` for quick tests, `--max-timeout 60` for complex workflows
3. **Monitor Activity Failures**: Pay attention to activity error messages and retry patterns
4. **Test with Production Data**: Use downloaded payloads to reproduce production issues

### Production Testing Strategy

1. **Use Secure Mode**: Production automatically runs without Temporal UI queries
2. **Download Payloads**: Always download production payloads for local reproduction
3. **Test Before Deployment**: Validate workflows in production after deployments
4. **Monitor Performance**: Track execution times and error rates

### Error Investigation Workflow

1. **Download Failed Payload**: Get the exact input that caused the failure
2. **Run with Enhanced Debugging**: Use dev mode with real-time monitoring
3. **Analyze Activity Failures**: Look for patterns in retry behavior and error messages
4. **Test Fix Locally**: Verify fix works with original payload
5. **Validate in Production**: Test fix with similar production data

## Integration with Development Workflow

### Worker Management

**CRITICAL**: After creating new workflows, always restart the worker:

```bash
# If using bin/dev (Overmind)
overmind restart worker

# If using yarn start:worker directly
# Stop (Ctrl+C) and restart
yarn start:worker
```

The enhanced `run-workflow` script detects this issue and provides helpful guidance.

### Documentation Updates

After workflow changes, update documentation:

```bash
# Generate workflow documentation
yarn g:workflow-doc

# Update workflow catalog
yarn g:catalog
```

## Command Reference (Updated)

### Core Commands

```bash
# Execute workflow (default: dev, sync)
yarn run-workflow <workflowName> [args]

# Enhanced debugging mode
yarn run-workflow <workflowName> [args] --async --max-timeout <seconds>

# Download workflow payload (default: prod)
yarn run-workflow download <workflowId> [runId]
```

### Enhanced Options

```bash
--env <dev|prod>          # Environment (default: dev)
--file <path>             # Load args from JSON file
--async                   # Run asynchronously with monitoring
--max-timeout <seconds>   # Timeout in seconds (default: 60)
```

### Example Commands

```bash
# Basic test with 10-second timeout
yarn run-workflow workflowName '{"args": "here"}' --async --max-timeout 10

# Production testing with 60-second timeout
yarn run-workflow workflowName '{"args": "here"}' --env prod --async --max-timeout 60

# Download and test workflow
yarn run-workflow download workflow-id --env prod
yarn run-workflow workflowName --async --max-timeout 30
```

## Troubleshooting Enhanced Features

### Common Issues

1. **Temporal UI Connection Failed**
   - **Expected**: `⚠️ Could not query local Temporal UI`
   - **Solution**: Normal behavior if Temporal UI is not running locally
   - **Impact**: Monitoring continues with standard polling

2. **Timeout in Development**
   - **Symptom**: `⏰ Polling timeout exceeded (30s elapsed)`
   - **Solution**: Increase timeout with `--max-timeout 60`
   - **Prevention**: Use appropriate timeout for workflow complexity

3. **Activity Failure Detection**
   - **Symptom**: `⚠️ Activity failing: activityName, attempt 2`
   - **Solution**: Check error message and activity implementation
   - **Benefits**: Immediate feedback vs waiting for completion

### Debug Tips

- **Use Fast Timeouts**: Start with `--max-timeout 10` for quick feedback
- **Monitor in Real-time**: Always use `--async` mode for development
- **Check Activity Messages**: Look for specific error details in monitoring output
- **Test Both Environments**: Verify behavior differences between dev and prod

This enhanced testing infrastructure provides powerful debugging capabilities while maintaining security and performance standards.
