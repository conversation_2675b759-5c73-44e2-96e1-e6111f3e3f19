# Pricing Accuracy Challenge: Research vs. Scraping

## Problem Overview

The Flow system generates vendor comparison content using the `ramp-vendor-pipeline-with-sanity.yaml` pipeline, which relies on the `DeepResearcherWorkflow` for pricing information. However, this approach has limitations that affect pricing accuracy, leading to editor feedback about missing pricing details and inaccurate pricing tables.

## Current Implementation

### Existing Pricing Research Flow

1. **DeepResearcherWorkflow** uses Perplexity's `sonar-pro` model to research pricing
2. **Domain filtering** restricts search to specific vendor websites
3. **Research questions** ask for pricing structure in markdown table format
4. **Generated content** includes pricing tables based on research findings

### Current Research Question

```yaml
- "PRICING TABLE: What is ${pipeline.input.name}'s exact pricing structure? List specific plan names, prices, key features, and ideal customer segments. Format as markdown table with columns: Plan | Price | Key features | Ideal for."
```

## Identified Issues

### 1. **Missing Actual Pricing Numbers**

- **Problem**: DeepResearcherWorkflow often returns generic pricing descriptions without specific dollar amounts
- **Example**: Returns "tiered pricing starting from Essential plan" instead of "Essential: US$14, Advanced: US$39"
- **Impact**: Editor feedback indicates pricing inaccuracies and missing pricing details

### 2. **Comprehensive Workflows Miss Pricing Too**

- **contextCompanyWorkflow**: Extensive domain analysis with multi-page scraping but still missed specific pricing numbers
- **Domain scraping**: Even when pricing pages are successfully scraped (confirmed by logs), specific pricing numbers don't appear in final output

### 3. **Research vs. Direct Scraping Gap**

- **Direct pricing page scraping** (bin/scrape tool) successfully captures exact pricing: US$14, US$39, US$49, US$64, US$99
- **Search-based research** (DeepResearcherWorkflow) misses these same numbers despite domain filtering
- **Root cause**: Search results may not include the most current pricing or dynamic pricing content

## Technical Investigation Results

### What Works

- **Direct URL scraping**: `yarn scrape https://pipedrive.com/pricing` captures exact pricing
- **Jina Reader API**: Successfully retrieves pricing page content with specific dollar amounts
- **URL discovery**: AI can identify pricing page URLs from homepage content
- **Pattern matching**: Common pricing URL patterns (/pricing, /plans) work for most domains

### What Doesn't Work

- **DeepResearcherWorkflow alone**: Misses specific pricing numbers despite domain restrictions
- **Comprehensive domain scraping**: Even when pricing pages are scraped, pricing numbers get lost in processing
- **Search-based approaches**: Don't reliably capture current, specific pricing information

## Proposed Solution: Hybrid Research + Scraping

### Approach

1. **Step 1: Research Phase** - Use DeepResearcherWorkflow to get general information and discover pricing page URLs
2. **Step 2: URL Extraction** - Parse pricing page URLs from research citations and responses
3. **Step 3: Direct Scraping** - Scrape discovered pricing URLs for exact pricing data
4. **Step 4: Content Combination** - Merge research context with scraped pricing content
5. **Step 5: Enhanced Generation** - Use combined content for accurate pricing tables

### Implementation Considerations

#### **Integration Points**

- **Option A**: Modify existing ramp-vendor-pipeline to add post-research scraping step
- **Option B**: Create enhanced pipeline variant with scraping capabilities
- **Option C**: Add scraping as optional enhancement that falls back gracefully

#### **URL Discovery Methods**

1. **Perplexity Citations**: Extract URLs from response citations
2. **Response Parsing**: Parse pricing URLs from research answer text
3. **Pattern Fallback**: Try common pricing URL patterns if discovery fails
4. **AI Discovery**: Use domain scraper's AI-powered URL prioritization

#### **Content Combination Strategies**

1. **Append to Research**: Add scraped content to research answers
2. **Separate Context**: Pass research and scraped content as distinct inputs
3. **Replace Pricing Sections**: Override research pricing with scraped pricing
4. **Hybrid Merge**: Intelligently combine complementary information

### Benefits

- **Accuracy**: Direct scraping ensures current, exact pricing information
- **Context**: Research provides company background and competitive positioning
- **Scalability**: Automated URL discovery works across hundreds of domains
- **Reliability**: Fallback mechanisms ensure pipeline robustness

### Challenges

- **Performance**: Additional scraping adds latency to pipeline execution
- **Rate Limits**: Scraping 400 domains requires careful rate limiting
- **URL Reliability**: Not all discovered URLs may contain usable pricing data
- **Content Integration**: Merging research and scraped content requires careful handling

## Editor Feedback Context

The challenge emerged from editor feedback on generated vendor comparisons:

1. **Missing "Ideal for" column** in pricing tables
2. **Inaccurate pricing information** compared to actual vendor websites
3. **Generic pricing descriptions** instead of specific dollar amounts

This feedback highlighted the gap between research-based content generation and the accuracy required for publication-ready vendor comparisons.

## Scale Requirements

The solution must handle:

- **400+ vendor domains** for comprehensive market coverage
- **Diverse pricing page structures** across different industries
- **Dynamic pricing content** that may be JavaScript-rendered
- **International pricing variations** and currency formats
- **Rate limiting constraints** for sustainable scraping at scale

## Success Metrics

A successful solution should:

1. **Capture specific pricing numbers** (e.g., US$14, US$39) in final output
2. **Include "Ideal for" column** as requested by editors
3. **Maintain research context** for comprehensive vendor understanding
4. **Scale efficiently** across hundreds of domains
5. **Degrade gracefully** when scraping fails or URLs are unavailable

## Related Components

### Existing Workflows

- `DeepResearcherWorkflow`: Current research approach
- `contextCompanyWorkflow`: Comprehensive domain analysis
- `contentUtilsDomainScraperWorkflow`: Lightweight domain scraping
- `ramp-vendor-pipeline-with-sanity.yaml`: Main pipeline for vendor content

### Supporting Tools

- `bin/scrape`: Direct URL scraping utility
- Perplexity API: Search and research capabilities
- Jina Reader API: Website content extraction
- Existing scraping functions: `scrapeResearchUrls`, `scrapeUrls`

### Infrastructure

- Temporal workflow orchestration
- Atlas pipeline execution
- Sanity CMS publishing
- Rate limiting and error handling systems
