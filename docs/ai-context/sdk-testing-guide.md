# SDK Testing Guide

## Running One-off Scripts with SDK Clients

When you need to test SDK clients, APIs, or run quick debugging scripts, follow this pattern:

### 1. Create Test Script

Create a test file in the project root (e.g., `test-script.js`):

```javascript
import { workerModule } from './packages/sdk/src/lib/worker-module.js';

async function testFunction() {
  // Access any SDK client through workerModule
  const { perplexityApiClient, openaiChatService, s3Client } = workerModule;

  try {
    // Your test code here
    const result = await perplexityApiClient.searchWithImages('test query');
    console.log('Result:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testFunction();
```

### 2. Run with Environment Variables

From the `packages/temporal` directory, run:

```bash
cd packages/temporal
npx tsx --env-file-if-exists=.env ../../test-script.js
```

### 3. Available SDK Clients

All SDK clients are available through `workerModule`:

```javascript
const {
  // API Clients
  perplexityApiClient,
  openaiChatService,
  anthropicChatService,
  azureOpenaiChatService,
  semRushApiClient,
  serpApiClient,
  youApiClient,
  slackApiClient,
  recraftApiClient,
  unsplashApiClient,
  workableApiClient,
  dataforSeoApiClient,
  deepgramApiClient,
  stabilityApiClient,
  hireflixApiClient,
  jinaReaderApiClient,

  // Storage & Utils
  s3Client,
  logger,
} = workerModule;
```

### 4. Example: Testing Perplexity API Images

```javascript
import { workerModule } from './packages/sdk/src/lib/worker-module.js';

async function testPerplexityImages() {
  const { perplexityApiClient } = workerModule;

  const response = await perplexityApiClient.searchWithImages('mountain landscapes');

  console.log('Images found:', response.images?.length || 0);
  if (response.images?.[0]) {
    console.log('First image fields:', Object.keys(response.images[0]));
    console.log('First image:', response.images[0]);
  }
}

testPerplexityImages();
```

### 5. Common Patterns

- **Environment variables**: Automatically loaded from `.env` files
- **Logging**: Available through `workerModule.logger`
- **Error handling**: Wrap in try/catch blocks
- **JSON output**: Use `JSON.stringify(data, null, 2)` for readable output

### 6. Cleanup

Delete test files after use to keep the repository clean:

```bash
rm test-script.js
```

## Important Notes

- Always run from `packages/temporal` directory to access environment variables
- Use `npx tsx` for TypeScript execution
- Import paths should be relative to project root
- All SDK clients are pre-configured with environment variables
- Test scripts are temporary - don't commit them to the repository
