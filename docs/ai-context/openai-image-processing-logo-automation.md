# OpenAI Image Processing for Logo Automation

## Context

The Ramp vendor workflow currently retrieves company logos via Brandfetch API, but requires manual processing in Canva Pro for:

- Background removal
- Resolution enhancement
- Resizing to 2400x2400 pixels

## Discovery: Existing OpenAI Image Workflow

Found existing workflow `content-utils/article-image-gpt` that provides **exactly the capabilities needed** for logo automation.

### Current OpenAI Image API Integration

**File**: `/packages/temporal/src/workflows/content-utils/article-image-gpt/activities.ts`

**Key Capabilities Available:**

1. **Image Editing API** (Lines 108-115):

```typescript
response = await openai.images.edit({
  image: shuffledFiles,
  prompt: messages[0].content,
  model: 'gpt-image-1',
  n: params.n || 1,
  quality: 'high',
  size: size,
});
```

2. **Features Perfect for Logo Processing:**
   - ✅ Background removal via edit API
   - ✅ Image enhancement and quality improvement
   - ✅ Resizing support (1024x1024, 1536x1024, 1024x1536)
   - ✅ Reference image support
   - ✅ S3 storage integration
   - ✅ High quality processing
   - ✅ Buffer handling for image data

3. **Current Workflow Capabilities:**
   - Downloads images from URLs
   - Processes with OpenAI image edit API
   - Uploads results to S3 bucket `flow-article-covers`
   - Returns processed image URLs

### Logo Automation Implementation Strategy

**Option 1: Extend Existing Workflow**

- Add "logo processing mode" to `article-image-gpt` workflow
- Add 2400x2400 size to `GPTImageSize` enum
- Create logo-specific prompts for background removal

**Option 2: Create Dedicated Logo Workflow**

- New workflow: `content-utils/logo-processor`
- Reuse OpenAI image editing logic from existing workflow
- Focused specifically on logo processing requirements

### Technical Implementation Details

**Required Changes:**

1. **Add 2400x2400 Size Support:**

```typescript
export enum GPTImageSize {
  '1024x1024' = '1024x1024',
  '1536x1024' = '1536x1024',
  '1024x1536' = '1024x1536',
  '2400x2400' = '2400x2400', // ADD THIS
  auto = 'auto',
}
```

2. **Logo Processing Prompts:**

- "Remove background, enhance quality, professional logo"
- "Clean white background, crisp edges, high resolution"
- "Corporate logo optimization, transparent background"

3. **Integration Points:**

- Input: Logo URL from Brandfetch API (`contextBrandWorkflow`)
- Processing: OpenAI image edit API
- Output: Processed logo URL for Sanity CMS
- Storage: S3 bucket for processed logos

### Automation Workflow

**Current Manual Process:**

1. Find company logo → Brandfetch API ✅
2. Remove background → **Manual Canva Pro**
3. Increase resolution → **Manual Canva Pro**
4. Resize to 2400x2400 → **Manual Canva Pro**

**Proposed Automated Process:**

1. Find company logo → Brandfetch API ✅
2. Remove background → **OpenAI Image Edit API**
3. Enhance resolution → **OpenAI Image Edit API**
4. Resize to 2400x2400 → **OpenAI Image Edit API**
5. Store processed logo → **S3 Storage**
6. Return URL for CMS → **Workflow output**

### Current Ramp Vendor Integration Point

**File**: `/packages/temporal/src/workflows/clients/ramp/ramp-vendor-workflow/workflow.ts`

**Line 126**: Logo assignment from brand info:

```typescript
logo: brandInfo?.logo || null,
```

**Enhancement**: Process logo before assignment:

```typescript
// Get raw logo from Brandfetch
const rawLogo = brandInfo?.logo;
// Process logo with OpenAI
const processedLogo = rawLogo ? await processCompanyLogo(rawLogo) : null;
// Use processed logo
logo: processedLogo,
```

### Cost Considerations

**OpenAI Image Edit Pricing**: ~$0.020 per image (1024x1024)
**vs Manual Canva Pro**: Time cost + subscription cost
**ROI**: High for 400+ vendor processing pipeline

### Next Steps

1. **Decide on implementation approach** (extend existing vs new workflow)
2. **Add 2400x2400 size support** to existing types
3. **Create logo processing prompts** for background removal/enhancement
4. **Test with sample logos** from Brandfetch API
5. **Integrate into Ramp vendor workflow**
6. **Scale to full vendor pipeline** (400+ vendors)

### Technical Files Reference

- **Main Workflow**: `content-utils/article-image-gpt/workflow.ts`
- **Activities**: `content-utils/article-image-gpt/activities.ts`
- **Types**: `content-utils/article-image-gpt/types.ts`
- **Integration Point**: `clients/ramp/ramp-vendor-workflow/workflow.ts`
- **Brand API**: `apis/brandfetch/brandfetch-api-client.ts`

This discovery provides a clear path to fully automate the logo processing workflow using existing OpenAI image capabilities.
