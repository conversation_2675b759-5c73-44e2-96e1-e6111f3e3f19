Execute a Temporal workflow with the provided JSON payload.

Parse the following JSON payload and run the corresponding workflow:

$ARGUMENTS

**Instructions:**

1. Parse the JSON payload to extract:
   - `workflowName` (required)
   - `args` (required) - the workflow input arguments
   - `operation` (optional) - "execute" (default), "async", or "poll"
   - `env` (optional) - environment to run in
   - `inspect` (optional) - enable inspection mode
   - `timeout` (optional) - timeout in seconds

2. Build and execute the yarn command:

   ```bash
   yarn run-workflow <workflowName> '<args_as_json_string>' [flags]
   ```

3. Add appropriate flags based on operation:
   - `execute`: no additional flags (synchronous)
   - `async`: add `--async` flag
   - `poll`: add `--async --poll` flags

4. Add other optional flags:
   - `--env <environment>` if env is specified
   - `--inspect` if inspect is true
   - `--timeout <seconds>` if timeout is specified

5. Execute the command using the Bash tool and show the results.

**Example payload:**

```json
{
  "workflowName": "contextCompanyWorkflow",
  "args": {
    "rawInfo": "rawInfo",
    "website": "julius.ai",
    "companyName": "Julius"
  },
  "operation": "execute"
}
```
