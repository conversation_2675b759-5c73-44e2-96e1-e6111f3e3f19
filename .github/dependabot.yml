version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    # Look for `package.json` and `lock` files in the `root` directory
    directory: "/"
    # Check for updates once a week
    schedule:
      interval: "weekly"
    # Limit the number of open PRs to avoid overwhelming the team
    open-pull-requests-limit: 10
    # Group all minor and patch updates together
    groups:
      development-dependencies:
        dependency-type: "development"
        update-types:
          - "minor"
          - "patch"
      production-dependencies:
        dependency-type: "production"
        update-types:
          - "minor"
          - "patch"
    # Assign reviewers
    reviewers:
      - "team-maintainers"
    # Labels on pull requests for version updates
    labels:
      - "dependencies"
      - "automated-pr"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    labels:
      - "dependencies"
      - "automated-pr"
      - "github-actions"
