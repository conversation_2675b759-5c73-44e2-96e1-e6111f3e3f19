name: Enforce workflow docs update

on:
  pull_request:
    paths:
      - 'packages/temporal/src/workflows/**/workflow.ts'
      - 'packages/temporal/src/workflows/**/activities.ts'

jobs:
  check-readme-files:
    runs-on: ubuntu-latest
    if: ${{ !contains(toJSON(github.event.pull_request.labels.*.name), 'skip-docs-check') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get list of changed files
        id: changes
        run: |
          git fetch origin ${{ github.base_ref }}
          CHANGED_FILES=$(git diff --name-only origin/${{ github.base_ref }} HEAD)
          echo "$CHANGED_FILES" > changed.txt
          echo "changed_files<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGED_FILES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Check for skip pattern
        id: skip-check
        run: |
          PR_TITLE=$(printf '%s\n' '${{ github.event.pull_request.title }}')
          echo "Debug: PR title: $PR_TITLE"
          if echo "$PR_TITLE" | grep -q "\[skip docs check\]"; then
            echo "skip=true" >> $GITHUB_OUTPUT
          else
            echo "skip=false" >> $GITHUB_OUTPUT
          fi

      - name: Enforce readme.xml update if workflow.ts or activities.ts changes
        if: steps.skip-check.outputs.skip != 'true'
        run: |
          # Read the changed files into an array
          mapfile -t CHANGED_FILES_ARRAY < <(echo "${{ steps.changes.outputs.changed_files }}")

          # Filter for specific file types
          WORKFLOW_FILES=()
          ACTIVITY_FILES=()
          README_FILES=()

          for file in "${CHANGED_FILES_ARRAY[@]}"; do
            # Skip files in the shared directory
            if [[ "$file" =~ /shared/ ]]; then
              continue
            fi

            if [[ "$file" =~ workflow\.ts$ ]]; then
              WORKFLOW_FILES+=("$file")
            elif [[ "$file" =~ activities\.ts$ ]]; then
              ACTIVITY_FILES+=("$file")
            elif [[ "$file" =~ readme\.xml$ ]]; then
              README_FILES+=("$file")
            fi
          done

          echo "Debug: Changed workflow files:"
          printf '%s\n' "${WORKFLOW_FILES[@]}"
          echo "Debug: Changed activity files:"
          printf '%s\n' "${ACTIVITY_FILES[@]}"
          echo "Debug: Changed readme files:"
          printf '%s\n' "${README_FILES[@]}"

          # Function to get the directory of a file
          get_dir() {
            echo "$1" | sed 's/\/[^\/]*$//'
          }

          # Function to get the workflow path for the command
          get_workflow_path() {
            echo "$1" | sed 's|^packages/temporal/src/workflows/||' | sed 's|/workflow\.ts$||'
          }

          # Check each changed workflow file
          if [ ${#WORKFLOW_FILES[@]} -gt 0 ]; then
            echo "Debug: Processing workflow files..."
            for workflow_file in "${WORKFLOW_FILES[@]}"; do
              workflow_dir=$(get_dir "$workflow_file")
              workflow_readme="${workflow_dir}/readme.xml"
              workflow_path=$(get_workflow_path "$workflow_file")

              echo "Debug: Checking workflow file: $workflow_file"
              echo "Debug: Expected readme path: $workflow_readme"

              # Check if the corresponding readme.xml was changed
              readme_found=false
              for readme_file in "${README_FILES[@]}"; do
                if [[ "$readme_file" == "$workflow_readme" ]]; then
                  readme_found=true
                  break
                fi
              done

              if [[ "$readme_found" == "false" ]]; then
                echo "❌ Workflow file $workflow_file was modified but readme.xml was not updated."
                echo "Please run 'yarn g:workflow-doc $workflow_path && yarn g:catalog' and push the changes."
                exit 1
              else
                echo "Debug: Found matching readme.xml update for $workflow_file"
              fi
            done
          fi

          # Check each changed activities file
          if [ ${#ACTIVITY_FILES[@]} -gt 0 ]; then
            echo "Debug: Processing activity files..."
            for activity_file in "${ACTIVITY_FILES[@]}"; do
              activity_dir=$(get_dir "$activity_file")
              activity_readme="${activity_dir}/readme.xml"
              workflow_path=$(get_workflow_path "$activity_file")

              echo "Debug: Checking activity file: $activity_file"
              echo "Debug: Expected readme path: $activity_readme"

              # Check if the corresponding readme.xml was changed
              readme_found=false
              for readme_file in "${README_FILES[@]}"; do
                if [[ "$readme_file" == "$activity_readme" ]]; then
                  readme_found=true
                  break
                fi
              done

              if [[ "$readme_found" == "false" ]]; then
                echo "❌ Activities file $activity_file was modified but readme.xml was not updated."
                echo "Please run 'yarn g:workflow-doc $workflow_path && yarn g:catalog' and push the changes."
                exit 1
              else
                echo "Debug: Found matching readme.xml update for $activity_file"
              fi
            done
          fi
