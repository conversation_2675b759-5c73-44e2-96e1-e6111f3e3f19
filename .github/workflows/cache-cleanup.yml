name: Cache Cleanup

on:
  pull_request:
    types:
      - closed

jobs:
  cleanup:
    runs-on: ubuntu-latest
    permissions:
      # Required to delete cache entries
      actions: write
      # Required to read repository contents
      contents: read
    steps:
      - name: Cleanup Pull Request Caches
        run: |
          echo "🧹 Starting cache cleanup for PR #${{ github.event.pull_request.number }}"
          echo "Fetching list of cache keys for branch: $BRANCH"
          
          # Get all cache keys for this PR branch
          cacheKeysForPR=$(gh cache list --ref $BRANCH --limit 100 --json id --jq '.[].id')
          
          if [ -z "$cacheKeysForPR" ]; then
            echo "No caches found for this PR branch"
            exit 0
          fi
          
          echo "Found caches to delete:"
          gh cache list --ref $BRANCH --limit 100
          
          # Set +e to not fail the workflow if individual cache deletions fail
          set +e
          echo "🗑️ Deleting caches..."
          deleted_count=0
          failed_count=0
          
          for cacheKey in $cacheKeysForPR; do
            if gh cache delete $cacheKey; then
              echo "✅ Deleted cache: $cacheKey"
              ((deleted_count++))
            else
              echo "❌ Failed to delete cache: $cacheKey"
              ((failed_count++))
            fi
          done
          
          echo "📊 Cache cleanup summary:"
          echo "  - Deleted: $deleted_count caches"
          echo "  - Failed: $failed_count caches"
          echo "🎉 Cache cleanup completed for PR #${{ github.event.pull_request.number }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}
          BRANCH: refs/pull/${{ github.event.pull_request.number }}/merge

      - name: Cleanup Additional PR References
        run: |
          echo "🧹 Cleaning up additional cache references for PR #${{ github.event.pull_request.number }}"
          
          # Also clean up caches for the head branch reference
          HEAD_BRANCH_REF="refs/heads/${{ github.event.pull_request.head.ref }}"
          echo "Checking for caches on head branch: $HEAD_BRANCH_REF"
          
          headCaches=$(gh cache list --ref $HEAD_BRANCH_REF --limit 50 --json id --jq '.[].id')
          
          if [ -n "$headCaches" ]; then
            echo "Found additional caches on head branch"
            set +e
            for cacheKey in $headCaches; do
              gh cache delete $cacheKey && echo "✅ Deleted head branch cache: $cacheKey"
            done
          else
            echo "No additional caches found on head branch"
          fi
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GH_REPO: ${{ github.repository }}