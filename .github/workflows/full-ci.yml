name: Full CI

on:
  push:
    branches: [ main ]
    paths:
      - 'package.json'
      - 'yarn.lock'
      - 'tsconfig.json'
      - '.github/workflows/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'package.json'
      - 'yarn.lock' 
      - 'tsconfig.json'
      - '.github/workflows/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  AIRTABLE_API_KEY: ${{ secrets.AIRTABLE_API_KEY }}
  ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
  ASHBY_API_KEY: test-api-key
  AWS_ACCESS_KEY_ID: test-api-key
  AWS_REGION: us-east-1
  AWS_SECRET_ACCESS_KEY: test-api-key
  AZURE_OPENAI_API_KEY: test-api-key
  BRANDFETCH_API_KEY: ${{ secrets.BRANDFETCH_API_KEY }}
  DATAFORSEO_LOGIN: test-api-key
  DATAFORSEO_PASSWORD: test-api-key
  DATAGRID_WEBFLOW_API_KEY: ${{ secrets.DATAGRID_WEBFLOW_API_KEY }}
  DEEPGRAM_API_KEY: test-api-key
  FIRECRAWL_API_KEY: ${{ secrets.FIRECRAWL_API_KEY }}
  GALILEO_API_KEY: ${{ secrets.GALILEO_API_KEY }}
  GALILEO_PROJECT: flow-tests
  GH_API_TOKEN: ${{ secrets.GH_API_TOKEN }}
  GOODCALL_WEBFLOW_API_KEY: ${{ secrets.GOODCALL_WEBFLOW_API_KEY }}
  HIREFLIX_API_KEY: test-api-key
  HTMLCSSIMAGE_API_KEY: test-api-key
  HTMLCSSIMAGE_USER_ID: test-user-id
  JINA_API_KEY: ${{ secrets.JINA_API_KEY }}
  LANGFUSE_HOST: https://us.cloud.langfuse.com
  LANGFUSE_PUBLIC_KEY: ${{ secrets.LANGFUSE_PUBLIC_KEY }}
  LANGFUSE_SECRET_KEY: ${{ secrets.LANGFUSE_SECRET_KEY }}
  LOG_LEVEL: warn
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  PERPLEXITY_API_KEY: test-api-key
  PEXELS_API_KEY: ${{ secrets.PEXELS_API_KEY }}
  RECRAFT_KEY: ${{ secrets.RECRAFT_KEY }}
  SEMRUSH_API_KEY: ${{ secrets.SEMRUSH_API_KEY }}
  SERP_API_KEY: ${{ secrets.SERP_API_KEY }}
  SLACK_GROWTHX_OAUTH_TOKEN: test-api-key
  SMITH_AI_WEBFLOW_API_KEY: test-api-key
  STABILITY_API_KEY: test-api-key
  STRAPI_KAPA_AI_API_KEY: ${{ secrets.STRAPI_KAPA_AI_API_KEY }}
  UNSPLASH_ACCESS_KEY: test-api-key
  WORKABLE_API_KEY: test-api-key
  YOU_API_KEY: test-api-key

jobs:
  build-and-test:
    name: Build and Test All Packages
    runs-on: ubuntu-latest
    if: ${{ github.actor != 'dependabot[bot]' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 24
          cache: "yarn"

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build SDK
        working-directory: packages/sdk
        run: yarn build

      - name: Build Workflows
        working-directory: packages/temporal
        run: yarn build

      - name: Build API
        working-directory: packages/next-app
        run: yarn build

      - name: Check SDK
        working-directory: packages/sdk
        run: yarn run check

      - name: Check Workflows
        working-directory: packages/temporal
        run: yarn run check

      - name: Check API
        working-directory: packages/next-app
        run: yarn run check