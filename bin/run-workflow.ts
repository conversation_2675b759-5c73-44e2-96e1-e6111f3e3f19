#!/usr/bin/env tsx

/**
 * 🚀 Workflow Runner Script
 *
 * Run workflows in development and production environments
 * Supports execution, status polling, and error inspection
 *
 * Usage:
 *   yarn run-workflow contextBrandWorkflow '{"domain": "example.com"}'
 *   yarn run-workflow contextCompanyWorkflow '{"website": "julius.ai", "companyName": "Julius"}' --env prod
 *   yarn run-workflow contextBrandWorkflow --file payload.json --async --poll
 *   yarn run-workflow contextBrandWorkflow '{"domain": "example.com"}' --async --poll --long
 *   yarn run-workflow contextBrandWorkflow '{"domain": "example.com"}' --async --poll --max-timeout 30
 *   yarn run-workflow download workflowId --env prod
 *   yarn run-workflow contextCompanyWorkflow
 */

import { Command } from 'commander';
import axios, { AxiosError } from 'axios';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

interface WorkflowTestConfig {
  baseUrl: string;
  name: string;
}

interface WorkflowResponse {
  workflowId: string;
  runId: string;
  status: 'STARTED' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  result?: unknown;
  error?: unknown;
}

interface WorkflowStatusResponse {
  workflowId: string;
  runId: string;
  status: string;
  input?: unknown;
  output?: unknown;
  error?: unknown;
  startTime?: string;
  endTime?: string;
  duration?: number;
}

// Allow environment variables to override default URLs
const getDevBaseUrl = (): string => {
  // Check for full URL override first
  if (process.env.API_BASE_URL) {
    return process.env.API_BASE_URL;
  }
  // Check for port override
  if (process.env.API_PORT) {
    return `http://localhost:${process.env.API_PORT}`;
  }
  // Default
  return 'http://localhost:2000';
};

const ENVIRONMENTS = {
  dev: {
    baseUrl: getDevBaseUrl(),
    name: 'Development',
  },
  prod: {
    baseUrl: process.env.PROD_API_BASE_URL || 'https://run.growthx.ai',
    name: 'Production',
  },
} as const;

/**
 * Quick check if workflow exists in catalog
 */
function validateWorkflowName(workflowName: string): boolean {
  try {
    const catalogPath = join(process.cwd(), 'src/workflows/catalog.json');
    if (!existsSync(catalogPath)) return true; // Skip validation if no catalog

    const content = readFileSync(catalogPath, 'utf-8');
    return content.includes(`"functionName": "${workflowName}"`);
  } catch {
    return true; // Skip validation on error
  }
}

class WorkflowRunner {
  private config: WorkflowTestConfig;
  private timeoutMinutes: number;
  private environment: string;

  constructor(environment: keyof typeof ENVIRONMENTS, timeoutMinutes: number = 10) {
    this.config = ENVIRONMENTS[environment];
    this.timeoutMinutes = timeoutMinutes;
    this.environment = environment;

    // Log the API URL being used
    console.log(`🔗 Using ${this.config.name} API: ${this.config.baseUrl}`);
  }

  async executeWorkflow(
    workflowName: string,
    args: unknown,
    operation: 'start' | 'execute' = 'start'
  ): Promise<WorkflowResponse> {
    const url = `${this.config.baseUrl}/api/workflow`;

    try {
      const response = await axios.post(
        url,
        {
          workflowName,
          args,
          operation,
        },
        {
          timeout: this.timeoutMinutes * 60 * 1000, // Use configured timeout for workflow execution
        }
      );

      const result = response.data;

      // Print header with workflow ID after getting the response
      const workflowId = result.workflowId || 'sync-execution';
      console.log(`# ${operation.toUpperCase()}: ${workflowName}  -  ID: ${workflowId}`);
      console.log();
      console.log(JSON.stringify(args, null, 2));

      if (operation === 'execute') {
        console.log();
        console.log(`# SUCCESS`);
        console.log();
        console.log(JSON.stringify(result, null, 2));
        return {
          workflowId,
          runId: result.runId || 'sync-execution',
          status: 'COMPLETED',
          result,
        };
      } else {
        return {
          workflowId,
          runId: result.runId,
          status: 'STARTED',
        };
      }
    } catch (error) {
      // Print header without workflow ID if request fails
      console.log(`# ${operation.toUpperCase()}: ${workflowName}`);
      console.log();
      console.log(JSON.stringify(args, null, 2));

      if (error instanceof AxiosError) {
        const errorMsg = error.response?.data || error.message;
        throw new Error(`HTTP ${error.response?.status || 'Error'}: ${JSON.stringify(errorMsg)}`);
      }
      throw error;
    }
  }

  async getWorkflowStatus(workflowId: string): Promise<WorkflowStatusResponse> {
    const url = `${this.config.baseUrl}/api/workflows/${workflowId}`;

    try {
      const response = await axios.get(url, { timeout: 10000 });
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorMsg = error.response?.data || error.message;
        throw new Error(`HTTP ${error.response?.status || 'Error'}: ${JSON.stringify(errorMsg)}`);
      }
      throw error;
    }
  }

  async getTemporalUIStatus(workflowId: string, environment: string): Promise<unknown> {
    // Only query Temporal UI in development environment
    // Production Temporal Cloud doesn't expose the same REST API structure
    // and would require different authentication/endpoint patterns
    if (environment !== 'dev') {
      return null;
    }

    // Query local Temporal UI directly for real-time workflow status (dev only)
    // Development: http://localhost:8233/api/v1/namespaces/default/workflows/{workflowId}
    // Production: Temporal Cloud UI uses different API structure not suitable for CLI access
    const temporalUIUrl = 'http://localhost:8233';
    const url = `${temporalUIUrl}/api/v1/namespaces/default/workflows/${workflowId}`;

    try {
      const response = await axios.get(url, { timeout: 10000 });
      return response.data;
    } catch {
      console.log(`   ⚠️ Could not query local Temporal UI`);
      return null;
    }
  }

  async getTemporalUIHistory(
    workflowId: string,
    runId: string,
    environment: string
  ): Promise<unknown> {
    if (environment !== 'dev') {
      return null;
    }

    const temporalUIUrl = 'http://localhost:8233';
    const url = `${temporalUIUrl}/api/v1/namespaces/default/workflows/${workflowId}/run/${runId}/history.json`;

    try {
      const response = await axios.get(url, { timeout: 10000 });
      return response.data;
    } catch {
      return null;
    }
  }

  analyzePendingActivities(temporalUIData: unknown): {
    pendingCount: number;
    activeFailures: Array<{ activityType: string; attempt: number; lastFailure: string }>;
    workflowTaskFailures: Array<{ message: string; source: string }>;
  } {
    const data = temporalUIData as {
      pendingActivities?: unknown[];
      workflowExecutionInfo?: {
        status?: string;
      };
    };

    // Check for workflow task failures by looking at the workflow status
    const workflowTaskFailures: Array<{ message: string; source: string }> = [];

    // If workflow status indicates failure, we should fail fast
    if (data.workflowExecutionInfo?.status === 'WORKFLOW_EXECUTION_STATUS_FAILED') {
      workflowTaskFailures.push({
        message: 'Workflow execution failed',
        source: 'WorkflowExecution',
      });
    }

    if (!data?.pendingActivities) {
      return { pendingCount: 0, activeFailures: [], workflowTaskFailures };
    }

    const activeFailures = data.pendingActivities
      .filter((activity: unknown) => {
        const act = activity as { lastFailure?: unknown };
        return act.lastFailure;
      })
      .map((activity: unknown) => {
        const act = activity as {
          activityType?: { name?: string };
          attempt?: number;
          lastFailure?: {
            message?: string;
            applicationFailureInfo?: { type?: string };
          };
        };
        return {
          activityType: act.activityType?.name || 'Unknown',
          attempt: act.attempt || 1,
          lastFailure: act.lastFailure?.message || 'Unknown error',
          failureType: act.lastFailure?.applicationFailureInfo?.type || 'Unknown',
        };
      });

    const pendingCount = data.pendingActivities.length;
    return {
      pendingCount,
      activeFailures,
      workflowTaskFailures,
    };
  }

  analyzeWorkflowHistory(historyData: unknown): {
    hasWorkflowTaskFailures: boolean;
    workflowTaskFailureMessage: string;
  } {
    const events = historyData as unknown[];
    if (!events || !Array.isArray(events)) {
      return { hasWorkflowTaskFailures: false, workflowTaskFailureMessage: '' };
    }

    for (const event of events) {
      const eventData = event as {
        event_type?: number;
        Attributes?: {
          WorkflowTaskFailedEventAttributes?: {
            failure?: {
              message?: string;
            };
          };
        };
      };

      // Check for WorkflowTaskFailedEventAttributes (event_type 9)
      if (
        eventData.event_type === 9 &&
        eventData.Attributes?.WorkflowTaskFailedEventAttributes?.failure?.message
      ) {
        return {
          hasWorkflowTaskFailures: true,
          workflowTaskFailureMessage:
            eventData.Attributes.WorkflowTaskFailedEventAttributes.failure.message,
        };
      }
    }

    return { hasWorkflowTaskFailures: false, workflowTaskFailureMessage: '' };
  }

  async getWorkflowOutput(workflowId: string, runId: string): Promise<unknown> {
    const url = `${this.config.baseUrl}/api/workflows/${workflowId}/${runId}/output`;

    try {
      const response = await axios.get(url, { timeout: 10000 });
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorMsg = error.response?.data || error.message;
        throw new Error(`HTTP ${error.response?.status || 'Error'}: ${JSON.stringify(errorMsg)}`);
      }
      throw error;
    }
  }

  async extractWorkflowError(workflowId: string): Promise<string> {
    try {
      const status = await this.getWorkflowStatus(workflowId);

      if (status.historyUrl) {
        const historyUrl = `${this.config.baseUrl}${status.historyUrl}`;
        const historyResponse = await axios.get(historyUrl, { timeout: 10000 });
        const history = historyResponse.data;

        // Look for the workflow execution failed event
        if (history.history && Array.isArray(history.history)) {
          for (const event of history.history) {
            if (event.eventType === 'EVENT_TYPE_WORKFLOW_EXECUTION_FAILED') {
              const failureMessage = event.workflowExecutionFailedEventAttributes?.failure?.message;
              if (failureMessage) {
                return failureMessage;
              }
            }
            // Also check for activity failures
            if (event.eventType === 'EVENT_TYPE_ACTIVITY_TASK_FAILED') {
              const failureMessage = event.activityTaskFailedEventAttributes?.failure?.message;
              if (failureMessage) {
                return failureMessage;
              }
            }
          }
        }
      }

      return 'Workflow failed';
    } catch {
      return 'Workflow failed';
    }
  }

  async pollWorkflowStatus(
    workflowId: string,
    intervalMs: number = 5000,
    failFast: boolean = true
  ): Promise<WorkflowStatusResponse> {
    let hasTriggeredFailFast = false;

    // Wait 1 second after POST request before first poll check
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Start the running indicator
    console.log();
    process.stdout.write('# RUNNING: ');

    while (true) {
      const status = await this.getWorkflowStatus(workflowId);
      if (!status) {
        process.stdout.write('x');
        continue;
      }

      const statusName = status.status?.name || status.status || 'UNKNOWN';

      // Get temporal UI data
      const temporalUIData = await this.getTemporalUIStatus(workflowId, this.environment);
      const { pendingCount, activeFailures, workflowTaskFailures } =
        this.analyzePendingActivities(temporalUIData);

      // Also check history for workflow task failures
      const historyData = await this.getTemporalUIHistory(
        workflowId,
        status.runId,
        this.environment
      );
      const { hasWorkflowTaskFailures } = this.analyzeWorkflowHistory(historyData);

      // Add a dot for each poll request
      process.stdout.write('.');

      // Use pendingCount for monitoring (preventing unused variable warning)
      if (pendingCount > 0 && this.environment === 'dev') {
        // In development, we have visibility into pending activities
      }

      // Fail-fast: Cancel workflow if activity failures or workflow task failures detected (only once)
      if (
        failFast &&
        (activeFailures.length > 0 || workflowTaskFailures.length > 0 || hasWorkflowTaskFailures) &&
        !hasTriggeredFailFast
      ) {
        hasTriggeredFailFast = true;
        await this.cancelWorkflow(workflowId, status.runId);

        console.log('\n');

        // Extract the actual error message from the workflow
        let errorMessage = await this.extractWorkflowError(workflowId);

        // If we couldn't get a specific error and we have activity failures, use the activity error
        if (errorMessage === 'Workflow failed' && activeFailures.length > 0) {
          errorMessage = activeFailures[0].lastFailure;
        }

        console.log(`# ERROR: ${errorMessage}`);
        console.log();

        // Create a special error that won't trigger the main error handler
        const error = new Error('FAIL_FAST_ERROR') as Error & { isFailFast: boolean };
        error.isFailFast = true;
        throw error;
      }

      if (statusName === 'COMPLETED') {
        console.log('\n');
        console.log(`# SUCCESS`);

        // Fetch the actual workflow output
        try {
          const output = await this.getWorkflowOutput(workflowId, status.runId);
          if (output?.result) {
            status.output = output.result;
          }
        } catch {
          // If we can't get the output, continue without it
        }

        return status;
      }

      if (statusName === 'FAILED') {
        console.log('\n');

        // Extract the actual error message from the workflow
        const errorMessage = await this.extractWorkflowError(workflowId);
        console.log(`# ERROR: ${errorMessage}`);
        console.log();
        return status;
      }

      if (statusName === 'CANCELLED') {
        console.log('\n');
        console.log(`# ERROR: Workflow cancelled`);
        console.log();
        return status;
      }

      // Wait before next poll
      await new Promise((resolve) => setTimeout(resolve, intervalMs));
    }
  }

  async inspectError(workflowId: string, runId: string): Promise<void> {
    console.log(`🔍 Inspecting workflow error...`);

    try {
      const output = await this.getWorkflowOutput(workflowId, runId);

      if (output.error) {
        console.log(`❌ Error Details:`);
        console.log(`   Message: ${output.error.message}`);
        console.log(`   Type: ${output.error.applicationFailureInfo?.type || 'Unknown'}`);

        if (output.error.stackTrace) {
          console.log(`   Stack Trace:`);
          console.log(output.error.stackTrace);
        }

        if (output.error.cause) {
          console.log(`   Cause:`);
          console.log(JSON.stringify(output.error.cause, null, 2));
        }
      }

      if (output.input) {
        console.log(`📝 Input:`);
        console.log(JSON.stringify(output.input, null, 2));
      }
    } catch (error) {
      console.log(`Failed to inspect error: ${error}`);
    }
  }

  async downloadWorkflowPayload(
    workflowId: string,
    runId?: string,
    filePath?: string
  ): Promise<void> {
    console.log(`📥 Downloading workflow payload...`);

    try {
      // If runId is not provided, get it from workflow status
      if (!runId) {
        console.log(`🔍 Finding runId for workflow: ${workflowId}`);
        const status = await this.getWorkflowStatus(workflowId);
        runId = status.runId;
        console.log(`   Found runId: ${runId}`);
      }

      const output = await this.getWorkflowOutput(workflowId, runId);

      const input = output.workflow?.input || output.input;

      if (input) {
        const payloadJson = JSON.stringify(input, null, 2);

        if (filePath) {
          writeFileSync(filePath, payloadJson);
          console.log(`✅ Payload saved to: ${filePath}`);
        } else {
          // Get workflow name from the output or history
          let workflowName = 'unknown';

          // Try to get workflow name from history
          try {
            const historyUrl = `${this.config.baseUrl}/api/workflows/${workflowId}/${runId}/history`;
            const historyResponse = await axios.get(historyUrl);
            const history = historyResponse.data;

            if (history.history && history.history.length > 0) {
              const firstEvent = history.history[0];
              if (firstEvent.workflowExecutionStartedEventAttributes?.workflowType?.name) {
                workflowName = firstEvent.workflowExecutionStartedEventAttributes.workflowType.name;
              }
            }
          } catch {
            // If we can't get the workflow name, use 'unknown'
            console.log(`   Could not determine workflow name, using 'unknown'`);
          }

          // Save to project root directory
          const defaultFileName = join(process.cwd(), '..', '..', 'run-workflow-payload.json');
          writeFileSync(defaultFileName, payloadJson);
          console.log(`✅ Payload saved to: run-workflow-payload.json`);
          console.log(
            `💡 Tip: Run 'yarn run-workflow ${workflowName} --file run-workflow-payload.json' to test with this payload`
          );
        }
      } else {
        console.log(`⚠️ No input data found for workflow`);
        console.log(`Debug: Response structure:`);
        console.log(JSON.stringify(output, null, 2));
      }
    } catch (error) {
      console.log(`Failed to download payload: ${error}`);
    }
  }

  private loadArgsFromFile(filePath: string): unknown {
    try {
      const content = readFileSync(filePath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`Failed to load payload from file ${filePath}: ${error}`);
    }
  }

  async cancelWorkflow(workflowId: string, runId: string): Promise<void> {
    const url = `${this.config.baseUrl}/api/workflows/${workflowId}`;
    const params = new URLSearchParams({
      action: 'cancel',
      reason: 'Fail-fast cancellation due to activity failure',
      runId: runId,
    });

    try {
      await axios.delete(`${url}?${params.toString()}`, { timeout: 10000 });
    } catch {
      // Silent cancellation failures - not critical for fail-fast functionality
    }
  }

  async runWorkflow(
    workflowName: string,
    args: unknown,
    options: {
      sync?: boolean;
      inspect?: boolean;
      timeoutSeconds?: number;
      failFast?: boolean;
    } = {}
  ): Promise<void> {
    try {
      const operation = options.sync ? 'execute' : 'start';
      const result = await this.executeWorkflow(workflowName, args, operation);

      if (operation === 'execute') {
        // Sync execution - we're done
        return;
      }

      // Async mode - always poll for status with fail-fast
      const status = await this.pollWorkflowStatus(result.workflowId, 5000, options.failFast);
      const statusName = status.status?.name || status.status || 'UNKNOWN';

      if (statusName === 'COMPLETED') {
        if (status.output) {
          console.log();
          console.log(JSON.stringify(status.output, null, 2));
        } else {
          // Try to fetch the output directly if it's not in the status
          try {
            const output = await this.getWorkflowOutput(result.workflowId, result.runId);
            if (output?.workflow?.output) {
              console.log();
              console.log(JSON.stringify(output.workflow.output, null, 2));
            } else if (output?.result) {
              console.log();
              console.log(JSON.stringify(output.result, null, 2));
            }
          } catch {
            // If we can't get the output, continue without it
          }
        }
      } else if (statusName === 'FAILED') {
        if (options.inspect) {
          await this.inspectError(result.workflowId, result.runId);
        }
      }
    } catch (error) {
      // Don't show duplicate error message for fail-fast errors
      if (error instanceof Error && (error as Error & { isFailFast?: boolean }).isFailFast) {
        console.log();
        return;
      }

      console.log();
      console.log(`# ERROR: ${error instanceof Error ? error.message : String(error)}`);
      console.log();
    }

    // Add space at the bottom for all cases
    console.log();
  }
}

// CLI Setup
const program = new Command();

program
  .name('run-workflow')
  .description('Run workflows in development and production environments')
  .version('1.0.0');

// Main workflow execution command
program
  .argument('[workflowName]', 'Name of the workflow to run (default: debugEchoWorkflow)')
  .argument('[args]', 'JSON string of workflow arguments (optional if using --file)')
  .option('--env <environment>', 'Environment to run (dev|prod)', 'dev')
  .option('--file <path>', 'Load workflow arguments from JSON file')
  .option('--sync', 'Execute synchronously without polling (disable fail-fast)')
  .option(
    '--max-timeout <seconds>',
    'Custom polling timeout in seconds (default is 60 seconds)',
    parseInt
  )
  .option('--fail-fast', 'Cancel workflow immediately on activity failures (default: true)')
  .option('--no-fail-fast', 'Allow workflow to continue on activity failures')
  .action(async (workflowName: string | undefined, argsString: string | undefined, options) => {
    try {
      // Use debugEchoWorkflow as default
      const actualWorkflowName = workflowName || 'debugEchoWorkflow';

      // Quick workflow validation
      if (!validateWorkflowName(actualWorkflowName)) {
        console.warn(
          `⚠️ Warning: Workflow '${actualWorkflowName}' not found in catalog. Run 'yarn g:catalog' to regenerate catalog.`
        );
      }

      let args: unknown;

      if (options.file) {
        console.log(`📂 Loading arguments from file: ${options.file}`);
        // Resolve file path relative to the original working directory (project root)
        const filePath = join(process.cwd(), '..', '..', options.file);
        args = JSON.parse(readFileSync(filePath, 'utf-8'));
      } else if (argsString) {
        args = JSON.parse(argsString);
      } else {
        // If no payload file and no args, use debugEchoWorkflow default
        if (actualWorkflowName === 'debugEchoWorkflow') {
          args = { message: 'Hello from run-workflow! 🚀', delaySeconds: 3 };
          console.log(`🎯 Running default debugEchoWorkflow demo`);
          console.log(
            `💡 Try: yarn run-workflow <workflowName> '{"args": "here"}' for other workflows`
          );
        } else {
          console.error(`❌ No arguments provided.`);
          console.error(`   Either provide args as second argument or use --file option`);
          process.exit(1);
        }
      }

      const env = options.env as keyof typeof ENVIRONMENTS;

      if (!ENVIRONMENTS[env]) {
        console.error(`❌ Invalid environment: ${env}. Use 'dev' or 'prod'`);
        process.exit(1);
      }

      // Calculate timeout based on options
      let timeoutSeconds = 60; // Default: 60 seconds
      if (options.maxTimeout) {
        timeoutSeconds = options.maxTimeout;
        console.log(`⏰ Using custom timeout: ${timeoutSeconds} seconds`);
      }

      const runner = new WorkflowRunner(env, Math.ceil(timeoutSeconds / 60)); // Convert to minutes for constructor
      await runner.runWorkflow(actualWorkflowName, args, {
        sync: options.sync, // Default to async execution, sync only when requested
        inspect: !options.sync, // Only inspect when async (default)
        timeoutSeconds,
        failFast: options.failFast, // Default true unless --no-fail-fast is used
      });
    } catch (error) {
      if (error instanceof SyntaxError) {
        console.error(`❌ Invalid JSON arguments: ${error.message}`);
      } else {
        console.error(`❌ Error: ${error}`);
      }
      process.exit(1);
    }
  });

// Payload download command
program
  .command('download')
  .description('Download workflow payload from production for local testing')
  .argument('<workflowId>', 'Workflow ID to download payload from')
  .argument(
    '[runId]',
    'Run ID to download payload from (optional - will auto-find if not provided)'
  )
  .option('--env <environment>', 'Environment to download from (dev|prod)', 'prod')
  .option('--save <path>', 'Save payload to file (default: run-workflow-payload.json)')
  .action(async (workflowId: string, runId: string | undefined, options) => {
    try {
      const env = options.env as keyof typeof ENVIRONMENTS;

      if (!ENVIRONMENTS[env]) {
        console.error(`❌ Invalid environment: ${env}. Use 'dev' or 'prod'`);
        process.exit(1);
      }

      const runner = new WorkflowRunner(env);
      await runner.downloadWorkflowPayload(workflowId, runId, options.save);
    } catch (error) {
      console.error(`❌ Error: ${error}`);
      process.exit(1);
    }
  });

// Export for programmatic use
export { WorkflowRunner };

// Run CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  program.parse();
}
