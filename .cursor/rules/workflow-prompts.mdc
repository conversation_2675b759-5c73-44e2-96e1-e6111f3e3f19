---
description:
globs: *prompts.ts
alwaysApply: false
---
# Cursor Rules for Temporal TypeScript Workflow Prompts

Every constant exported from a prompts.ts file is meant to be used in an activity from the activities.ts file in the same folder – these activities are functions used in our Temporal.io workflows. These prompts are used for LLMs from OpenAI, Anthropic, Perplexity and others.  With that in mind, this is how we work with our prompts:

## When Working with Prompts in Temporal Workflows

### File Structure & Naming

1. Each workflow's `prompts.ts` should:
   - Export prompts as const arrays
   - Use descriptive names ending in `Prompt`

```typescript
export const writingStylePrompt = [
  {
    role: 'system',
    content: `...`
  }
];
```

### Template Variables & Liquid Syntax

1. **Basic Variable Syntax**
   - Use double curly braces: `{{variable}}`
   - Match variables with types.ts definitions
   - Use dot notation for nested properties: `{{style.property}}`

2. **Liquid Control Flow**
   - Use `{%if condition%}...{%endif%}` for conditional blocks
   - Wrap optional content sections in conditionals
   - Use semantic (XML) tags to structure content

```typescript
export const examplePrompt = [
  {
    role: 'user',
    content: `
      // Basic variables
      Content: {{content}}
      Context: {{context}}

      // Conditional blocks with semantic tags
      {%if additionalData%}
      <additionalData>
      {{additionalData}}
      </additionalData>
      {%endif%}

      // Nested properties
      Style: {{style.property}}

      // Multiple conditions
      {%if competitors%}
      <competitors>
      {{competitors}}
      </competitors>
      {%endif%}
    `
  }
];
```

3. **Content Tags**
   - Wrap content blocks in semantic tags
   - Use tags that describe the content type
   - Common tags:
     ```typescript
     <content>{{content}}</content>
     <context>{{context}}</context>
     <examples>{{examples}}</examples>
     <competitors>{{competitors}}</competitors>
     ```

### System Message Structure

1. Define AI's role and expertise
2. Include step-by-step instructions if needed
3. Add examples for complex outputs
4. Keep focused on core instructions

```typescript
{
  role: 'system',
  content: `
    ## Role
    Clear role definition

    ## Steps
    1. Step one
    2. Step two

    ## Examples
    <examples>...</examples>
  `
}
```

### User Message Structure

1. Use markdown for structure
2. Wrap main content in tags
3. Use newlines for readability
4. Keep dynamic content here, at the end of everything, so we can to benefit from prompt caching

```typescript
{
  role: 'user',
  content: `
    <content>
    {{content}}
    </content>

    Additional context: {{context}}
  `
}
```

### Schema Handling

1. Don't include JSON format instructions in prompts
2. Schema validation are usually done in the service call:

```typescript
openaiChatService.request<Type>({
  messages,
  schema: TypeSchema,
  schemaName: 'type_schema'
});
```

### Best Practices

1. Keep prompts clean and focused
2. Avoid instruction repetition
3. Use markdown for structure
4. Include examples for complex outputs
5. Separate core instructions (system) from dynamic content (user)

6. Don't handle errors in prompts
7. Use content tags for main blocks
8. Match template variables with types
9. Use newlines for readability
10. Follow existing patterns from other workflows

### Example Full Prompt

```typescript
export const comprehensivePrompt = [
  {
    role: 'system',
    content: `
      ## Role
      You are an expert content analyzer.

      ## Task
      Analyze content and extract key characteristics.

      ## Steps
      1. Read the content
      2. Identify patterns
      3. Extract examples

      ## Examples
      <examples>
        <example>
          Input: "Sample text"
          Output: {characteristics: [...]}
        </example>
      </examples>
    `
  },
  {
    role: 'user',
    content: `
      <content>
      {{content}}
      </content>

      Context: {{context}}
      Style: {{style}}
    `
  }
];
```

### Advanced Prompt Techniques

1. **Zero-Shot vs Few-Shot**
   - Use zero-shot for straightforward tasks
   - Use few-shot when you need:
     - Consistent output formatting
     - Complex or specialized tasks
     - Domain-specific responses
   ```typescript
   // Zero-shot example
   export const zeroShotPrompt = [
     {
       role: 'system',
       content: 'You are an expert at X. Do Y.'
     }
   ];

   // Few-shot example
   export const fewShotPrompt = [
     {
       role: 'system',
       content: 'You are an expert at X.'
     },
     {
       role: 'user',
       content: `
         Example 1:
         Input: {{example1.input}}
         Output: {{example1.output}}

         Now process this:
         Input: {{input}}
       `
     }
   ];
   ```

2. **Chain-of-Thought (CoT)**
   - Use for complex reasoning tasks
   - Break down problems into clear steps
   - Works best with larger models
   ```typescript
   export const coTPrompt = [
     {
       role: 'system',
       content: `
         ## Role
         You are an expert at solving X.

         ## Process
         1. Break down the problem
         2. Solve each step
         3. Combine the results
       `
     },
     {
       role: 'user',
       content: `
         Let's solve this step by step:
         {{problem}}
       `
     }
   ];
   ```

3. **Directional Stimulus**
   - Include specific cues and keywords
   - Guide the model toward desired outputs
   ```typescript
   export const dspPrompt = [
     {
       role: 'system',
       content: `
         Focus on these key aspects:
         - {{aspect1}}
         - {{aspect2}}

         Emphasize: {{emphasis}}
       `
     }
   ];
   ```

4. **ReAct (Reasoning and Acting)**
   - Combine reasoning with specific actions
   - Useful for complex workflows
   ```typescript
   export const reactPrompt = [
     {
       role: 'system',
       content: `
         ## Steps
         1. Analyze the input
         2. Plan necessary actions
         3. Execute each action
         4. Validate results
       `
     },
     {
       role: 'user',
       content: `
         <input>{{input}}</input>
         <availableActions>{{actions}}</availableActions>
       `
     }
   ];
   ```
Remember:
- Choose techniques based on task complexity
- Combine techniques when needed
- Keep prompts clean and focused
- Use consistent formatting
- Follow our Liquid syntax conventions
