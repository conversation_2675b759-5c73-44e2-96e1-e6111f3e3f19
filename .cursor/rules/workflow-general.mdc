---
description:
globs:
alwaysApply: true
---
# Cursor Rules for Temporal TypeScript Workflows

Here we work with our way of coding Temporal.io workflows but using our own convetions, as following:

## When Working inside packages/temporal/src/workflows/

### Folder Structure Rules

1. Each workflow should be in its own subdirectory under packages/temporal/src/workflows/
2. Each workflow directory must contain:
   - workflow.ts (main orchestration)
   - activities.ts (business logic)
   - types.ts (Zod schemas and types)
   - prompts.ts (if using LLM prompts)

### Code Style & Patterns

#### workflow.ts

1. Define retry policies with:
   - startToCloseTimeout
   - initialInterval
   - maximumInterval
   - backoffCoefficient
   - maximumAttempts
   - nonRetryableErrorTypes
2. **Always validate input using Zod schemas before processing**
3. Use try/catch with handleWorkflowError helper
4. Keep workflow functions focused on orchestration, avoid business logic
5. Export async function for the main workflow
6. Declare a type for the workflow function input argument as `WorkflowInput` and return type as `Promise<WorkflowOutput>`

```typescript
import { handleWorkflowError, validateWorkflowInput } from '@flow/sdk/lib/workflow-utils.js';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { WorkflowInputSchema } from './types.js';

const {
  processData,
  validateResults
} = WorkflowScope.use<typeof activities>(import.meta.url);

export async function exampleWorkflow(rawInput: WorkflowInput): Promise<WorkflowOutput> {
  // Validate input
  const input = validateWorkflowInput(rawInput, WorkflowInputSchema);

  try {
    const processedData = await processData(input);
    return await validateResults(processedData);
  } catch (error) {
    handleWorkflowError(error);
  }
}
```

#### activities.ts

1. Each exported function should target one logical task
2. Use `ApplicationFailure` for error handling
3. Go easy on the types, we don't need to be too strict.
4. Keep external service calls (APIs, LLMs) in activities, not workflows
5. Pay attention to the path aliases in the imports (@temporal/xyz)
6. Make sure all the imports for types, schemas and prompts are correct.
7. We use schema for OpenAI and they must take a z.object() as input (make sure the Schema and the inferred type take that into account).
8. When working on an Activity that uses OpenAI o1 models don't include schema.

```typescript
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { archetypePrompt, writingStylePrompt } from './prompts.js';
import type { Archetype, WritingStyle } from './types.js';
import { ArchetypeSchema, WritingStyleSchema } from './types.js';

const { jinaReaderApiClient, openaiChatService, anthropicChatService } = workerModule;

export async function scrapeUrls(urls: string[]): Promise<string> {
  const content = await jinaReaderApiClient.scrape(urls[0]);
  return content.content;
}

export async function analyzeWritingStyle(content: string): Promise<WritingStyle> {
  const messages = await threadTemplate(writingStylePrompt, {
    content,
  });
  return openaiChatService.request<WritingStyle>({
    messages,
    model: 'gpt-4o',
    schema: WritingStyleSchema,
    schemaName: 'writing_style_schema',
  });
}

export async function generateArchetype(style: WritingStyle): Promise<Archetype> {
  const messages = await threadTemplate(archetypePrompt, { style }, { format: 'anthropic' });
  return anthropicChatService.request<Archetype>({
    messages,
    model: 'claude-3-5-sonnet-20240620',
    schema: ArchetypeSchema,
    schemaName: 'archetype_schema',
  });
}

export default WorkflowScope.register(import.meta.url, {
  scrapeUrls,
  analyzeWritingStyle,
  generateArchetype,
});
```

#### types.ts

1. Use Zod for schema validation
2. Define interfaces using z.object()
3. Export both schema and inferred type
4. Use descriptive names ending in 'Schema' for schemas
5. Use z.infer<typeof Schema> for TypeScript types

```typescript
import { z } from 'zod';

export const WorkflowInputSchema = z.object({
  urls: z.array(z.string().url()),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export type WorkflowOutput = {
  content: string;
  status: number;
}

export const WritingStyleSchema = z.object({
  characteristics: z.array(z.string()),
  quotes: z.array(z.string()),
});

export type WritingStyle = z.infer<typeof WritingStyleSchema>;

export const ArchetypeSchema = z.object({
  name: z.string(),
  description: z.string(),
  styleguideWithQuotes: z.string(),
});

export type Archetype = z.infer<typeof ArchetypeSchema>;
```

Important: We use a Zod but the main use case here is OpenAI backend so we don't support everything Zod has, keep things to simple schemas.
Don't use things like url or min/max and always wrap things in objects first.

#### prompts.ts

1. Export const arrays for prompt templates
2. Use role: "system" or "user" format
3. Use {{variable}} syntax for template variables
4. Structure prompts for clear system/user separation
5. Prefer to use the system message to pass the core instructions and the user message to the dynamic content
6. When working on a prompt for OpenAI o1 models don't include system roles, just user.

```typescript
export const ExamplePrompt = [
  {
    role: "system",
    content: "You are a helpful assistant that processes data."
  },
  {
    role: "user",
    content: "Process this data: {{input}}"
  }
];
```

### Logging in Workflows and Activities

Use the appropriate `log` import depending on the file type. This uses the installed Winston logger and automatically includes the Workflow ID and Run ID in log entries.

#### workflow.ts Logging

Import `log` from `@temporalio/workflow` for workflow files:

```typescript
import { log } from '@temporalio/workflow';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type * as activities from './activities.js';

const { generateTextContent } = WorkflowScope.use<typeof activities>(import.meta.url);

export async function internalDebugSimpleWorkflow(
  rawInput: WorkflowInput
): Promise<WorkflowOutput> {
  // Always validate input first
  log.info('generating log from workflow');
  
  // ... rest of workflow logic
}
```

#### activities.ts Logging

Import `log` from `@temporalio/activity` for activity files:

```typescript
import { log } from '@temporalio/activity';
import { workerModule } from '@flow/sdk/lib/worker-module.js';

export async function generateTextContent(_input: WorkflowInput): Promise<WorkflowOutput> {
  log.info('Generating text content');
  
  // ... rest of activity logic
}
```

#### Logging Best Practices

1. Use appropriate log levels: `log.debug()`, `log.info()`, `log.warn()`, `log.error()`
2. Include relevant context in log messages
3. Log key workflow/activity entry and exit points
4. Avoid logging sensitive data
5. Use structured logging when beneficial: `log.info('Processing data', { userId, dataSize })`

### Error Handling

1. **Always validate workflow input using `validateWorkflowInput(rawInput, WorkflowInputSchema)`**
2. Use `rawInput: WorkflowInput` parameter and validate with `validateWorkflowInput`
3. Include `'INVALID_INPUT'` in `nonRetryableErrorTypes` array
4. Use try/catch blocks in workflows and follow the `handleWorkflowError` pattern
5. The `validateWorkflowInput` function automatically handles validation errors with proper error messages

**Input Validation Pattern:**

```typescript
// Validate input
const result = WorkflowInputSchema.safeParse(input);
if (!result.success) {
  throw ApplicationFailure.create({
    message: `Invalid input: ${result.error.message}`,
    type: 'INVALID_INPUT',
  });
}
```

**Error Handling Pattern:**

```typescript
function handleWorkflowError(error: unknown): never {
  if (error instanceof ApplicationFailure) {
    throw error;
  }

  throw ApplicationFailure.nonRetryable(
    error instanceof Error ? error.message : 'Unknown error', 'UNRECOVERABLE_ERROR'
  );
}
```

**Non-retryable Error Types:**
Always include both `'UNRECOVERABLE_ERROR'` and `'INVALID_INPUT'` in your `nonRetryableErrorTypes` array:

```typescript
nonRetryableErrorTypes: ['UNRECOVERABLE_ERROR', 'INVALID_INPUT']
```

### API clients and coding style

All of our API clients are exposed through the `workerModule` constant exported from `@temporal/lib/worker-module.js`
and follow a naming convention with a suffix of `ApiClient` for the class name and property name.

**IMPORTANT: Never use axios directly. Always use HttpClient wrapper to prevent memory leaks from axios error objects.**

```typescript
import type { AxiosResponse } from 'axios';
import { HttpClient } from '../lib/http-client.js';
import { ApiError } from '../lib/api-error.js';
import logger from '../lib/logger.js';

export interface JinaScrapedContent {
  ///...
}

export class JinaReaderApiClient {
  private readonly httpClient: HttpClient;
  private readonly headers: Record<string, string>;

  constructor(
    private readonly baseUrl = 'https://r.jina.ai',
    private readonly apiKey = ensurePresent(process.env.JINA_API_KEY, 'JINA_API_KEY')
  ) {
    this.httpClient = new HttpClient();
    this.headers = {
      Authorization: `Bearer ${this.apiKey}`,
      Accept: 'application/json',
      'X-With-Images-Summary': 'true',
    };
  }

  async scrape(
    url: string,
    additionalHeaders?: Record<string, string>
  ): Promise<JinaScrapedContent> {
    try {
      const response = await this.httpClient.get(url, { headers: this.headers });
      return response.data;
    } catch (error: any) {
      // ApiError.fromError() handles memory-safe error conversion
      ApiError.fromError(error, 'JinaReaderAPI');
    }
  }
}
```

Usage will be:

```typescript
import { workerModule } from '@temporal/lib/worker-module.js';

const { jinaReaderApiClient } = workerModule;
const article = await jinaReaderApiClient.scrape('https://example.com');
console.log(article.content);
```

Here's the full list of available components exposed through the `workerModule` constant:

```typescript
  get semRushApiClient(): SemrushApiClient;
  get perplexityApiClient(): PerplexityApiClient;
  get serpApiClient(): SerpApiClient;
  get youApiClient(): YouApiClient;
  get slackApiClient(): SlackApiClient;
  get recraftApiClient(): RecraftApiClient;
  get unsplashApiClient(): UnsplashApiClient;
  get workableApiClient(): WorkableApiClient;
  get dataforSeoApiClient(): DataForSEOApiClient;
  get deepgramApiClient(): DeepgramApiClient;
  get stabilityApiClient(): StabilityApiClient;
  get hireflixApiClient(): HireflixApiClient;
  get jinaReaderApiClient(): JinaReaderApiClient;
  get openaiChatService(): OpenAIChatService;
  get anthropicChatService(): AnthropicChatService;
  get s3Client(): S3Client;
  get azureOpenaiChatService(): AzureOpenAIChatService;
```

### Service Integration

1. Import services from `@temporal/services/`
2. Import APIs from `@temporal/apis/`
3. Use `OpenAIChatService` for LLM interactions
4. Use proper tracing with Langfuse

### Type Safety

1. Always use TypeScript types for parameters and returns
2. Use Zod schemas for LLM output validation (the schema has to always be an object)
3. Export types for external usage
4. Use proper type narrowing in error handling

### Best Practices

1. Keep workflows focused on orchestration
2. Put business logic in activities
3. Use clear, descriptive naming
4. Include proper documentation comments
5. Follow existing patterns from example workflows
6. When workflow starts to get complex, extract helper functions and use them in the workflow.

### Creating New Workflows

**IMPORTANT: Never create workflows manually from scratch.** Use the workflow generators from the **project root directory**:

```bash
# Generate new workflow from plan (usable by Claude/AI)
yarn workspace @flow/workflows run generate workflow <path> --plan <planPath>

# Other generators (run from project root)
yarn g:workflow-interactive  # Interactive workflow generator (requires human input)
yarn g:workflow-prompts     # Generate workflow prompts (for existing workflows)
yarn g:workflow-doc         # Generate workflow documentation (for existing workflows)
```

## When working with OpenAI

- The models gpt-4o, o1-mini, o1, o1-preview, gpt-4o-mini are the models we use (they exist).
- We use `OpenAIChatService` for all OpenAI LLM interactions. And we declare it in the `activities.ts` file outside of the functions.
- We usually expect JSON outputs when using gpt-4o, but the way we specify that is by passing the schema and schema name in the request (we don't have to define the JSON in the prompt, keep the prompt clean).

## HTTP Client Usage Rules

**NEVER use axios directly in any code.** Always use the HttpClient wrapper to prevent memory leaks.

### Why HttpClient is Required

- Axios error objects contain massive stack traces and internal properties that cause memory leaks
- HttpClient interceptor creates clean Error objects with only essential properties
- ApiError.fromError() also protects against axios memory leaks at the application level

### Correct Usage Pattern

```typescript
import { HttpClient } from '@flow/sdk/lib/http-client.js';
import logger from '@flow/sdk/lib/logger.js';

const httpClient = new HttpClient();

try {
  const response = await httpClient.get('/api/endpoint');
  return response.data;
} catch (error: any) {
  // Clean error object, no memory leak
  ApiError.fromError(error, 'ServiceName');
}
```

### Forbidden Pattern

```typescript
// ❌ NEVER DO THIS - causes memory leaks
import axios from 'axios';
const response = await axios.get('/api/endpoint');
```

## General Typescript Rules

- We use ESM for imports in all files, and we need the .js extension for the imports.
- Go easy on the zod schema types. We don't need to be too strict.
- Go easy on error handling, and we prefer to handle errors in the workflow when possible.
