---
description: Git workflow rules - never commit to main
globs:
alwaysApply: true
---
# Git Workflow Rules

## Branch Protection

**NEVER commit directly to the main branch - always create a feature branch when making code changes**

Use descriptive branch names with prefixes:
- `fix/` for bug fixes  
- `feature/` for new features
- `refactor/` for code refactoring
- `docs/` for documentation changes
- `chore/` for maintenance tasks

Example:
```bash
git checkout -b feature/descriptive-name
# make changes
git add .
git commit -m "descriptive message"
git push -u origin feature/descriptive-name
```