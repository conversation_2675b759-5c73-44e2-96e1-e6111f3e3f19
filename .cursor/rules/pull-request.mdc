---
description:
globs:
alwaysApply: false
---
# Pull Request Best Practices
#
# This file was updated with AI assistance to add prompt documentation requirements.
# See the "Prompt Documentation in Pull Requests" section below for details.

## GitHub CLI Utility Integration

### Primary Method: Using GitHub CLI (`gh`)

**Always use GitHub CLI when available** - it's the preferred method for creating pull requests:

```bash
# Create branch and PR in one command
gh pr create --title "Descriptive PR title" --body-file .github/PULL_REQUEST_TEMPLATE.md --base main

# Or create branch first, then PR
git checkout -b fix/descriptive-branch-name
# ... make changes and commit ...
git push -u origin fix/descriptive-branch-name
gh pr create --title "Descriptive PR title" --body-file .github/PULL_REQUEST_TEMPLATE.md --base main
```

**When `gh` is available, always use it instead of manual PR creation.**

### Fallback Method: When `gh` is Not Installed

If GitHub CLI is not available, Cursor should:

1. **Suggest a branch name** following the naming convention
2. **Print the complete PR description** to copy/paste
3. **Provide manual steps** for creating the PR via GitHub web interface

#### Branch Name Suggestion Format
```
Suggested branch name: fix/descriptive-change-name
```

#### PR Description Output Format
```markdown
## Description

[Generated description based on changes]

## Changes Made

- [List of specific changes]

## How Has This Been Tested?

[Testing approach and methodology]

## Screenshots (if appropriate):

N/A - Code changes only
```

#### Manual PR Creation Steps
```bash
# 1. Create and switch to branch
git checkout -b fix/descriptive-branch-name

# 2. Make changes and commit
git add .
git commit -m "fix: descriptive commit message"

# 3. Push branch
git push -u origin fix/descriptive-branch-name

# 4. Create PR manually via GitHub web interface
# Visit: https://github.com/growthxai/flow/pull/new/fix/descriptive-branch-name
# Copy/paste the generated description above
```

## Branch Naming Convention

1. Use descriptive branch names with prefixes:
   - `fix/` for bug fixes
   - `feature/` for new features
   - `refactor/` for code refactoring
   - `docs/` for documentation changes
   - `chore/` for maintenance tasks

2. Use kebab-case for branch names
3. Include a brief description of the change

Examples:
- `fix/webflow-publishing-error-handling`
- `feature/add-user-authentication`
- `refactor/optimize-database-queries`

## Pull Request Creation Process

### 1. Create and Switch to New Branch
```bash
git checkout -b fix/descriptive-branch-name
```

### 2. Make Changes and Commit
```bash
git add .
git commit -m "Descriptive commit message"
```

### 3. Push Branch and Create PR
```bash
git push -u origin fix/descriptive-branch-name
gh pr create --title "Descriptive PR title" --body-file .github/PULL_REQUEST_TEMPLATE.md --base main
```

## Pull Request Title Standards

1. Use clear, descriptive titles
2. Start with a verb in present tense
3. Keep under 72 characters
4. Be specific about what the change accomplishes

Examples:
- "Fix webflow publishing error handling"
- "Add user authentication with OAuth"
- "Refactor database connection pooling"

## Pull Request Description Template

Always use the `.github/PULL_REQUEST_TEMPLATE.md` template and fill out all sections:

### Description Section
- Explain the purpose and motivation for the change
- Include context about why this change is necessary
- Reference any related issues or requirements
- Mention if it's required for specific systems (e.g., "necessary for Atlas to properly handle activity failures")

### Changes Made Section
- List specific technical changes
- Include file paths and function names
- Mention any new dependencies or imports
- Note any breaking changes

### How Has This Been Tested Section
- Describe testing approach and methodology
- Mention compatibility considerations
- Include any specific test scenarios
- Reference automated tests if applicable

### Screenshots Section
- Add screenshots for UI changes
- Use "N/A - Code changes only" for backend changes
- Include before/after screenshots when relevant

## Example Pull Request Description

```markdown
## Description

Updated the `publishContent` function in the webflow-publishing activities to throw `ApplicationFailure.nonRetryable()` instead of returning error results when publishing fails. This follows Temporal workflow best practices for error handling and is necessary for Atlas to properly handle activity failures.

## Changes Made

- Added `ApplicationFailure` import from `@temporalio/workflow`
- Modified `publishContent` function to throw `ApplicationFailure.nonRetryable()` with error type `'WEBFLOW_PUBLISHING_ERROR'` when API calls fail
- Maintained original return type `Promise<PublishingResult>` for successful cases

## How Has This Been Tested?

The changes follow established Temporal workflow patterns and maintain backward compatibility. Error handling now properly propagates failures to the workflow orchestration layer, ensuring Atlas can properly handle and respond to activity failures.

## Screenshots (if appropriate):

N/A - Code changes only
```

## Commit Message Standards

1. Use conventional commit format when possible
2. Keep first line under 50 characters
3. Use present tense ("Add feature" not "Added feature")
4. Be descriptive but concise
5. Reference issue numbers when applicable

Examples:
- `fix: handle webflow publishing errors properly`
- `feat: add user authentication system`
- `refactor: optimize database queries for performance`

## Review Process

1. Ensure all template sections are filled out
2. Verify the description clearly explains the change
3. Check that the title is descriptive and follows conventions
4. Confirm that any necessary context or examples are included
5. Update the description if the PR template creates empty sections

## Error Handling in PR Descriptions

When describing error handling changes:
- Explain why the current approach is problematic
- Describe how the new approach improves the situation
- Include specific error types and messages
- Reference any systems that depend on proper error handling
- Provide examples of failed workflows if available

## Prompt Documentation in Pull Requests

When finishing a pull request, always include a summary of the prompts or AI instructions used to build the change at the bottom of the PR description. This helps track the development process and provides context for future reference.

### Prompt Documentation Format

Add this section at the bottom of your PR description:

```markdown
## AI Development Context

**Prompts/Instructions Used:**
[Summarize the key prompts, instructions, or AI guidance used to build this feature]

**Development Approach:**
[Brief description of how AI was used in the development process]
```

### Example Prompt Documentation

```markdown
## AI Development Context

**Prompts/Instructions Used:**
- "Create a new Temporal workflow for user authentication that follows our established patterns"
- "Add proper error handling with ApplicationFailure for the webflow publishing activities"
- "Refactor the database queries to improve performance while maintaining backward compatibility"

**Development Approach:**
Used AI assistance to generate the initial workflow structure following our Temporal conventions, then iteratively refined the error handling and database optimization based on specific requirements.
```

### When to Include Prompt Documentation

- **Always include** when AI was used to generate or significantly modify code
- **Include** when following specific AI-generated patterns or conventions
- **Include** when the change was guided by AI instructions or prompts
- **Optional** for minor bug fixes or simple refactoring

### Benefits of Prompt Documentation

1. **Knowledge Preservation**: Keeps track of effective prompts for future development
2. **Context Understanding**: Helps reviewers understand the development approach
3. **Pattern Recognition**: Identifies successful AI-assisted development patterns
4. **Team Learning**: Enables team members to learn from effective prompt strategies

## GitHub CLI Installation

If `gh` is not installed, install it:

### macOS
```bash
brew install gh
```

### Ubuntu/Debian
```bash
sudo apt install gh
```

### Windows
```bash
winget install GitHub.cli
```

### Manual Installation
Visit: https://cli.github.com/
