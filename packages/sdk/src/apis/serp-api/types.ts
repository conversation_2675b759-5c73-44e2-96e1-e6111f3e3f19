export type SerpOrganicResult = {
  position: number;
  title: string;
  link: string;
  displayed_link: string;
  snippet: string;
  snippet_highlighted_words: string[];
  thumbnail?: string;
  date?: string;
};

export enum FlightSearchType {
  RoundTrip = 1,
  OneWay = 2,
  MultiCity = 3,
}

export enum FlightTravelClass {
  Economy = '1',
  PremiumEconomy = '2',
  Business = '3',
  FirstClass = '4',
}

export type FlightSearchParams = {
  departureId: string;
  arrivalId: string;
  outboundDate: string;
  returnDate: string;
  travelClass: FlightTravelClass;
  adults: string;
  type?: FlightSearchType;
};

export type SerpFlightSearchResult = {
  departure_airport: {
    name: string;
    id: string;
    time: string;
  };
  arrival_airport: {
    name: string;
    id: string;
    time: string;
  };
  duration: number;
  airplane: string;
  airline: string;
  airline_logo: string;
  travel_class: string;
  flight_number: string;
  legroom: string;
  extensions: string[];
};
