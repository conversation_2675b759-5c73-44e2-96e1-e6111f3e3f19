import { v4 as uuidv4 } from 'uuid';
import { ApiError } from '@flow/sdk/lib/api-error.js';
import type { HttpClient } from '@flow/sdk/lib/http-client.js';
import type {
  SanityDocument,
  SanityMutation,
  SanityMutationRequest,
  SanityMutationResponse,
  SanityQueryResponse,
  SanityConfig,
} from './types.js';

export class SanityApiClient {
  private readonly headers: Record<string, string>;
  private readonly baseUrl: string;
  private readonly config: SanityConfig;

  constructor(
    private readonly httpClient: HttpClient,
    config: SanityConfig
  ) {
    if (!config.token) {
      throw new Error('Sanity API token is required');
    }
    if (!config.projectId) {
      throw new Error('Sanity project ID is required');
    }
    if (!config.dataset) {
      throw new Error('Sanity dataset is required');
    }

    this.config = config;
    this.baseUrl = `https://${config.projectId}.api.sanity.io/${config.apiVersion}`;
    this.headers = {
      Authorization: `Bearer ${config.token}`,
      'Content-Type': 'application/json',
    };
  }

  private async request(
    method: 'GET' | 'POST',
    path: string,
    data?: unknown,
    queryParams?: Record<string, string>
  ): Promise<any> {
    try {
      let url = `${this.baseUrl}${path}`;

      if (queryParams) {
        const searchParams = new URLSearchParams(queryParams);
        url += `?${searchParams.toString()}`;
      }

      const config = { headers: this.headers };

      let response;
      switch (method) {
        case 'GET':
          response = await this.httpClient.get(url, config);
          break;
        case 'POST':
          response = await this.httpClient.post(url, data, config);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      return response.data;
    } catch (error: unknown) {
      const errorInstance = error instanceof Error ? error : new Error(String(error));
      throw ApiError.fromError(errorInstance, 'SanityAPI');
    }
  }

  async query<T = unknown>(
    query: string,
    params?: Record<string, unknown>,
    options?: {
      perspective?: 'published' | 'previewDrafts' | 'raw';
      returnDocuments?: boolean;
      explain?: boolean;
    }
  ): Promise<SanityQueryResponse<T>> {
    const queryParams: Record<string, string> = {};

    if (options?.perspective) {
      queryParams.perspective = options.perspective;
    }
    if (options?.returnDocuments) {
      queryParams.returnDocuments = 'true';
    }
    if (options?.explain) {
      queryParams.explain = 'true';
    }

    const requestBody = {
      query,
      ...(params && { params }),
    };

    return this.request('POST', `/data/query/${this.config.dataset}`, requestBody, queryParams);
  }

  async mutate(
    mutations: SanityMutation[],
    options?: {
      returnIds?: boolean;
      returnDocuments?: boolean;
      visibility?: 'sync' | 'async' | 'deferred';
    }
  ): Promise<SanityMutationResponse> {
    const queryParams: Record<string, string> = {};

    if (options?.returnIds) {
      queryParams.returnIds = 'true';
    }
    if (options?.returnDocuments) {
      queryParams.returnDocuments = 'true';
    }
    if (options?.visibility) {
      queryParams.visibility = options.visibility;
    }

    const requestBody: SanityMutationRequest = { mutations };

    return this.request('POST', `/data/mutate/${this.config.dataset}`, requestBody, queryParams);
  }

  async createDocument(
    document: SanityDocument,
    options?: {
      returnDocument?: boolean;
    }
  ): Promise<SanityMutationResponse> {
    const mutations: SanityMutation[] = [{ create: document }];
    return this.mutate(mutations, {
      returnIds: true,
      returnDocuments: options?.returnDocument,
    });
  }

  async createOrReplaceDocument(
    document: SanityDocument,
    options?: {
      returnDocument?: boolean;
    }
  ): Promise<SanityMutationResponse> {
    const mutations: SanityMutation[] = [{ createOrReplace: document }];
    return this.mutate(mutations, {
      returnIds: true,
      returnDocuments: options?.returnDocument,
    });
  }

  async updateDocument(
    id: string,
    updates: {
      set?: Record<string, unknown>;
      unset?: string[];
      setIfMissing?: Record<string, unknown>;
      inc?: Record<string, number>;
      dec?: Record<string, number>;
    },
    options?: {
      returnDocument?: boolean;
    }
  ): Promise<SanityMutationResponse> {
    const mutations: SanityMutation[] = [{ patch: { id, ...updates } }];
    return this.mutate(mutations, {
      returnIds: true,
      returnDocuments: options?.returnDocument,
    });
  }

  async deleteDocument(id: string): Promise<SanityMutationResponse> {
    const mutations: SanityMutation[] = [{ delete: { id } }];
    return this.mutate(mutations, {
      returnIds: true,
    });
  }

  async deleteDocumentsByQuery(query: string): Promise<SanityMutationResponse> {
    const mutations: SanityMutation[] = [{ delete: { query } }];
    return this.mutate(mutations, {
      returnIds: true,
    });
  }

  // Helper methods for common operations

  /**
   * Generate a unique key for Sanity blocks and other objects
   */
  generateKey(): string {
    return uuidv4().replace(/-/g, '').substring(0, 12);
  }

  /**
   * Generate a studio URL for editing a document
   */
  getStudioUrl(documentType: string, documentId: string): string {
    const baseStudioUrl = this.config.studioUrl || `https://${this.config.projectId}.sanity.studio`;
    return `${baseStudioUrl}/desk/${documentType};${documentId}`;
  }
}
