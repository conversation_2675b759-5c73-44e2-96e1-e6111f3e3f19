export interface SanityDocument {
  _id?: string;
  _type: string;
  _createdAt?: string;
  _updatedAt?: string;
  _rev?: string;
  [key: string]: unknown;
}

export interface SanityMutation {
  create?: SanityDocument;
  createOrReplace?: SanityDocument;
  createIfNotExists?: SanityDocument;
  delete?: { id: string } | { query: string };
  patch?: {
    id: string;
    set?: Record<string, unknown>;
    unset?: string[];
    setIfMissing?: Record<string, unknown>;
    inc?: Record<string, number>;
    dec?: Record<string, number>;
  };
}

export interface SanityMutationRequest {
  mutations: SanityMutation[];
}

export interface SanityMutationResponse {
  transactionId: string;
  results: Array<{
    id: string;
    operation: string;
    document?: SanityDocument;
  }>;
}

export interface SanityQueryResponse<T = unknown> {
  ms: number;
  query: string;
  result: T;
}

export interface SanityConfig {
  projectId: string;
  dataset: string;
  apiVersion: string;
  token: string;
  studioUrl?: string; // Optional studio URL for generating edit links
}
