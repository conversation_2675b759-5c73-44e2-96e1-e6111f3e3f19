import { ApiError } from '@flow/sdk/lib/api-error.js';
import { ensurePresent } from '@flow/sdk/lib/ensure-present-helper.js';
import type { HttpClient } from '@flow/sdk/lib/http-client.js';

import type { KapaAiAnswer, KapaAiProject } from './types.js';

// This is the Kapa AI API client, specifically for Strapi's Kapa AI instance.
// We're currently using the client's API key to authenticate requests, as it's trained on their documentation.
// At some point in the future, we could adapt this to use our own API key with <PERSON><PERSON> and feed it documentation about different clients, but for now this is only use for Strapi.
export class StrapiKapaAiApiClient {
  private readonly headers: Record<string, string>;

  constructor(
    private readonly httpClient: HttpClient,
    private readonly baseUrl = 'https://api.kapa.ai',
    private readonly apiKey = ensurePresent(
      process.env.STRAPI_KAPA_AI_API_KEY,
      'STRAPI_KAPA_AI_API_KEY'
    ),
    private readonly defaultProjectId = 'ada598a5-258a-4275-8678-f36ad9e94328',
    private readonly apiVersion = 'v1'
  ) {
    this.headers = {
      'X-API-KEY': this.apiKey,
      Accept: 'application/json',
    };
  }

  async getProject(projectId: string = this.defaultProjectId): Promise<KapaAiProject> {
    try {
      const response = await this.httpClient.get(
        `${this.baseUrl}/org/${this.apiVersion}/projects/${projectId}`,
        {
          headers: this.headers,
        }
      );
      return response.data;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      throw ApiError.fromError(error, 'Kapa AI');
    }
  }

  async chat(query: string, projectId: string = this.defaultProjectId): Promise<KapaAiAnswer> {
    try {
      const response = await this.httpClient.post(
        `${this.baseUrl}/query/${this.apiVersion}/projects/${projectId}/chat/`,
        {
          query: query,
        },
        {
          headers: this.headers,
        }
      );
      return response.data;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      throw ApiError.fromError(error, 'Kapa AI');
    }
  }
}
