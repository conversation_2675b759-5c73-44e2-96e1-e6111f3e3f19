import { ensurePresent } from '@flow/sdk/lib/ensure-present-helper.js';
import { ApiError } from '@flow/sdk/lib/api-error.js';
import type { HttpClient } from '@flow/sdk/lib/http-client.js';
import type { BrandfetchBrand } from './types.js';

export class BrandfetchApiClient {
  private readonly baseUrl = 'https://api.brandfetch.io/v2';
  private readonly apiKey = ensurePresent(process.env.BRANDFETCH_API_KEY, 'BRANDFETCH_API_KEY');
  private readonly headers = {
    Authorization: `Bearer ${this.apiKey}`,
    'Content-Type': 'application/json',
  } as Record<string, string>;

  constructor(private readonly httpClient: HttpClient) {}

  async getBrand(brand: string): Promise<BrandfetchBrand> {
    try {
      const response = await this.httpClient.get(`${this.baseUrl}/brands/${brand}`, {
        headers: this.headers,
      });

      return response.data as BrandfetchBrand;
    } catch (error: any) {
      throw ApiError.fromError(error, 'Brandfetch');
    }
  }
}
