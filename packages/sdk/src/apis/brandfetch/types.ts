export type BrandfetchBrand = {
  id: string;
  name: string | null;
  domain: string;
  claimed: boolean;
  description: string | null;
  longDescription: string | null;
  links: [
    {
      name: 'twitter' | 'facebook' | 'instagram' | 'github' | 'youtube' | 'linkedin' | 'crunchbase';
      url: string;
    },
  ];
  logos: [
    {
      theme: 'dark' | 'light';
      formats: [
        {
          src: string;
          format: 'svg' | 'webp' | 'png' | 'jpeg';
          height: number | null;
          width: number | null;
          size: number;
          background: 'transparent' | null;
        },
      ];
      tags: ('photography' | 'portrait')[];
      type: 'icon' | 'logo' | 'symbol' | 'other';
    },
  ];
  colors: [
    {
      hex: string;
      type: 'accent' | 'dark' | 'light' | 'brand';
      brightness: number;
    },
  ];
  fonts: [
    {
      name: string | null;
      type: 'title' | 'body';
      origin: 'google' | 'custom' | 'system';
      originId: string | null;
      weights: string[];
    },
  ];
  images: [
    {
      formats: [
        {
          src: string;
          format: 'svg' | 'webp' | 'png' | 'jpeg';
          height: number | null;
          width: number | null;
          size: number;
          background: 'transparent' | null;
        },
      ];
      tags: string[];
      type: 'banner' | 'other';
    },
  ];
  qualityScore: number;
  company: {
    employees: 1 | 2 | 11 | 51 | 201 | 501 | 1001 | 5001 | 10001;
    financialIdentifiers: {
      isin: string[];
      ticker: string[];
    } | null;
    foundedYear: number | null;
    industries: [
      {
        id: string;
        score: number;
        slug: string;
        name: string;
        emoji: string;
        parent: {
          id: string;
          slug: string;
          name: string;
          emoji: string;
        };
      },
    ];
    kind:
      | 'EDUCATIONAL'
      | 'GOVERNMENT_AGENCY'
      | 'NON_PROFIT'
      | 'PARTNERSHIP'
      | 'PRIVATELY_HELD'
      | 'PUBLIC_COMPANY'
      | 'SELF_EMPLOYED'
      | 'SELF_OWNED'
      | null;
    location: {
      city: string | null;
      country: string | null;
      countryCode: string | null;
      region: string | null;
      state: string | null;
      subregion: string | null;
    };
  };
  isNsfw: boolean;
  urn: string;
};
