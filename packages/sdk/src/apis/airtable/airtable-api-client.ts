import { ApiError } from '@flow/sdk/lib/api-error.js';
import { ensurePresent } from '@flow/sdk/lib/ensure-present-helper.js';
import type { HttpClient } from '@flow/sdk/lib/http-client.js';

export interface AirtableRecord {
  fields: Record<string, any>;
}

export interface AirtableCreateRecordResponse {
  id: string;
  createdTime: string;
  fields: Record<string, any>;
}

export interface AirtableCreateRecordsResponse {
  records: AirtableCreateRecordResponse[];
}

export class AirtableApiClient {
  private readonly headers: Record<string, string>;

  constructor(
    private readonly httpClient: HttpClient,
    private readonly baseUrl = 'https://api.airtable.com/v0',
    private readonly apiKey = ensurePresent(process.env.AIRTABLE_API_KEY, 'AIRTABLE_API_KEY')
  ) {
    this.headers = {
      Authorization: `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };
  }

  async createRecord(
    baseId: string,
    tableIdOrName: string,
    fields: Record<string, any>
  ): Promise<AirtableCreateRecordResponse> {
    try {
      const response = await this.httpClient.post(
        `${this.baseUrl}/${baseId}/${encodeURIComponent(tableIdOrName)}`,
        {
          fields,
          typecast: true,
        },
        { headers: this.headers }
      );

      return response.data;
    } catch (error: any) {
      throw ApiError.fromError(error, 'AirtableAPI');
    }
  }

  async createRecords(
    baseId: string,
    tableIdOrName: string,
    records: AirtableRecord[]
  ): Promise<AirtableCreateRecordsResponse> {
    try {
      const response = await this.httpClient.post(
        `${this.baseUrl}/${baseId}/${encodeURIComponent(tableIdOrName)}`,
        {
          records: records.map((record) => ({
            fields: record.fields,
          })),
          typecast: true,
        },
        { headers: this.headers }
      );

      return response.data;
    } catch (error: any) {
      throw ApiError.fromError(error, 'AirtableAPI');
    }
  }
}
