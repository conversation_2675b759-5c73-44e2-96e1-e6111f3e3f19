export type PrismicDocumentRequest = {
  title?: string;
  type: string;
  lang?: string;
  data: PrismicDocumentData;
  tags?: string[];
  uid?: string;
  alternate_language_id?: string;
};

// Prismic field type definitions
export type PrismicBooleanField = null | boolean;

export type PrismicColorField = null | string; // hexadecimal color value

export type PrismicDateField = null | string; // YYYY-MM-DD format

export type PrismicTimestampField = null | string; // YYYY-MM-DDTHH:MM:SS+0000 format

export type PrismicKeyTextField = string;

export type PrismicNumberField = null | number;

export type PrismicSelectField = null | string;

export type PrismicIntegrationField = string | number;

export type PrismicEmbedField = {
  embed_url?: string;
  [key: string]: any;
};

export type PrismicGeopointField = {
  latitude?: string;
  longitude?: string;
};

export type PrismicLinkField = {
  link_type: 'Document' | 'Web' | 'Media';
  id?: string; // required for Document/Media
  url?: string; // required for Web
  target?: '_blank';
  text?: string;
};

export type PrismicContentRelationshipField = PrismicLinkField;

export type PrismicImageField = {
  id: string; // required
  alt?: string;
  copyright?: string;
  dimensions?: {
    width?: number;
    height?: number;
  };
  edit?: {
    x?: number;
    y?: number;
    zoom?: number;
    background?: string;
  };
  x?: number;
  y?: number;
  zoom?: number;
  background?: string;
  [thumbnailName: string]: any; // for thumbnail properties
};

export type PrismicRichTextSpan = {
  type: 'em' | 'strong' | 'hyperlink';
  start: number;
  end: number;
  data?: PrismicLinkField; // for hyperlink spans
};

export type PrismicRichTextBlock = {
  type:
    | 'paragraph'
    | 'o-list-item'
    | 'list-item'
    | 'heading1'
    | 'heading2'
    | 'heading3'
    | 'heading4'
    | 'heading5'
    | 'heading6'
    | 'preformatted'
    | 'embed'
    | 'image';
  text?: string; // required for most types
  spans?: PrismicRichTextSpan[];
  direction?: 'rtl';
  oembed?: {
    embed_url: string;
  }; // required for embed type
} & Partial<PrismicImageField>; // for image type blocks

export type PrismicRichTextField = PrismicRichTextBlock[];

export type PrismicGroupField = Record<string, any>[];

export type PrismicSlice = {
  slice_type: string;
  variation: string;
  id?: string;
  slice_label?: string;
  primary: Record<string, any>;
  items?: Record<string, any>[]; // deprecated but still supported
};

export type PrismicSliceZoneField = PrismicSlice[];

// Union type for all possible Prismic field values
export type PrismicFieldValue =
  | PrismicBooleanField
  | PrismicColorField
  | PrismicDateField
  | PrismicTimestampField
  | PrismicKeyTextField
  | PrismicNumberField
  | PrismicSelectField
  | PrismicIntegrationField
  | PrismicEmbedField
  | PrismicGeopointField
  | PrismicLinkField
  | PrismicContentRelationshipField
  | PrismicImageField
  | PrismicRichTextField
  | PrismicGroupField
  | PrismicSliceZoneField;

export type PrismicDocumentData = Record<string, PrismicFieldValue>;
