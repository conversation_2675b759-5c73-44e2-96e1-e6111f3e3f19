import { ApiError } from '@flow/sdk/lib/api-error.js';
import type { HttpClient } from '@flow/sdk/lib/http-client.js';
import type { PrismicDocumentRequest } from './types.js';

export class PrismicMigrationApiClient {
  private readonly headers: Record<string, string>;
  private readonly baseUrl = 'https://migration.prismic.io';
  private readonly apiKey: string;
  private readonly repository: string;

  constructor(
    private readonly httpClient: HttpClient,
    apiKey: string,
    repository: string
  ) {
    if (!apiKey) {
      throw new Error('Prismic Migration API key is required');
    }
    this.apiKey = apiKey;
    this.repository = repository;
    this.headers = {
      Authorization: `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      repository: this.repository,
      // Key required for migration API beta—not unique to a client/repository
      'x-api-key': 'cSaZlfkQlF9C6CEAM2Del6MNX9WonlV86HPbeEJL',
    };
  }
  // Universal request method that handles all HTTP methods
  private async request(
    method: 'POST' | 'PUT',
    path: string,
    data?: PrismicDocumentRequest
  ): Promise<any> {
    try {
      const url = `${this.baseUrl}${path}`;
      const config = { headers: this.headers };

      let response;
      switch (method) {
        case 'POST':
          response = await this.httpClient.post(url, data, config);
          break;
        case 'PUT':
          response = await this.httpClient.put(url, data, config);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      return response.data;
    } catch (error: any) {
      throw ApiError.fromError(error, 'PrismicMigrationAPI');
    }
  }

  async createDocument(document: PrismicDocumentRequest): Promise<any> {
    return this.request('POST', '/documents', document);
  }

  async updateDocument(documentId: string, document: PrismicDocumentRequest): Promise<any> {
    return this.request('PUT', `/documents/${documentId}`, document);
  }
}
