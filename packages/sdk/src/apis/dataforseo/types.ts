// Types for the response from the DataForSEO API for the on_page-instant_pages endpoint
// https://docs.dataforseo.com/v3/on_page-instant_pages/

// Common types
export type NullableString = string | null;

// Base response structure
export type DataForSEOBaseTask = {
  id: string;
  status_code: number;
  status_message: string;
  time: string;
  cost: number;
};

// Content metrics
export type ContentMetrics = {
  plain_text_size: number;
  plain_text_rate: number;
  plain_text_word_count: number;
  automated_readability_index: number;
  coleman_liau_readability_index: number;
  dale_chall_readability_index: number;
  flesch_kincaid_readability_index: number;
  smog_readability_index: number;
  description_to_content_consistency: number;
  title_to_content_consistency: number;
  meta_keywords_to_content_consistency: number | null;
};

// Page timing metrics
export type PageTiming = {
  time_to_interactive: number;
  dom_complete: number;
  largest_contentful_paint: number;
  first_input_delay: number;
  connection_time: number;
  time_to_secure_connection: number;
  request_sent_time: number;
  waiting_time: number;
  download_time: number;
  duration_time: number;
  fetch_start: number;
  fetch_end: number;
};

// Resource error warning
export type ResourceErrorWarning = {
  line: number;
  column: number;
  message: string;
  status_code: number;
};

// Resource errors
export type ResourceErrors = {
  errors: any | null;
  warnings: ResourceErrorWarning[] | null;
};

// Cache control
export type CacheControl = {
  cachable: boolean;
  ttl: number;
};

// Last modified timestamps
export type LastModified = {
  header: NullableString;
  sitemap: NullableString;
  meta_tag: NullableString;
};

// Page meta information
export type PageMeta = {
  title: string;
  charset: number;
  follow: boolean;
  generator?: string;
  htags: Record<string, string[]>;
  description: string;
  favicon: string;
  meta_keywords: string | null;
  canonical: string;
  internal_links_count: number;
  external_links_count: number;
  inbound_links_count: number;
  images_count: number;
  images_size: number;
  scripts_count: number;
  scripts_size: number;
  stylesheets_count: number;
  stylesheets_size: number;
  title_length: number;
  description_length: number;
  render_blocking_scripts_count: number;
  render_blocking_stylesheets_count: number;
  cumulative_layout_shift: number;
  meta_title: string | null;
  content: ContentMetrics;
  deprecated_tags: any | null;
  duplicate_meta_tags: string[] | null;
  spell: any | null;
  social_media_tags: Record<string, string>;
};

// SEO and page checks
export type PageChecks = {
  no_content_encoding: boolean;
  high_loading_time: boolean;
  is_redirect: boolean;
  is_4xx_code: boolean;
  is_5xx_code: boolean;
  is_broken: boolean;
  is_www: boolean;
  is_https: boolean;
  is_http: boolean;
  high_waiting_time: boolean;
  no_doctype: boolean;
  has_html_doctype: boolean;
  canonical: boolean;
  no_encoding_meta_tag: boolean;
  no_h1_tag: boolean;
  https_to_http_links: boolean;
  size_greater_than_3mb: boolean;
  meta_charset_consistency: boolean;
  has_meta_refresh_redirect: boolean;
  has_render_blocking_resources: boolean;
  low_content_rate: boolean;
  high_content_rate: boolean;
  low_character_count: boolean;
  high_character_count: boolean;
  small_page_size: boolean;
  large_page_size: boolean;
  low_readability_rate: boolean;
  irrelevant_description: boolean;
  irrelevant_title: boolean;
  irrelevant_meta_keywords: boolean;
  title_too_long: boolean;
  has_meta_title: boolean;
  title_too_short: boolean;
  deprecated_html_tags: boolean;
  duplicate_meta_tags: boolean;
  duplicate_title_tag: boolean;
  no_image_alt: boolean;
  no_image_title: boolean;
  no_description: boolean;
  no_title: boolean;
  no_favicon: boolean;
  seo_friendly_url: boolean;
  flash: boolean;
  frame: boolean;
  lorem_ipsum: boolean;
  seo_friendly_url_characters_check: boolean;
  seo_friendly_url_dynamic_check: boolean;
  seo_friendly_url_keywords_check: boolean;
  seo_friendly_url_relative_length_check: boolean;
  [key: string]: boolean;
};

// Main response types
export type DataForSEOInstantPageAnalysisResponse = {
  version: string;
  status_code: number;
  status_message: string;
  time: string;
  cost: number;
  tasks_count: number;
  tasks_error: number;
  tasks: DataForSEOInstantPageAnalysisTask[];
};

export type DataForSEOInstantPageAnalysisTask = {
  id: string;
  status_code: number;
  status_message: string;
  time: string;
  cost: number;
  result_count: number;
  path: string[];
  data: {
    api: string;
    function: string;
    url: string;
    custom_js?: string;
  };
  result: DataForSEOInstantPageAnalysisResult[];
};

export type DataForSEOInstantPageAnalysisResult = {
  crawl_progress: string;
  crawl_status: string | null;
  crawl_gateway_address: string;
  items_count: number;
  items: DataForSEOInstantPageAnalysisItem[];
};

export type DataForSEOInstantPageAnalysisItem = {
  resource_type: string;
  status_code: number;
  location: string | null;
  url: string;
  meta: PageMeta;
  page_timing: PageTiming;
  onpage_score: number;
  total_dom_size: number;
  custom_js_response?: {
    url: string;
    [key: string]: any;
  };
  custom_js_client_exception: any | null;
  resource_errors: {
    errors: any | null;
    warnings: Array<{
      line: number;
      column: number;
      message: string;
      status_code: number;
    }> | null;
  };
  broken_resources: boolean;
  broken_links: boolean;
  duplicate_title: boolean;
  duplicate_description: boolean;
  duplicate_content: boolean;
  click_depth: number;
  size: number;
  encoded_size: number;
  total_transfer_size: number;
  fetch_time: string;
  cache_control: {
    cachable: boolean;
    ttl: number;
  };
  checks: PageChecks;
  content_encoding: string;
  media_type: string;
  server: string;
  is_resource: boolean;
  last_modified: {
    header: string | null;
    sitemap: string | null;
    meta_tag: string | null;
  };
};
