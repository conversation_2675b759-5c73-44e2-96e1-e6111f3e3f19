import { ApiError } from '@flow/sdk/lib/api-error.js';
import { ensurePresent } from '@flow/sdk/lib/ensure-present-helper.js';
import type { HttpClient } from '@flow/sdk/lib/http-client.js';
import type { DataForSEOInstantPageAnalysisResponse } from './types.js';

export class DataForSEOApiClient {
  private readonly headers: Record<string, string>;

  constructor(
    private readonly httpClient: HttpClient,
    private readonly baseUrl = 'https://api.dataforseo.com/v3',
    login: string = ensurePresent(process.env.DATAFORSEO_LOGIN, 'DATAFORSEO_LOGIN'),
    password: string = ensurePresent(process.env.DATAFORSEO_PASSWORD, 'DATAFORSEO_PASSWORD')
  ) {
    const auth = Buffer.from(`${login}:${password}`).toString('base64');
    this.headers = {
      Authorization: `Basic ${auth}`,
      'Content-Type': 'application/json',
    };
  }

  async domainRankOverview(params: any): Promise<any> {
    try {
      const response = await this.httpClient.post(
        `${this.baseUrl}/dataforseo_labs/google/domain_rank_overview/live`,
        [params],
        { headers: this.headers }
      );
      return response.data;
    } catch (error: any) {
      throw ApiError.fromError(error, 'DataForSEO');
    }
  }

  async rankedKeywords(params: any): Promise<any> {
    try {
      const response = await this.httpClient.post(
        `${this.baseUrl}/dataforseo_labs/google/ranked_keywords/live`,
        [params],
        { headers: this.headers }
      );
      return response.data;
    } catch (error: any) {
      throw ApiError.fromError(error, 'DataForSEO');
    }
  }

  async rankedKeywordsForPage(
    url: string,
    params?: { limit?: number; location?: string; minVolume?: number }
  ): Promise<any> {
    const domain = new URL(url).hostname.replace('www.', '');
    const path = new URL(url).pathname;

    const filters: (string | (string | number)[])[] = [
      ['ranked_serp_element.serp_item.relative_url', '=', path],
    ];

    // Add volume filter if minVolume is provided or use default of 100
    const minVolume = params?.minVolume ?? 100;
    filters.push('and', ['keyword_data.keyword_info.search_volume', '>', minVolume]);

    return this.rankedKeywords({
      target: domain,
      location_name: params?.location || 'United States',
      limit: params?.limit || 100,
      filters,
    });
  }

  async relatedKeywords(params: {
    keyword: string;
    language_name?: string;
    location_code?: number;
    limit?: number;
  }): Promise<any> {
    try {
      const response = await this.httpClient.post(
        `${this.baseUrl}/dataforseo_labs/google/related_keywords/live`,
        [
          {
            ...params,
            language_name: params.language_name || 'English',
            location_code: params.location_code || 2840,
            limit: params.limit || 100,
          },
        ],
        { headers: this.headers }
      );
      return response.data;
    } catch (error: any) {
      throw ApiError.fromError(error, 'DataForSEO');
    }
  }

  async organicSearch(params: {
    keyword: string;
    languageCode?: string;
    locationCode?: number;
  }): Promise<any> {
    try {
      const response = await this.httpClient.post(
        `${this.baseUrl}/serp/google/organic/live/regular`,
        [
          {
            keyword: params.keyword,
            language_code: params.languageCode || 'en',
            location_code: params.locationCode || 2840, // Default to US
          },
        ],
        { headers: this.headers }
      );
      return response.data;
    } catch (error: any) {
      throw ApiError.fromError(error, 'DataForSEO');
    }
  }

  /**
   * Analyze a URL using DataForSEO's Instant Pages API to get immediate results
   * https://docs.dataforseo.com/v3/on_page/instant_pages/?bash
   *
   * @param url - The URL to analyze
   * @param params - Optional parameters for the analysis
   * @returns The instant analysis results
   */
  async instantPageAnalysis(
    url: string,
    params?: {
      loadResources?: boolean;
      enableJavaScript?: boolean;
      customUserAgent?: string;
    }
  ): Promise<DataForSEOInstantPageAnalysisResponse> {
    try {
      const response = await this.httpClient.post(
        `${this.baseUrl}/on_page/instant_pages`,
        [
          {
            url: url,
            check_spell: false,
            disable_cookie_popup: true,
            return_despite_timeout: true,
            load_resources: params?.loadResources || false,
            enable_javascript: params?.enableJavaScript || false,
            custom_user_agent: params?.customUserAgent || 'Mozilla/5.0 (compatible; RSiteAuditor)',
          },
        ],
        { headers: this.headers }
      );

      // Check if the response contains an error status code
      const task = response.data?.tasks?.[0];
      // DataForSEO has custom status codes. 20000 is the default success code.
      if (task?.status_code !== 20000) {
        const error = new Error(task.status_message || 'Unknown DataForSEO error');
        Object.assign(error, {
          response: {
            status: task.status_code,
            data: { message: task.status_message || 'Unknown DataForSEO error' },
          },
        });
        throw ApiError.fromError(error, 'DataForSEO');
      }

      return response.data;
    } catch (error: any) {
      throw ApiError.fromError(error, 'DataForSEO');
    }
  }
}
