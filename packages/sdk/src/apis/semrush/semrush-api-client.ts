import { ApiError } from '@flow/sdk/lib/api-error.js';
import { ArgumentError, ensurePresent } from '@flow/sdk/lib/ensure-present-helper.js';
import type {
  SemrushKeywordOrganicSearchData,
  SemrushKeywordOverview,
  SemrushUrlAdword,
  SemrushUrlOrganicKeyword,
  SemrushDomainAdword,
  SemrushDomainOrganicKeyword,
  SemrushDomainOrganicPage,
} from '@flow/sdk/apis/semrush/types.js';
import { SemrushColumnCode, SemrushKeywordIntentMap } from '@flow/sdk/apis/semrush/types.js';
import isEmpty from 'lodash/isEmpty.js';
import type { HttpClient } from '@flow/sdk/lib/http-client.js';

export class SemrushApiClient {
  constructor(
    private readonly httpClient: HttpClient,
    private readonly baseUrl = 'https://api.semrush.com',
    private readonly apiKey = ensurePresent(process.env.SEMRUSH_API_KEY, 'SEMRUSH_API_KEY')
  ) {}

  /**
   * Performs a keyword overview search for up to 100 keywords.
   * https://developer.semrush.com/api/v3/analytics/keyword-reports/#keyword-overview-one-database/
   *
   * @param keywords - The keywords to search for.
   * @returns The keyword overview.
   */
  async keywordOverview(keywords: string[]): Promise<SemrushKeywordOverview[]> {
    if (isEmpty(keywords)) {
      return [];
    }
    if (keywords.length > 100) {
      throw new ArgumentError('Too many keywords, maximum is 100');
    }
    try {
      const response = await this.httpClient.get(this.baseUrl, {
        params: {
          type: 'phrase_these',
          key: this.apiKey,
          display_limit: 10,
          phrase: keywords.join(';'),
          database: 'us',
          export_columns: [
            SemrushColumnCode.Keyword,
            SemrushColumnCode.SearchVolume,
            SemrushColumnCode.CostPerClick,
            SemrushColumnCode.Competition,
            SemrushColumnCode.TotalResults,
            SemrushColumnCode.KeywordIntent,
            SemrushColumnCode.KeywordDifficulty,
          ].join(','),
        },
      });

      if (!response.data) {
        throw new Error('No data received from Semrush API');
      }
      const rows = ensurePresent(
        response.data.trim().split('\n').slice(1),
        'Semrush keywordOverview'
      );
      return rows
        .map((row: string) => row.split(';'))
        .map((row: string[]) => {
          const [_keyword, volume, costPerClick, competition, results, intent, keywordDifficulty] =
            row;
          // SemRush may return multiple comma-separatedintents for a keyword, we'll take the first one
          const firstIntent = intent.split(',')[0];
          return {
            keyword: _keyword,
            volume: +volume,
            costPerClick: +costPerClick,
            competition: +competition,
            results: +results,
            intent: SemrushKeywordIntentMap[firstIntent as keyof typeof SemrushKeywordIntentMap],
            keywordDifficulty: +keywordDifficulty,
          };
        });
    } catch (error: any) {
      throw ApiError.fromError(error, 'Semrush');
    }
  }

  /**
   * Lists domains ranking in Google's top 100 organic search results with a requested keyword.
   * https://developer.semrush.com/api/v3/analytics/keyword-reports/#organic-results/
   *
   * @param keyword - The keyword to search for.
   * @returns The keyword organic search results.
   */
  async keywordOrganicSearch(keyword: string): Promise<SemrushKeywordOrganicSearchData[]> {
    try {
      const response = await this.httpClient.get(this.baseUrl, {
        params: {
          type: 'phrase_organic',
          key: this.apiKey,
          display_limit: 10,
          phrase: keyword,
          database: 'us',
          export_columns: [
            SemrushColumnCode.DomainName,
            SemrushColumnCode.TargetUrl,
            SemrushColumnCode.SerpFeatures,
          ].join(','),
        },
      });

      if (!response.data) {
        throw new Error('No data received from Semrush API');
      }

      return response.data
        .trim()
        .split('\n')
        .slice(1)
        .map((row: string) => {
          const [domain, url, keywordSerpFeatures] = row.split(';');
          return {
            domain,
            url,
            keywordSerpFeatures: keywordSerpFeatures.split(',').map(Number),
          };
        });
    } catch (error: any) {
      throw ApiError.fromError(error, 'Semrush');
    }
  }

  /**
   * Lists keywords that bring users to a URL via Google's top 100 organic search results.
   * https://developer.semrush.com/api/v3/analytics/url-reports/#url-organic-search-keywords/
   *
   * @param url - The URL to search for.
   * @returns The URL organic search results.
   */
  async urlOrganicSearchKeywords(
    url: string,
    options: {
      limit?: number;
    } = {}
  ): Promise<SemrushUrlOrganicKeyword[]> {
    try {
      const response = await this.httpClient.get(this.baseUrl, {
        params: {
          type: 'url_organic',
          key: this.apiKey,
          display_limit: options.limit ?? 10,
          display_sort: `${SemrushColumnCode.SearchVolume.toLocaleLowerCase()}_desc`,
          export_columns: [
            SemrushColumnCode.Keyword,
            SemrushColumnCode.Position,
            SemrushColumnCode.SearchVolume,
            SemrushColumnCode.CostPerClick,
            SemrushColumnCode.Competition,
            SemrushColumnCode.TrafficShare,
            SemrushColumnCode.TrafficCostPercentage,
            SemrushColumnCode.TotalResults,
            SemrushColumnCode.TrendData,
          ].join(','),
          url,
          database: 'us',
        },
      });

      if (!response.data) {
        throw new Error('No data received from Semrush API');
      }

      return response.data
        .trim()
        .split('\n')
        .slice(1)
        .map((row: string) => {
          const [
            keyword,
            position,
            volume,
            costPerClick,
            competition,
            traffic,
            cost,
            results,
            trendsStr,
          ] = row.split(';');
          return {
            keyword,
            position: +position,
            searchVolume: +volume,
            costPerClick: +costPerClick,
            competition: +competition,
            trafficPercent: +traffic,
            trafficCost: +cost,
            numberOfResults: +results,
            trends: trendsStr.split(',').map(Number),
          };
        });
    } catch (error: any) {
      throw ApiError.fromError(error, 'Semrush');
    }
  }

  /**
   * Lists keywords that bring users to a domain via Google's top 100 organic search results.
   * https://developer.semrush.com/api/v3/analytics/domain-reports/#domain-organic-search-keywords/
   *
   * @param domain - The domain to search for.
   * @param options - Optional parameters for the request.
   * @returns The domain's organic search keywords.
   */
  async domainOrganicKeywords(
    domain: string,
    options: {
      database?: string;
      limit?: number;
      offset?: number;
      positionFrom?: number;
      positionTo?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<SemrushDomainOrganicKeyword[]> {
    try {
      const {
        database = 'us',
        limit = 10,
        offset = 0,
        positionFrom,
        positionTo,
        sortBy,
        sortOrder = 'desc',
      } = options;

      let displayFilter = '';
      if (positionFrom || positionTo) {
        let filterStr = '';
        if (positionFrom) {
          filterStr = `+|${SemrushColumnCode.Position}|Gt|${positionFrom}`;
        }
        if (positionTo) {
          if (filterStr) {
            filterStr += '|';
          }
          filterStr += `+|${SemrushColumnCode.Position}|Lt|${positionTo}`;
        }
        displayFilter = filterStr;
      }

      const displaySort = sortBy
        ? `${sortBy}_${sortOrder}`
        : `${SemrushColumnCode.TrafficShare.toLocaleLowerCase()}_desc`;

      const response = await this.httpClient.get(this.baseUrl, {
        params: {
          type: 'domain_organic',
          key: this.apiKey,
          domain,
          database,
          display_limit: limit,
          display_offset: offset,
          display_filter: displayFilter || undefined,
          display_sort: displaySort,
          export_columns: [
            SemrushColumnCode.Keyword,
            SemrushColumnCode.Position,
            SemrushColumnCode.SearchVolume,
            SemrushColumnCode.CostPerClick,
            SemrushColumnCode.Competition,
            SemrushColumnCode.TrafficShare,
            SemrushColumnCode.TrafficCostPercentage,
            SemrushColumnCode.TargetUrl,
            SemrushColumnCode.TrendData,
          ].join(','),
        },
      });

      if (!response.data) {
        throw new Error('No data received from Semrush API');
      }

      return response.data
        .trim()
        .split('\n')
        .slice(1)
        .map((row: string) => {
          const [
            keyword,
            position,
            volume,
            costPerClick,
            competition,
            traffic,
            cost,
            url,
            trendsStr,
          ] = row.split(';');
          return {
            keyword,
            position: +position,
            searchVolume: +volume,
            costPerClick: +costPerClick,
            competition: +competition,
            trafficPercent: +traffic,
            trafficCost: +cost,
            url,
            trends: trendsStr ? trendsStr.split(',').map(Number) : undefined,
          };
        });
    } catch (error: any) {
      throw ApiError.fromError(error, 'Semrush');
    }
  }

  /**
   * Lists keywords that a URL buys in Google Ads along with ad details.
   * https://developer.semrush.com/api/v3/analytics/url-reports/#url-ads/
   *
   * @param url - The URL to get advertising data for.
   * @returns The URL's advertising data.
   */
  async urlAdvertisingKeywords(
    url: string,
    options: {
      database?: string;
      limit?: number;
    } = {}
  ): Promise<SemrushUrlAdword[]> {
    try {
      const response = await this.httpClient.get(this.baseUrl, {
        params: {
          type: 'url_adwords',
          key: this.apiKey,
          display_limit: options?.limit ?? 10,
          export_columns: [
            SemrushColumnCode.Keyword,
            SemrushColumnCode.AdPosition,
            SemrushColumnCode.AdTitle,
            SemrushColumnCode.AdText,
            SemrushColumnCode.VisibleUrl,
            SemrushColumnCode.TargetUrl,
            SemrushColumnCode.CostPerClick,
            SemrushColumnCode.SearchVolume,
            SemrushColumnCode.Competition,
            SemrushColumnCode.TrafficCostPercentage,
            SemrushColumnCode.TrendData,
          ].join(','),
          url,
          database: options?.database ?? 'us',
        },
      });

      if (!response.data) {
        throw new Error('No data received from Semrush API');
      }

      return response.data
        .trim()
        .split('\n')
        .slice(1)
        .map((row: string) => {
          const [
            keyword,
            position,
            title,
            text,
            visibleUrl,
            targetUrl,
            costPerClick,
            volume,
            competition,
            trafficCost,
            trendsStr,
          ] = row.split(';');
          return {
            keyword,
            position: +position,
            title,
            text,
            visibleUrl,
            targetUrl,
            costPerClick: +costPerClick,
            searchVolume: +volume,
            competition: +competition,
            trafficCost: +trafficCost,
            trends: trendsStr.split(',').map(Number),
          };
        });
    } catch (error: any) {
      throw ApiError.fromError(error, 'Semrush');
    }
  }

  /**
   * This report lists keywords that bring users to a domain via Google's paid search results.
   * https://developer.semrush.com/api/v3/analytics/domain-reports/#domain-paid-search-keywords/
   *
   * @param domain - The domain to search for.
   * @param options - Optional parameters for the request.
   * @returns The domain's paid search keywords.
   */
  async domainAdvertisingKeywords(
    domain: string,
    options: {
      database?: string;
      limit?: number;
      offset?: number;
      positionFrom?: number;
      positionTo?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<SemrushDomainAdword[]> {
    try {
      const { database = 'us', limit = 10, offset = 0, sortBy, sortOrder = 'desc' } = options;

      const displaySort = sortBy
        ? `${sortBy}_${sortOrder}`
        : `${SemrushColumnCode.SearchVolume}_desc`;

      const response = await this.httpClient.get(this.baseUrl, {
        params: {
          type: 'domain_adwords',
          key: this.apiKey,
          domain,
          database,
          display_limit: limit,
          display_offset: offset,
          display_sort: displaySort,
          export_columns: [
            SemrushColumnCode.Keyword,
            SemrushColumnCode.Position,
            SemrushColumnCode.DomainPosition,
            SemrushColumnCode.PositionDifference,
            SemrushColumnCode.SearchVolume,
            SemrushColumnCode.CostPerClick,
            SemrushColumnCode.VisibleUrl,
            SemrushColumnCode.TrafficShare,
            SemrushColumnCode.TrafficCostPercentage,
            SemrushColumnCode.Competition,
            SemrushColumnCode.TotalResults,
            SemrushColumnCode.TrendData,
          ].join(','),
        },
      });

      if (!response.data) {
        throw new Error('No data received from Semrush API');
      }

      return response.data
        .trim()
        .split('\n')
        .slice(1)
        .map((row: string) => {
          const [
            keyword,
            position,
            domainPosition,
            positionDifference,
            searchVolume,
            costPerClick,
            url,
            trafficShare,
            trafficCostPercent,
            competition,
            totalResults,
            trends,
          ] = row.split(';');
          return {
            keyword,
            position: +position,
            domainPosition: +domainPosition,
            positionDifference: +positionDifference,
            searchVolume: +searchVolume,
            costPerClick: +costPerClick,
            url,
            trafficShare: +trafficShare,
            trafficCostPercent: +trafficCostPercent,
            competition: +competition,
            totalResults: +totalResults,
            trends: trends.split(',').map(Number),
          };
        });
    } catch (error: any) {
      throw ApiError.fromError(error, 'Semrush');
    }
  }

  /**
   * This report lists domain's top organic pages
   * https://developer.semrush.com/api/v3/analytics/domain-reports/#domain-paid-search-keywords/
   *
   * @param domain - The domain to search for.
   * @param options - Optional parameters for the request.
   * @returns The domain's paid search keywords.
   */
  async domainOrganicPages(
    domain: string,
    options: {
      database?: string;
      limit?: number;
      offset?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<SemrushDomainOrganicPage[]> {
    try {
      const { database = 'us', limit = 10, offset = 0, sortBy, sortOrder = 'desc' } = options;

      const displaySort = sortBy
        ? `${sortBy}_${sortOrder}`
        : `${SemrushColumnCode.TrafficShare.toLocaleLowerCase()}_desc`;

      const response = await this.httpClient.get(this.baseUrl, {
        params: {
          type: 'domain_organic_unique',
          key: this.apiKey,
          domain,
          database,
          display_limit: limit,
          display_offset: offset,
          display_sort: displaySort,
          export_columns: [
            SemrushColumnCode.TargetUrl,
            SemrushColumnCode.KeywordCount,
            SemrushColumnCode.EstimatedTraffic,
            SemrushColumnCode.TrafficShare,
            SemrushColumnCode.UnknownIntentPositions,
            SemrushColumnCode.CommercialIntentPositions,
            SemrushColumnCode.InformationalIntentPositions,
            SemrushColumnCode.NavigationalIntentPositions,
            SemrushColumnCode.TransactionalIntentPositions,
            SemrushColumnCode.UnknownIntentTraffic,
            SemrushColumnCode.CommercialIntentTraffic,
            SemrushColumnCode.InformationalIntentTraffic,
            SemrushColumnCode.NavigationalIntentTraffic,
            SemrushColumnCode.TransactionalIntentTraffic,
            SemrushColumnCode.SerpFeaturesKeywords,
            SemrushColumnCode.SerpFeaturesTraffic,
          ].join(','),
        },
      });

      if (!response.data) {
        throw new Error('No data received from Semrush API');
      }

      return response.data
        .trim()
        .split('\n')
        .slice(1)
        .map((row: string) => {
          const [
            url,
            keywordCount,
            estimateTraffic,
            trafficShare,
            unknownIntentPositions,
            commercialIntentPositions,
            informationalIntentPositions,
            navigationalIntentPositions,
            transactionalIntentPositions,
            unknownIntentTraffic,
            commercialIntentTraffic,
            informationalIntentTraffic,
            navigationalIntentTraffic,
            transactionalIntentTraffic,
            serpFeaturesKeywords,
            serpFeaturesTraffic,
          ] = row.split(';');
          return {
            url,
            keywordCount: +keywordCount,
            estimateTraffic: +estimateTraffic,
            trafficShare: +trafficShare,
            unknownIntentPositions: +unknownIntentPositions,
            commercialIntentPositions: +commercialIntentPositions,
            informationalIntentPositions: +informationalIntentPositions,
            navigationalIntentPositions: +navigationalIntentPositions,
            transactionalIntentPositions: +transactionalIntentPositions,
            unknownIntentTraffic: +unknownIntentTraffic,
            commercialIntentTraffic: +commercialIntentTraffic,
            informationalIntentTraffic: +informationalIntentTraffic,
            navigationalIntentTraffic: +navigationalIntentTraffic,
            transactionalIntentTraffic: +transactionalIntentTraffic,
            serpFeaturesKeywords: +serpFeaturesKeywords,
            serpFeaturesTraffic: +serpFeaturesTraffic,
          };
        });
    } catch (error: any) {
      throw ApiError.fromError(error, 'Semrush');
    }
  }
}
