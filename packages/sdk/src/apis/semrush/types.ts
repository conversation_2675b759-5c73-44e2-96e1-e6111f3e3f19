/** Enum mapping human-readable names to SEMRush column codes */
export enum SemrushColumnCode {
  // Ad-related columns
  AdPosition = 'Ab', // Place on the SERP where an ad appeared (top, side, or bottom blocks)
  AdCost = 'Ac', // Estimated budget spent buying keywords in Google Ads
  AdKeywords = 'Ad', // Keywords a website is buying in Google Ads
  AdKeywordsChange = 'Am', // Changes in the number of paid keywords
  AdTraffic = 'At', // Traffic brought to the website via paid search results

  // Change metrics
  PaidTrafficChange = 'Bm', // Changes in paid traffic
  PaidTrafficPriceChange = 'Cm', // Changes in paid traffic price

  // Competition and cost metrics
  Competition = 'Co', // Competitive density of advertisers (1 = highest competition)
  CostPerClick = 'Cp', // Average price in USD advertisers pay for a click
  CompetitionLevel = 'Cr', // Competition level based on total keywords
  KeywordCoverage = 'Cv', // Keyword coverage percentage in last 12 months

  // Database and domain info
  RegionalDatabase = 'Db', // Regional database (US, UK, Italy, etc.)
  DomainName = 'Dn', // Domain name
  AdText = 'Ds', // Ad text
  CurrentDate = 'Dt', // Current date

  // SERP features
  SerpFeatures = 'Fk', // All SERP Features triggered by a keyword
  DomainSerpFeatures = 'Fp', // SERP features in which a domain appears
  DomainSerpFeaturesDeprecated = 'Fl', // (Deprecated) SERP features
  TotalSerpFeatures = 'FKn', // Total number of SERP Features triggered by keywords
  TotalDomainSerpFeatures = 'FPn', // Total number of SERP Features domain ranks in

  // IP and infrastructure
  IpAddress = 'Ip', // IP address

  // Intent metrics
  KeywordIntent = 'In', // Keyword intents (0-Commercial, 1-Informational, 2-Navigational, 3-Transactional)
  UnknownIntentPositions = 'Ipu', // Total number of positions with unknown intent
  CommercialIntentPositions = 'Ip0', // Total number of positions with Commercial intent
  InformationalIntentPositions = 'Ip1', // Total number of positions with Informational intent
  NavigationalIntentPositions = 'Ip2', // Total number of positions with Navigational intent
  TransactionalIntentPositions = 'Ip3', // Total number of positions with Transactional intent
  UnknownIntentTraffic = 'Itu', // Total amount of traffic with unknown intent
  CommercialIntentTraffic = 'It0', // Total amount of traffic with Commercial intent
  InformationalIntentTraffic = 'It1', // Total amount of traffic with Informational intent
  NavigationalIntentTraffic = 'It2', // Total amount of traffic with Navigational intent
  TransactionalIntentTraffic = 'It3', // Total amount of traffic with Transactional intent
  UnknownIntentCost = 'Icu', // Total cost of traffic with unknown intent
  CommercialIntentCost = 'Ic0', // Total cost of traffic with Commercial intent
  InformationalIntentCost = 'Ic1', // Total cost of traffic with Informational intent
  NavigationalIntentCost = 'Ic2', // Total cost of traffic with Navigational intent
  TransactionalIntentCost = 'Ic3', // Total cost of traffic with Transactional intent

  // Keyword metrics
  KeywordDifficulty = 'Kd', // Estimate of ranking difficulty (percentage)
  CommonKeywords = 'Np', // Common keywords domains rank for in top 100
  SearchVolume = 'Nq', // Average monthly search volume over last 12 months
  TotalResults = 'Nr', // Total number of organic results for a keyword

  // Organic metrics
  OrganicKeywordsCost = 'Oc', // Estimated price of organic keywords in Google Ads
  ImprovedRankings = 'Oe', // Keywords with improved rankings still in top 100
  DecreasedRankings = 'Oi', // Results with decreased rankings still in top 100
  LostKeywords = 'Ol', // Keywords no longer ranking in top 100
  OrganicKeywordsChange = 'Om', // Changes in number of organic keywords
  NewKeywords = 'On', // New keywords ranking in top 100
  OrganicKeywords = 'Or', // Keywords bringing users via top 100 organic results
  OrganicTraffic = 'Ot', // Traffic from top 100 organic search results

  // Position metrics
  FirstDomainPosition = 'P0', // Position of first queried domain
  SecondDomainPosition = 'P1', // Position of second queried domain
  ThirdDomainPosition = 'P2', // Position of third queried domain
  FourthDomainPosition = 'P3', // Position of fourth queried domain
  FifthDomainPosition = 'P4', // Position of fifth queried domain
  KeywordCount = 'Pc', // Number of keywords
  PositionDifference = 'Pd', // Difference between previous and current position
  Keyword = 'Ph', // Keyword bringing users to the website
  Position = 'Po', // Position in Google's top 100 results
  DomainPosition = 'Pp', // Position domain gets with a keyword
  ProductPrice = 'Pr', // Price of promoted product
  PositionType = 'Pt', // Type of position (regular or SERP Feature)

  // Ranking metrics
  SemrushRank = 'Rk', // Semrush rating of website popularity
  Relevance = 'Rr', // Relevance of result keyword to seed keyword

  // SERP and cost metrics
  SerpFeaturesCost = 'Sc', // Estimated price for PPC ads to rank in SERP Features
  SerpFeaturesCostChange = 'Scm', // Changes in organic traffic cost from SERP Features
  ProductListingAdKeywords = 'Sh', // Number of keywords for product listing ads
  ShopName = 'Sn', // Shop name
  UniqueProductListingAds = 'Sv', // Number of unique product listing ads
  SerpFeaturesKeywords = 'Sr', // Number of keywords where domain ranks in SERP Features
  BrandedSerpFeaturesKeywords = 'Srb', // Number of branded keywords in SERP Features
  LostSerpFeaturesKeywords = 'Srl', // Keywords no longer ranking in SERP Features
  SerpFeaturesKeywordsChange = 'Srm', // Changes in keywords with SERP Feature positions
  NewSerpFeaturesKeywords = 'Srn', // New keywords ranking in SERP Features
  SerpFeaturesTraffic = 'St', // Estimated organic traffic from SERP Features
  BrandedSerpFeaturesTraffic = 'Stb', // Traffic from SERP Features on branded keywords
  SerpFeaturesTrafficChange = 'Stm', // Changes in traffic from SERP Features

  // Traffic metrics
  TrafficCostPercentage = 'Tc', // Percentage of domain's total traffic cost from keyword
  EstimatedTraffic = 'Tg', // Estimated organic traffic from keyword
  TrendData = 'Td', // Interest in keyword during last 12 months
  OrganicTrafficChange = 'Tm', // Changes in organic traffic
  TrafficShare = 'Tr', // Share of traffic from keyword
  Timestamp = 'Ts', // UNIX Timestamp
  AdTitle = 'Tt', // Ad title

  // URL and change metrics
  OrganicTrafficCostChange = 'Um', // Changes in organic traffic cost
  AdId = 'Un', // Ad ID
  TargetUrl = 'Ur', // URL of target page
  VisibleUrl = 'Vu', // Visible URL

  // Position distribution
  OrganicPositionDistribution = 'Xn', // Organic Position Distribution
}

export const SemrushKeywordIntentMap = {
  '0': 'Commercial',
  '1': 'Informational',
  '2': 'Navigational',
  '3': 'Transactional',
} as const;

export type SemrushKeywordIntent =
  (typeof SemrushKeywordIntentMap)[keyof typeof SemrushKeywordIntentMap];

export type SemrushKeywordOverview = {
  keyword: string;
  volume: number;
  costPerClick: number;
  competition: number;
  results: number;
  intent: SemrushKeywordIntent;
  keywordDifficulty: number;
};

export type SemrushKeywordOrganicSearchData = {
  domain: string;
  url: string;
  keywordSerpFeatures: number[];
};

export type SemrushUrlOrganicKeyword = {
  keyword: string;
  position: number;
  searchVolume: number;
  costPerClick: number;
  competition: number;
  trafficPercent: number;
  trafficCost: number;
  numberOfResults: number;
  trends: number[];
};

export type SemrushUrlAdword = {
  keyword: string;
  position: number;
  title: string;
  text: string;
  visibleUrl: string;
  targetUrl: string;
  costPerClick: number;
  searchVolume: number;
  competition: number;
  trafficCost: number;
  trends: number[];
};

export type SemrushDomainAdword = {
  keyword: string;
  position: number;
  domainPosition: number;
  positionDifference: number;
  searchVolume: number;
  costPerClick: number;
  url: string;
  trafficShare: number;
  trafficCostPercent: number;
  competition: number;
  totalResults: number;
  trends: [];
};

export type SemrushDomainOrganicKeyword = {
  keyword: string;
  position: number;
  searchVolume: number;
  costPerClick: number;
  competition: number;
  trafficPercent: number;
  trafficCost: number;
  url?: string;
  trends?: number[];
};

export type SemrushDomainOrganicPage = {
  url: string;
  keywordCount: number;
  estimateTraffic: number;
  trafficShare: number;
  unknownIntentPositions: number;
  commercialIntentPositions: number;
  informationalIntentPositions: number;
  navigationalIntentPositions: number;
  transactionalIntentPositions: number;
  unknownIntentTraffic: number;
  commercialIntentTraffic: number;
  informationalIntentTraffic: number;
  navigationalIntentTraffic: number;
  transactionalIntentTraffic: number;
  serpFeaturesKeywords: number;
  serpFeaturesTraffic: number;
};
