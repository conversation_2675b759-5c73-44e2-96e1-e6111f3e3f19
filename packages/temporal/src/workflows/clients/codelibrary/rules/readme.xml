<!-- Generated on 2025-07-01T22:14:16.180Z -->
<workflow>
  <functionName>clientsCodelibraryRulesWorkflow</functionName>
  <displayName>Code Library Rules Generator</displayName>
  <path>clients/codelibrary/rules</path>
  <overview>Analyzes a given programming topic to generate comprehensive coding rules and best practices by conducting research, analyzing patterns, and synthesizing findings into structured guidance</overview>
  <flowChart><![CDATA[```mermaid
graph TD
    A[Start] --> B[Topic Analysis]
    B --> C[Conduct Research]
    C --> D{Parallel Analysis}
    D --> E[Analyze Technology]
    D --> F[Analyze Patterns]
    D --> G[Analyze Frameworks]
    E --> H[Generate Rules]
    F --> H
    G --> H
    H --> I[End]
```]]></flowChart>
  <activities>
    <activity>
      <functionName>topicAnalysis</functionName>
      <displayName>Topic Analysis</displayName>
      <description>Uses OpenAI to analyze the input topic and create a structured research plan including technology queries, pattern queries, main technology focus, and related technologies</description>
    </activity>
    <activity>
      <functionName>conductResearch</functionName>
      <displayName>Conduct Research</displayName>
      <description>Executes research queries using Perplexity API to gather comprehensive information about technologies, patterns, and best practices, including citation tracking</description>
    </activity>
    <activity>
      <functionName>analyzeTechnologyResearch</functionName>
      <displayName>Technology Research Analysis</displayName>
      <description>Processes research data to extract technology-specific best practices, conventions, frameworks, and tooling recommendations</description>
    </activity>
    <activity>
      <functionName>analyzePatternResearch</functionName>
      <displayName>Pattern Research Analysis</displayName>
      <description>Analyzes research data to identify coding patterns, naming conventions, error handling approaches, and testing/performance patterns</description>
    </activity>
    <activity>
      <functionName>frameworkAnalysis</functionName>
      <displayName>Framework Analysis</displayName>
      <description>Evaluates research data to determine recommended frameworks, common implementation patterns, and framework-specific best practices</description>
    </activity>
    <activity>
      <functionName>generateRules</functionName>
      <displayName>Rules Generation</displayName>
      <description>Synthesizes all analysis results to create comprehensive coding rules including configuration, best practices, and implementation guidelines specific to the topic</description>
    </activity>
  </activities>
</workflow>