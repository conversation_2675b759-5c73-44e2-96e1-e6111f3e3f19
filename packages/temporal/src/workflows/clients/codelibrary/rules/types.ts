import { z } from 'zod';

export const WorkflowInputSchema = z.object({
  topic: z.string(),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export const ResearchPlanSchema = z.object({
  techQueries: z.array(z.string()),
  patternQueries: z.array(z.string()),
  mainTechnology: z.string(),
  relatedTechnologies: z.array(z.string()),
});

export type ResearchPlan = z.infer<typeof ResearchPlanSchema>;

export const TechResearchSchema = z.object({
  bestPractices: z.array(z.string()),
  conventions: z.array(z.string()),
  frameworks: z.array(z.string()),
  tooling: z.array(z.string()),
});

export type TechResearch = z.infer<typeof TechResearchSchema>;

export const PatternResearchSchema = z.object({
  namingConventions: z.array(z.string()),
  errorHandling: z.array(z.string()),
  testingPatterns: z.array(z.string()),
  performancePatterns: z.array(z.string()),
});

export type PatternResearch = z.infer<typeof PatternResearchSchema>;

export const FrameworkAnalysisSchema = z.object({
  recommendedFrameworks: z.array(z.string()),
  commonPatterns: z.array(z.string()),
  bestPractices: z.array(z.string()),
});

export type FrameworkAnalysis = z.infer<typeof FrameworkAnalysisSchema>;

export const RulesSchema = z.object({
  name: z.string(),
  description: z.string(),
  language: z.string(),
  use_case: z.string(),
  complexity: z.string(),
  category: z.string(),
  configuration: z.string(),
});

export type Rules = z.infer<typeof RulesSchema>;

export type WorkflowOutput = Rules;
