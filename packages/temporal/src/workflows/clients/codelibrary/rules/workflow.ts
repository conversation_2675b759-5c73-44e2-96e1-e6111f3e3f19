import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { handleWorkflowError, validateWorkflowInput } from '@flow/sdk/lib/workflow-utils.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { WorkflowInputSchema } from './types.js';

// Todo—move to a different worker

const {
  topicAnalysis,
  conductResearch,
  analyzeTechnologyResearch,
  analyzePatternResearch,
  frameworkAnalysis,
  generateRules,
} = WorkflowScope.use<typeof activities>(import.meta.url, {
  startToCloseTimeout: '10 minute', // Extended from 5 minutes for busy server scenarios
});

export async function clientsCodelibraryRulesWorkflow(
  rawInput: WorkflowInput
): Promise<WorkflowOutput> {
  // Validate input
  const input = validateWorkflowInput(rawInput, WorkflowInputSchema);

  try {
    // Step 1: Analyze topic and create research plan
    const researchPlan = await topicAnalysis(input);

    // Step 2: Conduct all research with Perplexity
    const researchData = await conductResearch(researchPlan);

    // Step 3: Parallel analysis of research data
    const [techResearch, patternResearch, frameworkResearch] = await Promise.all([
      analyzeTechnologyResearch(researchData),
      analyzePatternResearch(researchData),
      frameworkAnalysis(researchData),
    ]);

    // Step 5: Generate comprehensive rules
    return await generateRules({
      topic: input.topic,
      techResearch,
      patternResearch,
      frameworkData: frameworkResearch,
    });
  } catch (error) {
    handleWorkflowError(error);
  }
}
