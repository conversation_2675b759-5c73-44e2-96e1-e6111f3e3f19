import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { topicAnalysisPrompt, researchPrompt, rulesGenerationPrompt } from './prompts.js';
import type {
  WorkflowInput,
  ResearchPlan,
  TechResearch,
  PatternResearch,
  FrameworkAnalysis,
  Rules,
} from './types.js';
import {
  ResearchPlanSchema,
  TechResearchSchema,
  PatternResearchSchema,
  FrameworkAnalysisSchema,
  RulesSchema,
} from './types.js';

const { perplexityApiClient, openaiChatService } = workerModule;

export async function topicAnalysis(input: WorkflowInput): Promise<ResearchPlan> {
  const messages = await threadTemplate(topicAnalysisPrompt, {
    topic: input.topic,
  });

  return await openaiChatService.request<ResearchPlan>({
    messages,
    model: 'o4-mini',
    schema: ResearchPlanSchema,
    schemaName: 'research_plan_schema',
  });
}

export async function conductResearch(plan: ResearchPlan): Promise<string> {
  const allQueries = [...plan.techQueries, ...plan.patternQueries];

  const researchResults = await Promise.all(
    allQueries.map(async (query) => {
      const perplexityResponse = await perplexityApiClient.chat({
        model: 'sonar-pro',
        messages: [{ role: 'user', content: query }],
        return_citations: true,
      });

      return {
        query,
        result: perplexityResponse.choices[0].message.content,
        sources: perplexityResponse.citations?.map((source: string) => source.trim()),
      };
    })
  );

  const formattedResults = researchResults
    .map(
      ({ query, result, sources }) =>
        `Question: ${query}\nResult: ${result}\nSources: ${sources?.join(', ')}`
    )
    .join('\n\n');

  return formattedResults;
}

export async function analyzeTechnologyResearch(research: string): Promise<TechResearch> {
  const messages = await threadTemplate(researchPrompt, {
    research,
    focus: 'technology best practices, frameworks, and tooling',
  });

  return await openaiChatService.request<TechResearch>({
    messages,
    model: 'o4-mini',
    schema: TechResearchSchema,
    schemaName: 'tech_research_schema',
  });
}

export async function analyzePatternResearch(research: string): Promise<PatternResearch> {
  const messages = await threadTemplate(researchPrompt, {
    research,
    focus: 'coding patterns, conventions, and best practices',
  });

  return await openaiChatService.request<PatternResearch>({
    messages,
    model: 'o4-mini',
    schema: PatternResearchSchema,
    schemaName: 'pattern_research_schema',
  });
}

export async function frameworkAnalysis(research: string): Promise<FrameworkAnalysis> {
  const messages = await threadTemplate(researchPrompt, {
    research,
    focus: 'technology best practices, frameworks, and tooling',
  });

  return await openaiChatService.request<FrameworkAnalysis>({
    messages,
    model: 'o4-mini',
    schema: FrameworkAnalysisSchema,
    schemaName: 'framework_analysis_schema',
  });
}

export async function generateRules(input: {
  topic: string;
  techResearch: TechResearch;
  patternResearch: PatternResearch;
  frameworkData: FrameworkAnalysis;
}): Promise<Rules> {
  const messages = await threadTemplate(rulesGenerationPrompt, {
    topic: input.topic,
    techResearch: JSON.stringify(input.techResearch, null, 2),
    patternResearch: JSON.stringify(input.patternResearch, null, 2),
    frameworkData: JSON.stringify(input.frameworkData, null, 2),
  });

  return await openaiChatService.request<Rules>({
    messages,
    model: 'o3',
    schema: RulesSchema,
    schemaName: 'rules_schema',
  });
}

export default WorkflowScope.register(import.meta.url, {
  topicAnalysis,
  conductResearch,
  analyzeTechnologyResearch,
  analyzePatternResearch,
  frameworkAnalysis,
  generateRules,
});
