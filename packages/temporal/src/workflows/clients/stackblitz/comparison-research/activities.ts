import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { questionsPrompt, reportPrompt } from './prompts.js';
import { QuestionsSchema } from './types.js';

const { openaiChatService, anthropicChatService } = workerModule;

export async function generateQuestions(input: {
  name: string;
  url: string;
  description: string;
}): Promise<string[]> {
  const messages = await threadTemplate(questionsPrompt, {
    name: input.name,
    url: input.url,
    description: input.description,
  });

  const result = await openaiChatService.request<{ questions: string[] }>({
    messages,
    model: 'o3',
    schema: QuestionsSchema,
    schemaName: 'questions_schema',
  });

  const enrichedQuestions = result.questions.map(
    (questionSection) =>
      `From the perspective of a Web Designer & Developer we are researching ${input.name}.
  This is what ${input.name} does:
  ${input.description}

  Here's the section of questions I need you to research and answer for me:
  <question-section>
  ${questionSection}
  </question-section>

  Use your best judgement to find the right sources, for example if the questions might need user sentiment make sure to prioritize Reddit, Youtube, and other social channels. If the questions are about certain features, check on official docs, support pages, and other technical channels.`
  );

  return enrichedQuestions;
}

export async function generateReport(researchData: string): Promise<string> {
  const { system, messages } = await threadTemplate(
    reportPrompt,
    {
      researchData,
    },
    { format: 'anthropic', strict: true }
  );

  return await anthropicChatService.request<string>({
    system,
    messages,
    model: 'claude-3-7-sonnet-latest',
    thinking: { type: 'enabled', budget_tokens: 60000 },
  });
}

export default WorkflowScope.register(import.meta.url, {
  generateQuestions,
  generateReport,
});
