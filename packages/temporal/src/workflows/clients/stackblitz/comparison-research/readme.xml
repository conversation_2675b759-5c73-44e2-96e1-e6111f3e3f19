<?xml version="1.0" encoding="UTF-8"?>
<workflow>
  <functionName>clientsStackblitzComparisonResearchWorkflow</functionName>
  <displayName>Competitor Comparison Research</displayName>
  <path>clients/stackblitz/comparison-research</path>
  <overview>
    A workflow that performs comprehensive competitor analysis by generating targeted research questions, conducting deep research through a specialized research workflow, and synthesizing findings into a detailed report. The workflow takes competitor information (name, URL, and description) as input and returns a structured markdown report containing competitive insights organized into multiple analysis categories.
  </overview>
  <flowChart>
    ```mermaid
    graph TD
      A[Start] --> B[Generate Research Questions]
      B --> C[Execute Deep Research Workflow]
      C --> D[Generate Analysis Report]
      D --> E[End]
    ```
  </flowChart>
  <activities>
    <activity>
      <functionName>generateQuestions</functionName>
      <displayName>Generate Research Questions</displayName>
      <description>
        Generates themed research questions using GPT-4 based on competitor information.

        **Key Processing Steps:**
        - Takes competitor name, URL, and description as input
        - Uses OpenAI Chat API with o3 model (GPT-4)
        - Processes questions through a comprehensive question bank template
        - Enriches questions with research context and source suggestions
        - Returns array of themed question sections

        **Key Prompt Logic:**
        - System prompt establishes researcher persona
        - Uses extensive question bank covering 20 analysis themes
        - Questions are customized with competitor context
        - Adds source guidance for each question section
        - Validates output with Zod schema
      </description>
    </activity>
    <activity>
      <functionName>deepResearcherWorkflow</functionName>
      <displayName>Execute Deep Research</displayName>
      <description>
        Child workflow that performs deep research on generated questions.

        **Key Processing Steps:**
        - Takes questions array, context, topic name, and model type
        - Uses Sonar deep research model for analysis
        - Returns consolidated research findings
      </description>
    </activity>
    <activity>
      <functionName>generateReport</functionName>
      <displayName>Generate Analysis Report</displayName>
      <description>
        Synthesizes research data into comprehensive markdown report.

        **Key Processing Steps:**
        - Takes consolidated research data as input
        - Uses Anthropic Claude-3 Sonnet model for report generation
        - Returns formatted markdown report with sections for:
          - Executive Summary
          - Features Overview
          - Detailed Analysis
          - Community Sentiment
          - Pricing Analysis

        **Key Prompt Logic:**
        - System prompt establishes professional researcher persona
        - Uses structured report template
        - Implements extensive markdown formatting
        - Processes with high token budget (60,000) for detailed output
      </description>
    </activity>
  </activities>
</workflow>