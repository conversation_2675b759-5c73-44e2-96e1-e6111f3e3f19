import { executeChild } from '@temporalio/workflow';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { handleWorkflowError } from '@flow/sdk/lib/workflow-utils.js';

const { generateQuestions, generateReport } = WorkflowScope.use<typeof activities>(
  import.meta.url,
  {
    startToCloseTimeout: '15 minute',
    retry: {
      initialInterval: '1 second',
      maximumInterval: '1 minute',
      backoffCoefficient: 2,
      maximumAttempts: 100,
      nonRetryableErrorTypes: ['UNRECOVERABLE_ERROR'],
    },
  }
);

export async function clientsStackblitzComparisonResearchWorkflow(
  input: WorkflowInput
): Promise<WorkflowOutput> {
  try {
    const questions = await generateQuestions({
      name: input.competitorName,
      url: input.competitorUrl,
      description: input.competitorDescription,
    });

    const researchData = await executeChild('deepResearcherWorkflow', {
      workflowId: `deep-research-${Date.now()}-${Math.random().toString(36).substring(7)}`,
      args: [
        {
          questions,
          context: input.competitorDescription,
          topic: input.competitorName,
          model: 'sonar-deep-research',
        },
      ],
    });

    return await generateReport(researchData);
  } catch (error) {
    handleWorkflowError(error);
  }
}
