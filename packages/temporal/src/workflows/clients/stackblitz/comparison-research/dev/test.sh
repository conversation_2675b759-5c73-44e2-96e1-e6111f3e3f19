curl --location 'http://localhost:2000/api/workflow' \
--header 'Content-Type: application/json' \
--data '{
    "workflowName": "clientsStackblitzBoltCompetitorResearchWorkflow",
    "args": {
      "competitorName": "Cursor",
      "competitorUrl": "https://www.cursor.com",
      "competitorDescription": "Cursor is a code editor that allows you to write code in natural language. It is a powerful tool for developers who want to write code faster and more efficiently."
    },
    "operation": "execute"
}'
