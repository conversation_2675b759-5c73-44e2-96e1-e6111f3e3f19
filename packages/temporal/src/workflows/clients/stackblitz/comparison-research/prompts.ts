export const questionsPrompt = [
  {
    role: 'system',
    content: `
## Role
You are a researcher tasked with creating a comprehensive list of research questions about a product/website/service, organized by themes.

## Task
You'll be given the name and description of the product/website/service in question. You need to generate themed sections of questions using the provided question bank as a guide. Each section should contain 3-5 most important questions for that theme.

## Output Format
Return an array of themed question sections, where each element is a string containing:
1. A section heading with the theme (e.g., "## Quick-Start Snapshot")
2. A bulleted list of self-contained questions related to that theme

## Example Output
[
  "## Quick-Start Snapshot about Lovable.dev
- What is the one-sentence elevator pitch of Lovable.dev?
- Who is Lovable.dev's primary audience (designers, full-stack devs, hobbyists, enterprise teams)?
- How does Lovable.dev position itself against competitors (unique selling points)?
- What is Lovable.dev's current maturity — alpha, beta, GA, or open-source community project?",

  "## AI & Prompt Workflow
- Does Lovable.dev support natural-language prompting or chat-style commands? If yes, which models power it?
- Can the AI create, edit, delete, or refactor files automatically on Lovable.dev?
- How does Lovable.dev surface error explanations or auto-fixes?
- Are there visual prompt helpers on Lovable.dev (slash commands, templates, GUI pickers)?
- Can you have multi-turn conversations that retain full project context in Lovable.dev?"
]
    `,
  },
  {
    role: 'user',
    content: `
<question-bank>
### 1 · Quick-Start Snapshot

- What is the one-sentence elevator pitch of the tool?
- Who is the primary audience (designers, full-stack devs, hobbyists, enterprise teams)?
- How does the platform position itself against competitors (unique selling points)?
- What is the current maturity — alpha, beta, GA, or open-source community project?

### 2 · AI & Prompt Workflow

- Does the platform support natural-language prompting or chat-style commands? If yes, which models (GPT-4, Claude, proprietary) power it?
- Can the AI **create, edit, delete, or refactor** files automatically?
- Is there a "prompt enhancement" or "suggested prompt" feature?
- What guardrails exist (token quotas, rate limits, model fallback)?
- Can you have **multi-turn** conversations that retain full project context?
- How does the system surface **error explanations or auto-fixes**?
- Are there visual prompt helpers (slash commands, templates, GUI pickers)?
- Does it expose an API / SDK to programmatically trigger AI operations?

### 3 · Framework & Language Support

- Which **frontend frameworks** are first-class (React, Next.js, SvelteKit, Astro, Solid, Qwik, Preact)?
- Which **backend runtimes** or full-stack frameworks are supported (Node/Express, NestJS, Remix, Ruby, Go, Python/FastAPI)?
- Does it scaffold multi-framework **monorepos** (TurboRepo, Nx)?
- Native TypeScript support? ESLint / Prettier presets?
- Build tools: Vite, Webpack, SWC/Rspack? How are configs customized?
- Ability to mix vanilla HTML/CSS/JS?
- Are **edge-runtime** or serverless frameworks supported (Cloudflare Workers, Deno, Bun)?

### 4 · Design & Prototyping

- Figma-to-code or Sketch/Framer imports? Fidelity level?
- Built-in design-system templates (Material UI, Shadcn, Radix)?
- Visual inspectors / component pickers for **selective AI edits**?
- Theme, tokens, or Style Dictionary synchronization?
- Animation editing (GSAP, Framer Motion, CSS keyframes) — is there a GUI?
- CSS frameworks (Tailwind vX, CSS-in-JS, UnoCSS) — auto-class suggestion?
- Accessibility linting (a11y) or color-contrast checkers?
- Can designers hand off annotated mock-ups and have components generated?

### 5 · Development Environment & IDE

- Is the IDE **browser-based, desktop, VS Code extension, or local CLI**?
- WebContainers / containerized VM? CPU/RAM limits?
- IntelliSense depth — semantic autocomplete, inline docs?
- Built-in terminal with real Node/npm/yarn/pnpm?
- Hot-module replacement or fast refresh for major frameworks?
- Device emulation (mobile viewports, ARIA live regions)?
- Source-map-aware DevTools and network inspectors?
- Local-file sync or offline editing story?

### 6 · Integrations & Ecosystem

- Databases: Supabase, PlanetScale, Neon, Firestore?
- Auth providers: Auth0, Clerk, Supabase Auth, custom OAuth?
- Payments: Stripe, Lemon Squeezy, Paddle?
- CMS / Headless: Sanity, Contentful, Storyblok?
- Cloud functions / serverless: Netlify Functions, Vercel Edge, AWS Lambda?
- Third-party APIs (SendGrid, Twilio, Algolia) — scaffolding or recipes?
- CI/CD hooks or GitHub Actions templates?
- Plugin marketplace or extension APIs?

### 7 · Deployment & Hosting

- One-click deploy targets (Netlify, Vercel, Render, Fly.io, Cloudflare Pages)?
- Custom domains, SSL, HTTP/2 or QUIC support?
- SSR/SSG options per framework (Next ISR, Astro SSG, Remix edge)?
- Preview URLs / branch deploys / staging slots?
- CDN strategy and cache-invalidation controls?
- Support for Docker export or infra-as-code (Terraform, Pulumi)?
- Are **environment variables** and secrets managed securely?

### 8 · Collaboration & Version Control

- Real-time pair-programming or Google-Docs-style cursors?
- Role-based access control (viewer, editor, owner)?
- Git integration depth (push/pull, merge conflicts, PRs)?
- Inline code comments or annotation threads?
- Project history / time-travel diff view?
- Audit logs for enterprise?

### 9 · Mobile & Cross-Platform

- Generates React Native / Expo / Flutter / Kotlin Multiplatform?
- Live device preview or QR code linking?
- OTA updates, EAS builds, TestFlight / Play Store pipelines?
- Shared UI kit between web and mobile (design system parity)?
- Mobile-specific AI prompts (e.g., "add iOS haptics")?

### 10 · Performance, Reliability & Security

- Typical cold-start times in dev and prod?
- Build-cache or remote cache features?
- DDoS protection / WAF offerings if hosted?
- Supply-chain security (Snyk scans, Sigstore, lockfile verification)?
- SOC 2, ISO 27001, GDPR compliance docs?
- Rate limits on AI requests and background tasks?

### 11 · Pricing & Licensing

- Free tier limits (projects, AI tokens, runtime hours)?
- Paid plans (seat-based, usage-based, enterprise SSO)?
- Overage pricing for AI tokens or build minutes?
- SLA uptime guarantees on paid tiers?
- Is any part open-source? What license (MIT, Apache 2, BSL)? Dual-license quirks?

### 12 · Learning Curve & Community

- Quality of official docs, tutorials, example repos?
- Community activity — Discord, forums, GitHub issues velocity?
- Presence of **template galleries** or starter kits?
- Office hours, live streams, or conference talks?
- Adoption metrics (monthly active projects, GitHub stars)?
- Notable showcase sites or apps built with the platform?

### 13 · Accessibility & Internationalization

- Built-in i18n frameworks or helpers?
- Right-to-left layout support, locale-aware date/number formatting?
- WCAG compliance tools? Automated audits?
- AI translation suggestions for UI copy?

### 14 · Limitations, Bugs & Pain Points

- Known issues list from GitHub or community boards?
- Edge cases (large monorepos, binary assets, GraphQL codegen)?
- AI hallucination frequency and mitigation tips?
- Browser compatibility quirks (Safari WebContainer caveats)?
- Backup/export workflows if you outgrow the tool?

### 15 · Experimental & Roadmap Items

- Alpha/Beta flags (voice-to-code, multi-modal inputs, VR preview)?
- Public roadmap links or GitHub Projects boards?
- Recently shipped features (last 90 days) to gauge velocity?
- Partnerships or upcoming integrators (e.g., with Hugging Face models)?

### 16 · Competitive Positioning & Differentiators

- What core problems does this tool solve **better** or uniquely?
- Where does it lag behind Bolt.new or the other two platforms?
- Ecosystem overlap — can it co-exist with VS Code, Codespaces, or AI pair-programmers like Cursor?
- Enterprise friendliness vs indie hacker focus?

### 17 · Use-Case Deep Dives

- How does a **web-first marketing site** workflow look?
- How does a **SaaS dashboard** with auth + payments come together?
- How fast can you replicate a **Figma landing-page design**?
- What's the experience building a **mobile companion app**?

### 18 · Metrics & KPI Tracking

- Time-to-first-preview from blank prompt?
- Average prompt tokens per successful feature?
- Build & deploy success rates (CI green / failed)?
- Performance metrics of generated sites (LCP, CLS out-of-the-box)?
- Community adoption curves (Google Trends, Stack Overflow tags)?

### 19 · Source Verification & Research Hygiene

- Official docs sections to bookmark?
- Release notes / changelog cadence?
- Blog posts, demos, keynote recordings?
- Independent reviews, Twitter threads, Reddit discussions?
- Benchmarks or academic papers (if any)?

### 20 · Migration & Exit Strategy

- How easy is it to export code to a local repo?
- Vendor-lock factors (proprietary runtimes, closed APIs)?
- Import tools from GitHub or other IDEs back into the platform?
</question-bank>

<product-name>
{{name}}
</product-name>

<product-description>
{{description}}
</product-description>

<product-url>
{{url}}
</product-url>

Please create themed question sections about {{name}} using the question bank above. We want all themes, and for each theme include the most relevant questions for this product/website/service, making sure they are self-contained by replacing generic terms with "{{name}}".
    `,
  },
];

export const reportPrompt = [
  {
    role: 'system',
    content: `
You are researcher and your task is to take the research data and create a comprehensive report matching the example report below as the minimum standard, and add more depth and detail to the report where possible. We want a very detailed report that will help us understand the product/website/service in depth.

It's ok if the report is longer than the example report below, we want as much detail as possible. But we do want at least all the sections and questions from the example report below.

## Example Report
<example-report>
# <Platform Name> Feature Overview Template

> **How to use:** Replace the bracketed placeholders (e.g. \`<Platform Name>\`, \`<Strength 1>\`) with data for your target platform. Gather information from official documentation, user forums (Reddit, Discord, etc.), GitHub issues, YouTube reviews, and hands‑on testing. Integrate pros, cons, bugs, and user complaints directly into the "Limitations" columns so they sit beside each feature description.

---

## Executive Summary

**Platform Snapshot**
*One‑paragraph overview that explains what \`<Platform Name>\` is, its core value proposition for web designers/developers, and any headline caveats.*

### Key Strengths

* \`<Strength 1>\`
* \`<Strength 2>\`
* \`<Strength 3>\`

### Key Limitations / Risks

* \`<Limitation 1>\`
* \`<Limitation 2>\`
* \`<Limitation 3>\`

---

# Features Overview

## 1. AI & Prompt Workflow

| Feature                                  | Description (What it does & developer perspective) | Strengths / Benefits | Limitations, Bugs & User Complaints |
| ---------------------------------------- | -------------------------------------------------- | -------------------- | ----------------------------------- |
| AI‑Powered Code Generation               |                                                    |                      |                                     |
| Multi‑Turn Conversation & Context Memory |                                                    |                      |                                     |
| Prompt Engineering Best Practices        |                                                    |                      |                                     |

---

## 2. Development Environment & Tooling

| Feature                                | Description | Strengths | Limitations & Bugs |
| -------------------------------------- | ----------- | --------- | ------------------ |
| Zero‑Setup Cloud IDE                   |             |           |                    |
| Package Management (npm / yarn / pnpm) |             |           |                    |
| Live Preview & Testing                 |             |           |                    |
| Collaboration & Sharing                |             |           |                    |
| Version Control & Rollback             |             |           |                    |

---

## 3. Framework & Language Support

| Area                                              | Supported Stacks / Notes | Strengths | Limitations |
| ------------------------------------------------- | ------------------------ | --------- | ----------- |
| Front‑End Frameworks (React, Vue, Svelte, etc.)   |                          |           |             |
| Back‑End Frameworks (Express, Next.js API, etc.)  |                          |           |             |
| Mobile / Cross‑Platform (e.g. Expo, React Native) |                          |           |             |
| UI Libraries & Styling (Tailwind, MUI, etc.)      |                          |           |             |

---

## 4. Design Integration

| Feature                       | Description | Strengths | Limitations & Complaints |
| ----------------------------- | ----------- | --------- | ------------------------ |
| Figma / Design‑to‑Code Import |             |           |                          |
| Image / Screenshot‑to‑Code    |             |           |                          |
| Asset Management Workflow     |             |           |                          |

---

## 5. Database & Backend Integration

| Feature                                               | Description | Strengths | Limitations & Bugs |
| ----------------------------------------------------- | ----------- | --------- | ------------------ |
| Built‑in BaaS (e.g. Supabase, Firebase)               |             |           |                    |
| Serverless / Edge Functions                           |             |           |                    |
| Third‑Party API Integrations (Stripe, SendGrid, etc.) |             |           |                    |

---

## 6. Deployment & Hosting

| Feature                        | Description | Strengths | Limitations & Complaints |
| ------------------------------ | ----------- | --------- | ------------------------ |
| One‑Click Deployment Provider  |             |           |                          |
| Application Hosting & Live URL |             |           |                          |
| Export / Self‑Host Options     |             |           |                          |

---

## 7. Community Sentiment

\`<Quotes from Reddit, Youtube, Discord, etc. >\`

---

## 8. Cost Model & Pricing

\`<Pricing tiers, token limits, overage costs>\`

# Detailed Report

Proceed to create a super long article with all details from the research data, we want extensive details, information about news/issues, quotes from user reviews, put almost everything in the research data into the report all  the questions and the results, just format it a little.
</example-report>
  `,
  },
  {
    role: 'user',
    content: `
<research-data>
{{researchData}}
</research-data>

Now generate the report for me.

Important: Don't save on tokens, give as much detail as possible, especially for the \`#Detailed Report\` section.
`,
  },
];
