import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { comparisonArticlePrompt } from './prompts.js';
import type { WorkflowInput } from './types.js';

const { openaiChatService } = workerModule;

export async function generateArticle(input: WorkflowInput): Promise<string> {
  const messages = await threadTemplate(
    comparisonArticlePrompt,
    {
      competitorName: input.competitorName,
      competitorData: input.competitorData,
      clientData: input.clientData,
      context: input.context,
      template: input.template,
    },
    { format: 'openai', strict: true }
  );

  const result = await openaiChatService.request<string>({
    messages,
    model: 'o3',
  });

  return result;

  // const { system, messages } = await threadTemplate(
  //   comparisonArticlePrompt,
  //   {
  //     competitorName: input.competitorName,
  //     competitorData: input.competitorData,
  //     clientData: input.clientData,
  //     context: input.context,
  //     template: input.template,
  //   },
  //   { format: 'anthropic', strict: true }
  // );

  // const result = await anthropicChatService.request<string>({
  //   system,
  //   messages,
  //   model: 'claude-3-7-sonnet-latest',
  // });

  // return result;
}

export default WorkflowScope.register(import.meta.url, {
  generateArticle,
});
