export const comparisonArticlePrompt = [
  {
    role: 'system',
    content: `
I have these two long form research articles and I need to a comparison article similar to the template attached that will compare Bolt to {{competitorName}} (a competitor).

Your task will be to generate the article for me.

I want you to first read the data on Bolt.new (which is the company I work for) and then read the data on the competitor ({{competitorName}}). Analyze the context carefully to identify which persona would be the best fit for this particular comparison but don't mention it in the article, don't make the persona obvious. Then learn our writing style based on the selected persona and tone of voice attached, and use the draft template as inspiration to write a comprehensive article comparing Bolt to {{competitorName}}.

Identify if the competitor is a direct competitor or a complementary product, and if it's a direct competitor, pitch Bolt.new as the better solution in a convincing way without being too salesy.

Make the intro tight and punchy, and the conclusion should be a call to action for the reader to try Bolt.new like the template suggests.
    `,
  },
  {
    role: 'user',
    content: `
## Competitor Info

<competitorInfo>
Competitor Name: {{competitorName}}
Competitor Research Data: {{competitorData}}
</competitorInfo>

## Bolt.new Research Data

<boltInfo>
{{clientData}}
</boltInfo>

## Context with tone of voice and persona

<context-with-tone-of-voice-and-persona>
{{context}}
</context-with-tone-of-voice-and-persona>

## Article Draft Template to follow

<template>
{{template}}
</template>

Now generate the article for me.
`,
  },
];
