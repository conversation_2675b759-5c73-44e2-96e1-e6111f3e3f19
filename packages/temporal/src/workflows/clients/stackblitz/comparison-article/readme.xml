<?xml version="1.0" encoding="UTF-8"?>
<workflow>
  <functionName>clientsStackblitzComparisonArticleWorkflow</functionName>
  <displayName>Stackblitz Competitor Comparison Article Generator</displayName>
  <path>clients/stackblitz/comparison-article</path>
  <overview>
    A workflow that generates AI-powered comparison articles between Bolt.new and competitor products. It processes structured input data including competitor information, Bolt.new details, and formatting requirements to produce professional comparison content using OpenAI's GPT model. The workflow handles input validation, content generation, and error management while ensuring consistent article structure and tone.
  </overview>
  <flowChart>
    ```mermaid
    graph TD
      A[Start] --> B[Input Validation]
      B --> C[Generate Article Activity]
      C --> D[Thread Template Processing]
      D --> E[OpenAI API Call]
      E --> F[Return Article]
      F --> G[End]
    ```
  </flowChart>
  <activities>
    <activity>
      <functionName>generateArticle</functionName>
      <displayName>Comparison Article Generator</displayName>
      <description>
        Generates a detailed comparison article using OpenAI's GPT model with structured prompt engineering.

        **External Services:**
        - OpenAI Chat Service (model: o3)
        - Thread Template Processor
        
        **Key Processing Steps:**
        1. Processes input data through threadTemplate utility
        2. Constructs message thread with competitor and Bolt.new data
        3. Makes API call to OpenAI with structured format
        4. Returns formatted article content

        **Key Prompt Logic:**
        - System prompt establishes article comparison context
        - Implements structured analysis approach:
          - Processes Bolt.new data
          - Analyzes competitor information
          - Determines competitor relationship (direct/complementary)
          - Adapts to specified persona and tone
          - Follows template structure
        - Ensures balanced comparison with strategic positioning
        - Maintains professional tone while highlighting Bolt.new advantages
        
        **Input Schema:**
        - competitorName: string
        - competitorData: string
        - clientData: string
        - context: string
        - template: string

        **Output Schema:**
        - article: string
      </description>
    </activity>
  </activities>
</workflow>