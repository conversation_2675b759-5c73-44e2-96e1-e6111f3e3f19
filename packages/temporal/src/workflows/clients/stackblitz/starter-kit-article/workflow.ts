import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { handleWorkflowError } from '@flow/sdk/lib/workflow-utils.js';

const { generateArticleBody, generateIntroAndClosing } = WorkflowScope.use<typeof activities>(
  import.meta.url
);

export async function clientsStackblitzBoltPromptArticleWorkflow(
  input: WorkflowInput
): Promise<WorkflowOutput> {
  try {
    const articleBody = await generateArticleBody(input);
    const { introduction, conclusion } = await generateIntroAndClosing({
      ...input,
      articleBody,
    });

    return {
      article: `${introduction}\n\n${articleBody}\n\n${conclusion}`,
    };
  } catch (error) {
    handleWorkflowError(error);
  }
}
