import { z } from 'zod';

export const WorkflowInputSchema = z.object({
  context: z.string(),
  prompt: z.string(),
  category: z.string().optional(),
  keyword: z.string().optional(),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export const ArticleBodySchema = z.object({
  content: z.string(),
});

export const IntroClosingSchema = z.object({
  introduction: z.string(),
  conclusion: z.string(),
});

export type WorkflowOutput = {
  article: string;
};
