<?xml version="1.0" encoding="UTF-8"?>
<workflow>
  <functionName>clientsStackblitzBoltPromptArticleWorkflow</functionName>
  <displayName>Starter Kit Article Generator</displayName>
  <path>clients/stackblitz/starter-kit-article</path>
  <overview>
    A workflow that generates tutorial content explaining how to customize Bolt.new Starter Kit prompts. The workflow processes input containing AI context (personas and styleguide), prompt content, and optional category/keyword parameters. It generates a complete article with customization instructions, leveraging both Anthropic and OpenAI models for different sections. The content is specifically structured to help non-technical users understand how to modify starter kit prompts for their needs.
  </overview>
  <flowChart>
    ```mermaid
    graph TD
      A[Start] --> B[Generate Article Body with Claude]
      B --> C[Generate Intro/Conclusion with GPT]
      C --> D[Combine Article Sections]
      D --> E[Return Complete Article]
    ```
  </flowChart>
  <activities>
    <activity>
      <functionName>generateArticleBody</functionName>
      <displayName>Generate Tutorial Body Content</displayName>
      <description>
        Generates the main tutorial content using Anthropic's Claude model.

        **Key Processing Steps:**
        - Takes WorkflowInput containing context, prompt, and optional category
        - Uses threadTemplate to format prompt for Anthropic
        - Makes API call to claude-3-7-sonnet-latest
        - Returns formatted article body as string

        **Key Prompt Logic:**
        - Sets AI role as Bolt.new copywriter
        - Analyzes target audience from provided personas
        - Breaks down prompt customization into logical steps
        - Focuses on non-technical explanations
        - Includes prompt engineering tips
        - Maintains supported tech stack constraints
        - Excludes introduction and conclusion sections
      </description>
    </activity>
    <activity>
      <functionName>generateIntroAndClosing</functionName>
      <displayName>Generate Introduction and Conclusion</displayName>
      <description>
        Creates introduction and conclusion sections using OpenAI's GPT model.

        **Key Processing Steps:**
        - Takes extended WorkflowInput including articleBody
        - Uses threadTemplate for specialized intro/conclusion prompt
        - Makes API call to OpenAI o3 model
        - Validates response using IntroClosingSchema
        - Returns structured object with introduction and conclusion

        **Key Prompt Logic:**
        - Sets AI role as introduction/conclusion specialist
        - Analyzes main article body for context
        - Creates engaging 2-3 paragraph introduction
        - Writes concise 1-2 paragraph conclusion
        - Incorporates SEO keywords when provided
        - Focuses on starter kit value proposition
        - Maintains assumption of Bolt knowledge
        - Avoids technical stack modifications
      </description>
    </activity>
  </activities>
</workflow>