import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { articleBodyPrompt, introClosingPrompt } from './prompts.js';
import type { WorkflowInput } from './types.js';
import { IntroClosingSchema } from './types.js';

const { openaiChatService, anthropicChatService } = workerModule;

export async function generateArticleBody(input: WorkflowInput): Promise<string> {
  const { system, messages } = await threadTemplate(
    articleBodyPrompt,
    {
      context: input.context,
      prompt: input.prompt,
      category: input.category,
    },
    { format: 'anthropic' }
  );

  const result = await anthropicChatService.request<string>({
    system,
    messages,
    model: 'claude-3-7-sonnet-latest',
  });

  return result;
}

export async function generateIntroAndClosing(input: WorkflowInput & { articleBody: string }) {
  const messages = await threadTemplate(introClosingPrompt, {
    context: input.context,
    prompt: input.prompt,
    body: input.articleBody,
    category: input.category,
    keyword: input.keyword,
  });

  return await openaiChatService.request<{ introduction: string; conclusion: string }>({
    messages,
    model: 'o3',
    schema: IntroClosingSchema,
    schemaName: 'intro_closing_schema',
  });
}

export default WorkflowScope.register(import.meta.url, {
  generateArticleBody,
  generateIntroAndClosing,
});
