curl --location 'http://localhost:2000/api/workflow' \
--header 'Content-Type: application/json' \
--data '{
    "workflowName": "clientsStackblitzBoltStarterKitsWorkflow",
    "args": {
        "request": "We need a task management app for remote teams that tracks project progress, assigns tasks to team members, and visualizes deadlines with a Kanban board. It should include user roles (admin, manager, member), task prioritization, file attachments, activity logs, and automated notifications. The interface should be clean and intuitive with a responsive design for both desktop and mobile use."
    },
    "operation": "execute"
}'
