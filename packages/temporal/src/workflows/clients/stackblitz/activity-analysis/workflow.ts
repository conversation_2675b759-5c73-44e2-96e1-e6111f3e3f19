import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { handleWorkflowError } from '@flow/sdk/lib/workflow-utils.js';

const { summarizeActivity, categorizeActivity } = WorkflowScope.use<typeof activities>(
  import.meta.url
);

export async function clientsStackblitzActivityAnalysisWorkflow(
  input: WorkflowInput
): Promise<WorkflowOutput> {
  try {
    const summary = await summarizeActivity(input);

    let categorization = null;
    if (input.categories) {
      categorization = await categorizeActivity({
        summary: summary.summary,
        categories: input.categories,
      });
    }

    return {
      user_id: input.user_id,
      activity: input.activity,
      summary: summary.summary,
      ...(categorization && {
        category: categorization.category,
        sub_category: categorization.sub_category,
      }),
    };
  } catch (error) {
    handleWorkflowError(error);
  }
}
