import { z } from 'zod';

export const WorkflowInputSchema = z.object({
  user_id: z.number(),
  activity: z.string(),
  categories: z.array(z.string()).optional(),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export const ActivitySummarySchema = z.object({
  summary: z.string(),
});

export type ActivitySummary = z.infer<typeof ActivitySummarySchema>;

export const ActivityCategorizationSchema = z.object({
  category: z.string(),
  sub_category: z.string(),
});

export type ActivityCategorization = z.infer<typeof ActivityCategorizationSchema>;

export type WorkflowOutput = {
  user_id: number;
  activity: string;
  summary: string;
  category?: string;
  sub_category?: string;
};
