<?xml version="1.0" encoding="UTF-8"?>
<workflow>
  <functionName>clientsStackblitzActivityAnalysisWorkflow</functionName>
  <displayName>Activity Analysis Workflow</displayName>
  <path>clients/stackblitz/activity-analysis</path>
  <overview>
    A workflow that analyzes user activity logs to generate structured insights using GPT-4. It processes activity text to create a chronological summary and optionally categorizes the activity into predefined categories. The workflow accepts a user ID, activity log text, and optional category array as inputs. It returns a structured response containing the original inputs, an AI-generated summary, and when applicable, category/sub-category classifications. The analysis specifically focuses on capturing project evolution, technical decisions, and key progress points in the user's development journey.
  </overview>
  <flowChart>
    ```mermaid
    graph TD
      A[Start] --> B[Input Validation]
      B --> C[Summarize Activity via GPT-4]
      C --> D{Categories Provided?}
      D -->|Yes| E[Categorize via GPT-4-mini]
      D -->|No| F[Return Summary Only]
      E --> G[Return Summary + Categories]
      F --> H[End]
      G --> H[End]
    ```
  </flowChart>
  <activities>
    <activity>
      <functionName>summarizeActivity</functionName>
      <displayName>Activity Summary Generation</displayName>
      <description>
        Generates a structured summary of user activity using OpenAI's GPT-4 model.

        **Key Processing Steps:**
        - Accepts WorkflowInput containing user_id and activity log text
        - Formats activity data using threadTemplate with summaryPrompt
        - Sends request to GPT-4 (gpt-4o model)
        - Validates response against ActivitySummarySchema
        - Returns structured ActivitySummary object

        **Key Prompt Logic:**
        - System prompt instructs to analyze and summarize:
          - Initial project goals and tech stack selections
          - Direction changes and technical pivots
          - Time gaps and session breaks
          - Major feature changes
          - Conversion-driving decisions
        - Emphasizes chronological ordering and extreme conciseness
        - Includes structured example for consistent formatting
        - Uses activity markers and gap notation for temporal context
      </description>
    </activity>
    <activity>
      <functionName>categorizeActivity</functionName>
      <displayName>Activity Categorization</displayName>
      <description>
        Classifies the activity summary into predefined categories using OpenAI's GPT-4-mini model.

        **Key Processing Steps:**
        - Takes generated activity summary and array of valid categories
        - Formats input using threadTemplate with categorizationPrompt
        - Sends request to GPT-4 (gpt-4o-mini model)
        - Validates response against ActivityCategorizationSchema
        - Returns ActivityCategorization with category and sub-category

        **Key Prompt Logic:**
        - System prompt instructs to:
          - Select most specific matching category from provided options
          - Generate appropriate sub-category based on work details
          - Prioritize main goal/purpose over specific tools used
          - Focus on primary work outcome
        - Includes formatted example showing category/sub-category structure
        - Provides explicit rules for categorization priority
        - Uses structured input format for consistency
      </description>
    </activity>
  </activities>
</workflow>