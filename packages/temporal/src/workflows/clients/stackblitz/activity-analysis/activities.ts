import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { summaryPrompt, categorizationPrompt } from './prompts.js';
import { ActivitySummarySchema, ActivityCategorizationSchema } from './types.js';
import type { WorkflowInput, ActivitySummary, ActivityCategorization } from './types.js';

const { openaiChatService } = workerModule;

export async function summarizeActivity(input: WorkflowInput): Promise<ActivitySummary> {
  const messages = await threadTemplate(summaryPrompt, {
    activity: input.activity,
  });

  return await openaiChatService.request<ActivitySummary>({
    messages,
    model: 'gpt-4o',
    schema: ActivitySummarySchema,
    schemaName: 'activity_summary_schema',
  });
}

export async function categorizeActivity(input: {
  summary: string;
  categories: string[];
}): Promise<ActivityCategorization> {
  const messages = await threadTemplate(categorizationPrompt, {
    summary: input.summary,
    categories: JSON.stringify(input.categories),
  });

  return await openaiChatService.request<ActivityCategorization>({
    messages,
    model: 'gpt-4o-mini',
    schema: ActivityCategorizationSchema,
    schemaName: 'activity_categorization_schema',
  });
}

export default WorkflowScope.register(import.meta.url, {
  summarizeActivity,
  categorizeActivity,
});
