curl --location 'http://localhost:2000/api/workflow' \
--header 'Content-Type: application/json' \
--data '{
    "workflowName": "clientsStackblitzActivityAnalysisWorkflow",
    "args": {
        "user_id": 22617,
        "activity": "(first seen 79 days ago)\r\nuser msg: [I want create single page front for Guest house. The logo is attached. The logo is nautilus. Create front page using tailwind css and nuxt js. Use lines like logo and nautilus vertically. Use Lemon Yellow, turquoise Green,  Charcole Gray and White color]\r\nuser msg: [Add line like wave vertically and animate when scroll]\r\nuser msg: [Use  Lemon Yellow, turquoise Green mix as background color. Add line like wave vertically From navigation bar to footer and animate when scroll\r\n\r\n]\r\nuser msg: [Change the vertical lines like spiral like Nautilus. Use Theme colors mix]\r\nuser msg: [Stop the line animation and start lines from right top to left bottom]\r\nuser msg: [Add multiple lines like a spiral]\r\n<gap: 19h>\r\n<SESSION START (2025-02-12)>\r\nuser msg: [remove the vertical line]\r\nuser msg: [change footer top like a wave ]\r\nuser msg: [stop the animation and make the wave as top part of the footer]\r\nuser msg: [build the website only using html and css and make responsive.  Seo optimized. remove the wave in the footer]\r\nuser msg: [Also remove nuxt js. Only html and css]\r\nuser msg: [preview]\r\n<gap: 12d>\r\nuser msg: [Based on these images design front-end of an online book website where user are able to buy and read books online. Use tailwind ]\r\nuser msg: [used nuxt js and remove bottom nav bar from web version. Also design other pages. Book search, Books details, login and register, user account.]\r\nuser msg: [Instead of react js  us vue nuxt js. Remove bottom nav bar from web version. Also design other pages. Book search, Books details, login and register, user account.]\r\nuser msg: [Start fresh with a new Nuxt.js project]\r\nuser msg: [npm run dev\r\n]\r\n<gap: 11h>\r\n<SESSION START (2025-02-25)>\r\nuser msg: [Based on these images design front-end of an online book website where user are able to buy and read books online. Use tailwind and nuxt js. Create home page, search page, books details, checkout, user account, register and login page]\r\nuser msg: [Based on these images design front-end of an online book website where user are able to buy and read books online. Use tailwind and nuxt js. Create home page, search page, books details, checkout, user account, register and login page]\r\nuser msg: [dont use Supabase only create front ]\r\n<event:subscription_created>\r\n(active 30 days ago)",
        "categories": ["internal_tool", "mobile"," landing_page", "transactional", "personal_utility", "developer_tool", "content", "game", "social_community", "portfolio", "landing-page", "developer-tool", "personal-utility", "internal-tool", "novelty", "social-community", "web_design"]
    },
    "operation": "execute"
}'
