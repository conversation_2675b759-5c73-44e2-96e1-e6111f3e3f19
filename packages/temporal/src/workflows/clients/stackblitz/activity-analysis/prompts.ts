export const summaryPrompt = [
  {
    role: 'system',
    content: `
You are an expert at analyzing user activity logs and creating concise summaries.

Your task is to analyze a sequence of user activities and create a clear, chronological summary that captures:
- Initial project goals and tech stack choices
- Key pivots or changes in direction (e.g. switching frameworks, major design changes)
- Session breaks and time gaps
- Major feature additions or removals
- Final decisions that led to conversion/subscription

Focus on being extremely concise while maintaining clarity about the user's journey.

Example Input:
<activity>
user msg: [Create a landing page with React and Tail<PERSON>]
user msg: [Add a hero section with animation]
user msg: [Change the animation to be more subtle]
<gap: 12h>
user msg: [Let's switch to Vue instead of React]
<event:subscription_created>
</activity>

Example Output:
"Started with React/Tailwind landing page, focused on animations. After 12h break, pivoted to Vue. Shortly after framework switch, converted to paid."
`,
  },
  {
    role: 'user',
    content: `
Please analyze this activity log and create a summary:

<activity>
{{activity}}
</activity>
`,
  },
];

export const categorizationPrompt = [
  {
    role: 'system',
    content: `
You are an expert at categorizing user development activities and identifying patterns.

Your task is to:
1. Match the activity with the most specific category from the provided options
2. Determine a precise sub-category based on the actual work being done
3. Ensure categorization captures the primary focus of the work

Categories should reflect the main goal, not just the tools used. For example:
- A landing page with animations is "web_design/landing_page" not "animation/web"
- An e-commerce site is "web_development/e-commerce" not just "web_development"

Example Input:
<summary>
Started with landing page for guest house using Nuxt/Tailwind, experimented with wave animations. After 19h, simplified design by removing animations. 12 days later, switched to HTML/CSS only, focusing on SEO. Finally converted after 30 days.
</summary>

Available categories:
["web_design", "web_development", "mobile_development"]

Example Output:
{
  "category": "web_design",
  "sub_category": "landing_page"
}
`,
  },
  {
    role: 'user',
    content: `
Based on this summary, please categorize the activity:

<summary>
{{summary}}
</summary>

Available categories:
<categories>
{{categories}}
</categories>
`,
  },
];
