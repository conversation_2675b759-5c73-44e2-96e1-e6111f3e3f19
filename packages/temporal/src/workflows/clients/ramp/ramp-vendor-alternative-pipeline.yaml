---
name: <PERSON><PERSON> Vendor Alternative Content
environment:
  company_name: Ramp
  full_context: '## Company Context: ${artifacts.company_context} ## Target Audience: ${artifacts.audience_personas} ## Brand Voice & Writing Guidelines: ${artifacts.writing_guidelines} ## Content Guidelines: ${artifacts.ramp_writing_guidelines}'
input_schema:
  parent_vendor_name:
    type: string
    position: 0
    description: Parent vendor name to find alternatives for (e.g., <PERSON><PERSON>, Slack, Asana)
    required: true
  parent_vendor_category:
    type: string
    position: 1
    description: Parent vendor category
    required: true
    enum:
      - Generative AI
      - Email marketing
      - Project management
      - CRM
      - Web Hosting & Site Builders
      - Content Creation
      - DevOps
  parent_vendor_website:
    type: string
    position: 2
    description: Parent vendor's website URL (optional, for better research)
    required: false
  primaryVendorRefID:
    type: string
    position: 3
    description: Sanity document ID of the parent vendor (for creating references)
    required: false
  alternativeRefIDs:
    type: string
    position: 4
    description: Comma-separated list of Sanity document IDs for alternative vendors
    required: false
output_schema:
  # Basic vendor information (positions 0-4)
  parent_vendor_name:
    type: string
    default: ${steps.vendorAlternativeContent.output.parentVendorName}
    position: 0
    description: Parent vendor name
    display_name: Parent Vendor
  category:
    type: string
    default: ${steps.vendorAlternativeContent.output.category}
    position: 1
    description: Vendor category
    display_name: Category
  website:
    type: string
    default: null
    position: 2
    description: Parent vendor website (not applicable for alternatives)
    display_name: Website
  developer:
    type: string
    default: null
    position: 3
    description: Parent vendor developer (not applicable for alternatives)
    display_name: Developer
  description:
    type: string
    default: null
    position: 4
    description: Parent vendor description (not applicable for alternatives)
    display_name: Description

  # Hero section (positions 5-6)
  hero_title:
    type: string
    default: ${steps.vendorAlternativeContent.output.heroTitle}
    position: 5
    description: Alternative page title
    display_name: Hero Title
  hero_body:
    type: string
    default: ${steps.vendorAlternativeContent.output.heroBody}
    position: 6
    description: Alternative page description
    display_name: Hero Description

  # Content sections (positions 7-11)
  alternatives_list:
    ui: rich_text
    type: string
    default: ${steps.vendorAlternativeContent.output.fullAlternativesList}
    position: 7
    description: Complete alternatives list content
    display_name: Best Alternatives List
  evaluation_criteria:
    ui: rich_text
    type: string
    default: ${steps.vendorAlternativeContent.output.fullEvaluationCriteria}
    position: 8
    description: Complete evaluation criteria sections
    display_name: Evaluation Criteria
  selection_guidance:
    ui: rich_text
    type: string
    default: ${steps.vendorAlternativeContent.output.fullSelectionGuidance}
    position: 9
    description: Complete selection guidance sections
    display_name: Selection Guidance
  pros:
    type: string
    default: null
    position: 10
    description: Pros section (not applicable for alternatives)
    display_name: Pros
  cons:
    type: string
    default: null
    position: 11
    description: Cons section (not applicable for alternatives)
    display_name: Cons

  # Metadata (positions 12-16)
  has_free_tier:
    type: boolean
    default: null
    position: 12
    description: Has free tier (not applicable for alternatives)
    display_name: Has Free Tier
  g2_score:
    type: number
    default: null
    position: 13
    description: G2 score (not applicable for alternatives)
    display_name: G2 Score
  g2_link:
    type: string
    default: null
    position: 14
    description: G2 link (not applicable for alternatives)
    display_name: G2 Link
  best_for_company_size:
    type: string
    default: null
    position: 15
    description: Best for company size (not applicable for alternatives)
    display_name: Best For Company Size
  best_for_editorial:
    type: string
    default: null
    position: 16
    description: Best for editorial (not applicable for alternatives)
    display_name: Best For Editorial

  # Brand information (positions 17-24)
  logo:
    type: string
    default: null
    position: 17
    description: Logo URL (not applicable for alternatives)
    display_name: Logo
  logo_alt:
    type: string
    default: null
    position: 18
    description: Alternative logo URL (not applicable for alternatives)
    display_name: Logo Alt
  brand_colors:
    type: string
    default: null
    position: 19
    description: Brand colors (not applicable for alternatives)
    display_name: Brand Colors
  social_links:
    type: string
    default: null
    position: 20
    description: Social links (not applicable for alternatives)
    display_name: Social Links
  company_size:
    type: string
    default: null
    position: 21
    description: Company size (not applicable for alternatives)
    display_name: Company Size
  founded_year:
    type: number
    default: null
    position: 22
    description: Founded year (not applicable for alternatives)
    display_name: Founded Year
  industry:
    type: string
    default: null
    position: 23
    description: Industry (not applicable for alternatives)
    display_name: Industry
  location:
    type: string
    default: null
    position: 24
    description: Location (not applicable for alternatives)
    display_name: Location

  # Alternative-specific fields (positions 25-26)
  meta_title:
    type: string
    default: ${steps.vendorAlternativeContent.output.metaTitle}
    position: 25
    description: Page meta title
    display_name: Meta Title
  meta_description:
    type: string
    default: ${steps.vendorAlternativeContent.output.metaDescription}
    position: 26
    description: Page meta description
    display_name: Meta Description
  hub_topic:
    type: string
    default: ${steps.vendorAlternativeContent.output.hubTopic}
    position: 27
    description: Content hub topic
    display_name: Hub Topic
steps:
  - id: research
    function_name: deepResearcherWorkflow
    inputs:
      context: '## RAMP COMPANY CONTEXT: ${artifacts.company_context} ## TARGET AUDIENCE: ${artifacts.audience_personas} ## RESEARCH FOCUS: Vendor alternatives analysis for ${pipeline.input.parent_vendor_name}'
      questions:
        - 'BEST ALTERNATIVES LIST: What are the top 8-12 alternatives to ${pipeline.input.parent_vendor_name} in the ${pipeline.input.parent_vendor_category} category for 2024? For each alternative, provide: company name, main value proposition, key differentiators from ${pipeline.input.parent_vendor_name}, and data-backed reasons why users choose this alternative.'
        - 'CORE FUNCTIONALITY: How do the top alternatives to ${pipeline.input.parent_vendor_name} compare in terms of essential features and capabilities? Focus on the fundamental functions that define the ${pipeline.input.parent_vendor_category} category.'
        - 'USER EXPERIENCE AND SUPPORT: How do alternatives to ${pipeline.input.parent_vendor_name} compare in terms of interface design, ease of use, onboarding experience, customer service quality, and available support channels?'
        - 'INTEGRATION CAPABILITIES: How do alternatives to ${pipeline.input.parent_vendor_name} compare in terms of native integrations, third-party connections, API access, and data synchronization capabilities?'
        - 'VALUE FOR MONEY: How do alternatives to ${pipeline.input.parent_vendor_name} compare in terms of pricing models, cost-effectiveness, feature-to-price ratio, and overall value proposition?'
        - 'INDUSTRY-SPECIFIC REQUIREMENTS: How do alternatives to ${pipeline.input.parent_vendor_name} address specialized features and requirements for different industries or business sectors?'
        - 'ASSESS TEAM REQUIREMENTS: What process should businesses follow to understand their specific needs and priorities when evaluating ${pipeline.input.parent_vendor_name} alternatives?'
        - 'TEST DRIVE EVALUATION: What are the best practices for testing and evaluating ${pipeline.input.parent_vendor_name} alternatives before making a commitment, including trial periods and evaluation methods?'
        - 'LONG-TERM FIT EVALUATION: How should businesses evaluate alternatives to ${pipeline.input.parent_vendor_name} for scalability, future-proofing, and long-term strategic alignment?'
        - 'SUPPORT AND TRAINING RESOURCES: What support and training resources are available for each ${pipeline.input.parent_vendor_name} alternative, including implementation assistance and learning materials?'
      domainsToSearch:
        - ${pipeline.input.parent_vendor_website}
        - g2.com
        - capterra.com
        - trustradius.com
        - alternativeto.net
    outputs:
      research_data:
        ui: text
        type: string
        value: ${step.result}
        position: 0
        description: Comprehensive alternatives research data
        display_name: Research Results
    display_name: Alternative Research

  - id: vendorAlternativeContent
    function_name: rampVendorAlternativeWorkflow
    inputs:
      researchData: ${steps.research.output.research_data}
      context: '${artifacts.ramp_vendor_alternative_guidelines}'
      parentVendorName: ${pipeline.input.parent_vendor_name}
    outputs:
      # Basic vendor information (positions 0-4)
      parentVendorName:
        type: string
        value: ${step.result.parentVendorName}
        description: Parsed parent vendor name
      category:
        type: string
        value: ${step.result.category}
        description: Parsed category
      website:
        type: string
        value: null
        description: Website (not applicable for alternatives)
      developer:
        type: string
        value: null
        description: Developer (not applicable for alternatives)
      description:
        type: string
        value: null
        description: Description (not applicable for alternatives)

      # Hero section (positions 5-6)
      heroTitle:
        type: string
        value: ${step.result.heroTitle}
        description: Parsed hero title
      heroBody:
        type: string
        value: ${step.result.heroBody}
        description: Parsed hero body

      # Content sections (positions 7-11)
      fullAlternativesList:
        type: string
        value: ${step.result.fullAlternativesList}
        description: Complete alternatives list content
      fullEvaluationCriteria:
        type: string
        value: ${step.result.fullEvaluationCriteria}
        description: Complete evaluation criteria sections
      fullSelectionGuidance:
        type: string
        value: ${step.result.fullSelectionGuidance}
        description: Complete selection guidance sections
      pros:
        type: string
        value: null
        description: Pros (not applicable for alternatives)
      cons:
        type: string
        value: null
        description: Cons (not applicable for alternatives)

      # Metadata (positions 12-16)
      hasFreeTier:
        type: boolean
        value: null
        description: Has free tier (not applicable for alternatives)
      g2Score:
        type: number
        value: null
        description: G2 score (not applicable for alternatives)
      g2Link:
        type: string
        value: null
        description: G2 link (not applicable for alternatives)
      bestForCompanySize:
        type: string
        value: null
        description: Best for company size (not applicable for alternatives)
      bestForEditorial:
        type: string
        value: null
        description: Best for editorial (not applicable for alternatives)

      # Brand information (positions 17-24)
      logo:
        type: string
        value: null
        description: Logo (not applicable for alternatives)
      logoAlt:
        type: string
        value: null
        description: Logo alt (not applicable for alternatives)
      brandColors:
        type: string
        value: null
        description: Brand colors (not applicable for alternatives)
      socialLinks:
        type: string
        value: null
        description: Social links (not applicable for alternatives)
      companySize:
        type: string
        value: null
        description: Company size (not applicable for alternatives)
      foundedYear:
        type: number
        value: null
        description: Founded year (not applicable for alternatives)
      industry:
        type: string
        value: null
        description: Industry (not applicable for alternatives)
      location:
        type: string
        value: null
        description: Location (not applicable for alternatives)

      # Alternative-specific fields (positions 25-27)
      metaTitle:
        type: string
        value: ${step.result.metaTitle}
        description: Parsed meta title
      metaDescription:
        type: string
        value: ${step.result.metaDescription}
        description: Parsed meta description
      hubTopic:
        type: string
        value: ${step.result.hubTopic}
        description: Parsed hub topic
    display_name: Generate & Parse Vendor Alternative Content
    human_review: true

  - id: publishToSanity
    function_name: sanityPublisherWorkflow
    inputs:
      document:
        name: ${steps.vendorAlternativeContent.output.parentVendorName} Alternatives
        slug:
          type: slug
          current: ${pipeline.input.parent_vendor_name}-alternatives
        heroTitle: ${steps.vendorAlternativeContent.output.heroTitle}
        heroBody: ${steps.vendorAlternativeContent.output.heroBody}
        # Reference fields - will be transformed to Sanity references
        primaryVendorRefID: ${pipeline.input.primaryVendorRefID}
        alternativeRefIDs: ${pipeline.input.alternativeRefIDs}
        # Content fields
        alternativesList: ${steps.vendorAlternativeContent.output.fullAlternativesList}
        evaluationCriteria: ${steps.vendorAlternativeContent.output.fullEvaluationCriteria}
        selectionGuidance: ${steps.vendorAlternativeContent.output.fullSelectionGuidance}
        seo:
          title: ${steps.vendorAlternativeContent.output.metaTitle}
          description: ${steps.vendorAlternativeContent.output.metaDescription}
        hubTopic: ${steps.vendorAlternativeContent.output.hubTopic}
        publishedAt: 2025-07-11T17:39:25.000Z
        source: ramp-vendor-alternative-workflow
      options:
        instanceName: ramp
        dataset: production
        asDraft: true
        documentType: vendorAlternative
    outputs:
      sanityUrl:
        type: string
        value: ${step.result.studioUrl}
        description: Sanity Studio URL for the published document
      documentId:
        type: string
        value: ${step.result.documentId}
        description: Sanity document ID
    display_name: Publish to Sanity Dev
    human_review: false
