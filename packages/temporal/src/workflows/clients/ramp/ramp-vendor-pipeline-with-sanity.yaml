---
name: <PERSON><PERSON> Vendor Content with Sanity Publishing
environment:
  company_name: Ramp
  full_context: '## Company Context: ${artifacts.company_context} ## Target Audience: ${artifacts.audience_personas} ## Brand Voice & Writing Guidelines: ${artifacts.writing_guidelines} ## Content Guidelines: ${artifacts.ramp_writing_guidelines}'
input_schema:
  name:
    type: string
    position: 0
    description: Vendor name (e.g., MongoD<PERSON>, <PERSON>la<PERSON>, Notion)
    required: true
  category:
    enum:
      - Generative AI (05a6ffd5-98b4-43c2-8b3d-7df1377552a3)
      - Email marketing (05cb9ed5-c702-4d1a-b376-29c5de271f36)
      - Project management (129cd7d2-dbf6-46eb-93e2-fc2c2983c20b)
      - CRM (33a6bd5d-4945-4237-be98-d9b4461e12ba)
      - Web Hosting & Site Builders (6a030841-decf-4a38-b7e9-39f66092d486)
      - Content Creation (8e478f8e-f409-44dd-993f-10be6dad2534)
      - DevOps (a90f324d-13db-4a00-bd0a-5cf2c7357b1e)
      - Enterprise CRM (da88795d-9e73-4c68-b176-b36e9ee14728)
    type: string
    position: 1
    description: Product category
    required: true
  website_url:
    type: string
    position: 2
    description: Vendor's website URL (optional, for better research)
output_schema:
  document_id:
    type: string
    default: ${steps.publishToSanity.output.documentId}
    position: 0
    description: Sanity document ID
    display_name: Document ID
  studio_url:
    type: string
    default: ${steps.publishToSanity.output.studioUrl}
    position: 1
    description: Sanity Studio edit URL
    display_name: Studio URL
steps:
  - id: research
    function_name: deepResearcherWorkflow
    inputs:
      context: '## RAMP COMPANY CONTEXT: ${artifacts.company_context} ## TARGET AUDIENCE: ${artifacts.audience_personas} ## WRITING APPROACH: ${artifacts.writing_guidelines}'
      questions:
        - 'OVERVIEW (60-70 words): What is ${pipeline.input.name} and what does it do exactly? Provide a concise 60-70 word overview including product category, core value proposition, target audience, and competitive positioning.'
        - 'FEATURES (5-6 features): What are the 5-6 key features of ${pipeline.input.name}? For each feature provide: feature name, technical description, and business benefit.'
        - "PRICING TABLE: What is ${pipeline.input.name}'s exact pricing structure? List specific plan names, prices, key features, and ideal customer segments. Format as markdown table with columns: Plan | Price | Key features | Ideal for."
        - 'FREE TIER: Does ${pipeline.input.name} offer a free tier or free trial? Answer Yes or No and explain limitations.'
        - 'COMPANY DETAILS: Who develops ${pipeline.input.name}? What company is behind it? What is the official website URL (must start with https://)?'
        - "G2 RATING: What is ${pipeline.input.name}'s G2 rating (numerical score) and what do customer reviews say? Provide G2 link."
        - 'TARGET COMPANIES: What company sizes typically use ${pipeline.input.name} (micro, small, medium, large, enterprise)? Who is it best for?'
        - 'PROS: What are the main advantages of using ${pipeline.input.name}? What are the best use cases where it excels? Format as bullet points.'
        - 'CONS: What are the limitations or disadvantages of ${pipeline.input.name}? What types of businesses might struggle with it? Format as bullet points.'
        - "DESCRIPTION: Provide a one-sentence description in format: 'A [category] that helps [audience] [main function].'"
      domainsToSearch:
        - ${pipeline.input.website_url}
        - g2.com
        - capterra.com
        - trustradius.com
    outputs:
      research_data:
        ui: text
        type: string
        value: ${step.result}
        position: 0
        description: Comprehensive vendor research data
        display_name: Research Results
    display_name: Vendor Research
    required_inputs:
  - id: enhancePricing
    function_name: researchImprovementWorkflow
    inputs:
      targetDomain: ${pipeline.input.website_url}
      researchOutput: ${steps.research.output.research_data}
      maxUrlsToScrape: 3
      enhancementFocus: detailed pricing information with specific plan names, exact prices, key features, and ideal customer segments for comprehensive pricing table generation
    outputs:
      enhanced_research:
        type: string
        value: ${step.result.enhancedContent}
        description: Research enhanced with scraped pricing data
    display_name: Enhance Pricing Accuracy
    required_inputs:
      - researchOutput
      - targetDomain
      - enhancementFocus
  - id: vendorContent
    function_name: rampVendorWorkflow
    human_review: true
    inputs:
      context: ${artifacts.ramp_pseo_vendor_guidelines}
      vendorName: ${pipeline.input.name}
      researchData: ${steps.enhancePricing.output.enhanced_research}
    outputs:
      vendorName:
        type: string
        value: ${step.result.vendorName}
        description: Parsed vendor name
        position: 0
      category:
        type: string
        value: ${step.result.category}
        description: Parsed category
        position: 1
      website:
        type: string
        value: ${step.result.website}
        description: Parsed website
        position: 2
      developer:
        type: string
        value: ${step.result.developer}
        description: Parsed developer
        position: 3
      description:
        type: string
        value: ${step.result.description}
        description: Parsed description
        position: 4
      overviewContent:
        ui: rich_text
        type: string
        value: ${step.result.overviewContent}
        description: Parsed overview content
        position: 5
      featuresContent:
        ui: rich_text
        type: string
        value: ${step.result.featuresContent}
        description: Parsed features content
        position: 6
      pricingTable:
        ui: rich_text
        type: string
        value: ${step.result.pricingTable}
        description: Parsed pricing table
        position: 7
      pros:
        ui: rich_text
        type: string
        value: ${step.result.pros}
        description: Parsed pros
        position: 8
      cons:
        ui: rich_text
        type: string
        value: ${step.result.cons}
        description: Parsed cons
        position: 9
      hasFreeTier:
        type: boolean
        value: ${step.result.hasFreeTier}
        description: Parsed free tier availability
        position: 10
      g2Score:
        type: number
        value: ${step.result.g2Score}
        description: Parsed G2 score
        position: 11
      g2Link:
        type: string
        value: ${step.result.g2Link}
        description: Parsed G2 link
        position: 12
      bestForCompanySize:
        type: string
        value: ${step.result.bestForCompanySize}
        description: Parsed company size recommendation
        position: 13
      bestForEditorial:
        type: string
        value: ${step.result.bestForEditorial}
        description: Parsed editorial recommendation
        position: 14
      logo:
        type: string
        value: ${step.result.logo}
        description: Primary logo URL
        position: 15
      logoAlt:
        type: string
        value: ${step.result.logoAlt}
        description: Alternative logo URL
        position: 16
      brandColors:
        type: string
        value: ${step.result.brandColors}
        description: Brand colors
        position: 17
      socialLinks:
        ui: rich_text
        type: string
        value: ${step.result.socialLinks}
        description: Social media links
        position: 18
      companySize:
        type: string
        value: ${step.result.companySize}
        description: Company size
        position: 19
      foundedYear:
        type: number
        value: ${step.result.foundedYear}
        description: Founded year
        position: 20
      industry:
        type: string
        value: ${step.result.industry}
        description: Primary industry
        position: 21
      location:
        type: string
        value: ${step.result.location}
        description: Company location
        position: 22
      spendingAnalysis:
        ui: rich_text
        type: string
        value: ${step.result.spendingAnalysis}
        description: Generated spending chart analysis
        position: 23
      targetAnalysis:
        ui: rich_text
        type: string
        value: ${step.result.targetAnalysis}
        description: Generated target chart analysis
        position: 24
      spendingChartId:
        type: string
        value: ${step.result.spendingChartId}
        description: Spending chart ID
        position: 25
      targetChartId:
        type: string
        value: ${step.result.targetChartId}
        description: Target chart ID
        position: 26
      metaTitle:
        type: string
        value: "${step.result.vendorName | append: ' Review: a Data-Backed Look'}"
        description: SEO meta title
        position: 27
      metaDescription:
        type: string
        value: "${step.result.description | truncate: 152, '...'}"
        description: SEO meta description (max 152 chars)
        position: 28
    display_name: Generate & Parse Vendor Content
    required_inputs:
      - researchData
      - context
      - vendorName
  - id: publishToSanity
    function_name: sanityPublisherWorkflow
    inputs:
      options:
        slug: '${steps.vendorContent.output.vendorName | downcase | replace: " ", "-" | replace: "[^a-z0-9-]", ""}'
        asDraft: true
        dataset: production
        documentType: vendor
        instanceName: ramp
        createOrUpdate: true
        skipPublishing: false
        mappingSchema: 'ramp-vendor'
      sourceData:
        # Basic fields
        vendorName: ${steps.vendorContent.output.vendorName}
        website: ${steps.vendorContent.output.website}
        developer: ${steps.vendorContent.output.developer}
        description: ${steps.vendorContent.output.description}

        # Hero fields
        heroTitle: "${steps.vendorContent.output.vendorName | append: ' review: a data-backed look'}"
        heroBody: '${steps.vendorContent.output.description | capitalize}'

        # G2 fields
        g2Score: ${steps.vendorContent.output.g2Score}
        g2Link: ${steps.vendorContent.output.g2Link}

        # SEO fields
        metaTitle: ${steps.vendorContent.output.metaTitle}
        metaDescription: ${steps.vendorContent.output.metaDescription}

        # Brand fields (excluding logos for now)
        brandColors: ${steps.vendorContent.output.brandColors}

        # Company fields (note: some may not be in Sanity schema)
        foundedYear: ${steps.vendorContent.output.foundedYear}

        # Pros and Cons as separate fields (required by Sanity schema)
        pros: ${steps.vendorContent.output.pros}
        cons: ${steps.vendorContent.output.cons}

        # All content merged into single comprehensive field (matches Pipedrive structure)
        contentWithCharts: |
          ${steps.vendorContent.output.overviewContent}

          ## Features

          ${steps.vendorContent.output.featuresContent}

          ## Pricing

          ${steps.vendorContent.output.pricingTable}

          ## Pros

          ${steps.vendorContent.output.pros}

          ## Cons

          ${steps.vendorContent.output.cons}

          ## How much do businesses spend on ${steps.vendorContent.output.vendorName}?

          The chart below illustrates average spending on ${steps.vendorContent.output.vendorName} across different business sizes.

          [datawrapper:${steps.vendorContent.output.spendingChartId}]

          ${steps.vendorContent.output.spendingAnalysis}

          ## Who is ${steps.vendorContent.output.vendorName} best for?

          The chart below breaks down ${steps.vendorContent.output.vendorName}'s user base by industry and business size.

          [datawrapper:${steps.vendorContent.output.targetChartId}]

          ${steps.vendorContent.output.targetAnalysis}

        # Other fields
        hasFreeTier: ${steps.vendorContent.output.hasFreeTier}
        bestForEditorial: ${steps.vendorContent.output.bestForEditorial}
        bestForCompanySize: ${steps.vendorContent.output.bestForCompanySize}

        # Additional Runpod-compatible fields
        alternatives: null
        primaryVendor: null

        # Sanity-specific
        category: ${pipeline.input.category}

        # Auto-generated slug from vendor name
        slug: '${steps.vendorContent.output.vendorName | downcase | replace: " ", "-" | replace: "[^a-z0-9-]", ""}'

        # Hub and publishing metadata will be set by mapping schema defaults
        # hubTopic: Always "Vendor Management" (handled by mapping schema)
        # publishingTeams: Always "SEO" team (handled by mapping schema)
    outputs:
      studioUrl:
        type: string
        value: ${step.result.studioUrl}
        description: Sanity Studio edit URL
      documentId:
        type: string
        value: ${step.result.documentId}
        description: Sanity document ID
    display_name: Publish to Sanity CMS
    required_inputs:
      - sourceData
      - options
