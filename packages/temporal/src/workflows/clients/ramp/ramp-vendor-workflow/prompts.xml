<?xml version="1.0" encoding="UTF-8"?>
<!-- Content hash: 60a44f76e2e79202fb6bd9520fbf114ac8cdc181a4e623241a58425bf757b795 -->
<activities>
  <activity>
    <activityName>parseAndFormatVendorContent</activityName>
    <prompts>
      <prompt>
        <promptName>vendorParsingPrompt</promptName>
        <description>Uses OpenAI to parse and extract structured vendor information from raw content</description>
        <inputs>
          <input>vendorName</input>
          <input>content</input>
        </inputs>
        <outputSchema>VendorParsing</outputSchema>
        <model>gpt-4o</model>
        <systemPrompt>Extract vendor information from the content and return it in the specified JSON structure. Be precise and accurate.

Note: Hero title and body fields are NOT needed - they are generated automatically via liquid filters in the pipeline.</systemPrompt>
        <userPromptTemplate>Vendor: {{vendorName}}

Content:
{{content}}</userPromptTemplate>
        <additionalFeatures>
          <feature>Chart Data Lookup</feature>
          <feature>Precise Vendor Mapping</feature>
          <feature>Company Size Categorization</feature>
        </additionalFeatures>
        <validationRules>
          <rule>Chart ID must be exactly 5 alphanumeric characters</rule>
          <rule>Company Size must be one of: "micro", "smb", "all", "midMarketEnterprise"</rule>
          <rule>Vendor name lookup is case-insensitive</rule>
        </validationRules>
      </prompt>
    </prompts>
  </activity>
</activities>