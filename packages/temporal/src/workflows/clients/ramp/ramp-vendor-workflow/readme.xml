<!-- Generated on 2025-07-23T13:52:48.904Z -->
<!-- Content hash: d678bfb83952a6685139fae3044e388d20008e28f059d9855018bb87d6e127a1 -->
<workflow>
  <functionName>rampVendorWorkflow</functionName>
  <displayName>Ramp Vendor Content Generation</displayName>
  <path>clients/ramp/ramp-vendor-workflow</path>
  <overview>Generates comprehensive vendor profiles for Ram<PERSON>'s Atlas review system by combining AI-generated content with brand information and chart analysis. The workflow handles content generation, formatting, brand data enrichment, and optional Sanity CMS publishing.</overview>
  <flowChart><![CDATA[```mermaid
graph TD
  A[Start] --> B[Validate Input]
  B --> C[Generate Content via SEO Template]
  C --> D[Parse & Format Content]
  D --> E[Fetch Brand Info]
  E --> F[Generate Chart Summaries]
  F --> G[Merge Content & Brand Data]
  G --> H{Publish to Sanity?}
  H -->|Yes| I[Transform to Sanity Format]
  I --> J[Publish to Sanity CMS]
  H -->|No| K[Return Results]
  J --> K
```]]></flowChart>
  <activities>
    <activity>
      <functionName>parseAndFormatVendorContent</functionName>
      <displayName>Parse Vendor Content</displayName>
      <description>Uses OpenAI GPT-4 to parse and structure raw content into standardized vendor profile format, validating against schema requirements for Atlas review system.</description>
    </activity>
    <activity>
      <functionName>extractBrandInfo</functionName>
      <displayName>Extract Brand Information</displayName>
      <description>Processes Brandfetch API data to extract company logos, brand colors, social links, and company metadata in a structured format.</description>
    </activity>
    <activity>
      <functionName>transformRampToSanityDocument</functionName>
      <displayName>Transform to Sanity Format</displayName>
      <description>Converts structured vendor data into Sanity CMS document format with proper schema typing and slug generation for publishing.</description>
    </activity>
    <activity>
      <functionName>generateChartSummary</functionName>
      <displayName>Generate Chart Analysis</displayName>
      <description>Creates narrative summaries of spending and target audience data charts, following strict editorial guidelines for business context without specific numbers.</description>
    </activity>
  </activities>
</workflow>