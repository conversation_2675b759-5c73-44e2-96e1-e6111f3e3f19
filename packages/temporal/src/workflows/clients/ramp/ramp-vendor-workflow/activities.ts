import { ApplicationFailure } from '@temporalio/workflow';
import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import type { WorkflowOutput, VendorParsing } from './types.js';
import { VendorParsingSchema } from './types.js';
import { vendorParsingPrompt } from './prompts.js';
import type { SanityDocument } from '../../../cms/sanity-publisher/types.js';
import type { BrandfetchBrand } from '@flow/sdk/apis/brandfetch/types.js';

const { openaiChatService } = workerModule;

export async function parseAndFormatVendorContent(
  content: string,
  vendorName: string
): Promise<WorkflowOutput> {
  try {
    // Use OpenAI to parse the content with schema validation
    const messages = await threadTemplate(vendorParsingPrompt, {
      vendorName,
      content,
    });

    const parsedData = await openaiChatService.request<VendorParsing>({
      messages,
      model: 'gpt-4o',
      schema: VendorParsingSchema,
      schemaName: 'vendor_parsing_schema',
    });

    // Convert to workflow output format
    const result: WorkflowOutput = {
      // Basic vendor information
      vendorName: parsedData.vendor_name,
      category: parsedData.category,
      website: parsedData.website,
      developer: parsedData.developer,
      description: parsedData.description,

      // Content sections
      overviewContent: parsedData.overview_content,
      featuresContent: parsedData.features_content,
      pricingTable: parsedData.pricing_table,
      pros: parsedData.pros,
      cons: parsedData.cons,

      // Chart-dependent sections
      spendingAnalysis: parsedData.spending_analysis,
      targetAnalysis: parsedData.target_analysis,
      spendingChartId: parsedData.spending_chart_id,
      targetChartId: parsedData.target_chart_id,

      // Metadata
      hasFreeTier: parsedData.has_free_tier,
      g2Score: parsedData.g2_score,
      g2Link: parsedData.g2_link,
      bestForCompanySize: parsedData.best_for_company_size,
      bestForEditorial: parsedData.best_for_editorial,

      // Brand information (will be populated later)
      logo: null,
      logoAlt: null,
      brandColors: null,
      socialLinks: null,
      companySize: null,
      foundedYear: null,
      industry: null,
      location: null,
    };

    return result;
  } catch (error) {
    throw ApplicationFailure.create({
      message: `Failed to parse vendor content: ${error instanceof Error ? error.message : 'Unknown error'}`,
      type: 'CONTENT_PARSING_ERROR',
    });
  }
}

export function transformRampToSanityDocument(rampData: WorkflowOutput): SanityDocument {
  const vendorName = rampData.vendorName || 'Unknown Vendor';

  // Create slug from vendor name
  const slug = vendorName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();

  return {
    _type: 'vendor',
    name: vendorName,
    slug: {
      _type: 'slug',
      current: slug,
    },

    // Content sections
    overview: {
      content: rampData.overviewContent || '',
    },
    features: {
      content: rampData.featuresContent || '',
    },
    pricing: {
      table: rampData.pricingTable || '',
      hasFreeTier: rampData.hasFreeTier || false,
    },
    prosAndCons: {
      pros: rampData.pros || '',
      cons: rampData.cons || '',
    },

    // Basic information
    category: rampData.category || '',
    website: rampData.website || '',
    developer: rampData.developer || '',
    description: rampData.description || '',

    // Metadata
    g2: {
      score: rampData.g2Score || null,
      link: rampData.g2Link || '',
    },
    recommendations: {
      companySize: rampData.bestForCompanySize || '',
      editorial: rampData.bestForEditorial || '',
    },

    // Publishing metadata
    publishedAt: new Date().toISOString(),
    source: 'ramp-vendor-workflow',
  };
}

export async function extractBrandInfo(brandData: BrandfetchBrand) {
  try {
    // Extract primary logo (prefer light theme, PNG format)
    let logo = null;
    let logoAlt = null;

    if (brandData.logos && brandData.logos.length > 0) {
      // Find the best logo - prefer light theme, PNG format
      const primaryLogo =
        brandData.logos.find((l) => l.theme === 'light' && l.type === 'logo') ||
        brandData.logos.find((l) => l.type === 'logo') ||
        brandData.logos[0];

      if (primaryLogo && primaryLogo.formats && primaryLogo.formats.length > 0) {
        // Prefer PNG, then SVG, then any format
        const logoFormat =
          primaryLogo.formats.find((f) => f.format === 'png') ||
          primaryLogo.formats.find((f) => f.format === 'svg') ||
          primaryLogo.formats[0];

        logo = logoFormat.src;

        // Try to find an alternative logo (dark theme or different type)
        const altLogo =
          brandData.logos.find((l) => l.theme === 'dark' && l.type === 'logo') ||
          brandData.logos.find((l) => l.type === 'icon');

        if (altLogo && altLogo.formats && altLogo.formats.length > 0) {
          const altFormat =
            altLogo.formats.find((f) => f.format === 'png') ||
            altLogo.formats.find((f) => f.format === 'svg') ||
            altLogo.formats[0];
          logoAlt = altFormat.src;
        }
      }
    }

    // Extract brand colors (primary brand colors)
    let brandColors = null;
    if (brandData.colors && brandData.colors.length > 0) {
      const primaryColors = brandData.colors
        .filter((c) => c.type === 'brand' || c.type === 'accent')
        .map((c) => c.hex)
        .slice(0, 3); // Limit to 3 colors

      if (primaryColors.length > 0) {
        brandColors = primaryColors.join(', ');
      }
    }

    // Extract social links
    let socialLinks = null;
    if (brandData.links && brandData.links.length > 0) {
      const socialData = brandData.links.reduce(
        (acc, link) => {
          acc[link.name] = link.url;
          return acc;
        },
        {} as Record<string, string>
      );

      if (Object.keys(socialData).length > 0) {
        socialLinks = JSON.stringify(socialData);
      }
    }

    // Extract company information
    let companySize = null;
    let foundedYear = null;
    let industry = null;
    let location = null;

    if (brandData.company) {
      // Map employee count to readable format
      const employeeMap: Record<number, string> = {
        1: '1 employee',
        2: '2-10 employees',
        11: '11-50 employees',
        51: '51-200 employees',
        201: '201-500 employees',
        501: '501-1000 employees',
        1001: '1001-5000 employees',
        5001: '5001-10000 employees',
        10001: '10000+ employees',
      };

      companySize = employeeMap[brandData.company.employees] || null;
      foundedYear = brandData.company.foundedYear;

      // Extract primary industry
      if (brandData.company.industries && brandData.company.industries.length > 0) {
        industry = brandData.company.industries[0].name;
      }

      // Extract location
      if (brandData.company.location) {
        const loc = brandData.company.location;
        const locationParts = [loc.city, loc.state, loc.country].filter(Boolean);
        location = locationParts.join(', ') || null;
      }
    }

    return {
      logo,
      logoAlt,
      brandColors,
      socialLinks,
      companySize,
      foundedYear,
      industry,
      location,
    };
  } catch {
    // Return empty brand info if extraction fails
    return {
      logo: null,
      logoAlt: null,
      brandColors: null,
      socialLinks: null,
      companySize: null,
      foundedYear: null,
      industry: null,
      location: null,
    };
  }
}

export default WorkflowScope.register(import.meta.url, {
  parseAndFormatVendorContent,
  transformRampToSanityDocument,
  extractBrandInfo,
});
