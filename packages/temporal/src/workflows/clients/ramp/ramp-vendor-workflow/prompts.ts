export const vendorParsingPrompt = [
  {
    role: 'system',
    content: `Extract vendor information from the content and return it in the specified JSON structure. Be precise and accurate.

Note: Hero title and body fields are NOT needed - they are generated automatically via liquid filters in the pipeline.

FEATURES CONTENT RULES:
1. features_content: Markdown formatted text for the body content, including feature names and descriptions
   - Include 5-6 key features with clear descriptions
   - Format as markdown with proper headers and bullet points

Chart Data (CSV format - vendor,spending_chart_id,target_chart_id):

vendor,line,pie
asana,Jcn93,VxPqj
trello,5pyFF,sEqk9
clickup,chjmD,1VvTd
monday.com,hDzxj,THMDl
atlassian,dxixM,MgnWy
airtable,ET18p,YFrRE
smartsheet,DDkvs,DAX7e
notion labs,uIS0s,KTxYN
basecamp,0sYzZ,RQYHC
teamwork,Nu2J7,Nu2J7
zoho,,uvuCV
microsoft office / azure,SIyoO,ggz21
activecampaign,wNxcm,uvuCV
mailchimp,oheLt,5SVd0
marketo,tlXu0,U9vm4
sendgrid,5IN2n,02vlp
constant contact,WG68Y,VAaRP
klaviyo,1Nt1M,QTLfQ
omnisend,FNdxt,Hc0Hm
browserstack,T4TLa,noIhR
CircleCI,HKCYl,YyVl0
GitLab,TtA1s,8o54m
MongoDB,AS8i1,cuXqS
Supabase,hx6GI,kuUTB
Postman,EiRHc,plr9x
Canva,KbQi2,o4pBc
Descript,ujJEH,8fqeO
CapCut,qIKT2,2wD2O
Figma,VKAIp,ZnL30
Salesforce,os90m,qEmJ6
Zoho,bDmUO,9EIWi
Close,NmNPo,BCfy2
Pipedrive,atwlb,VMdXc
Freshworks,VgJ6C,Eqw3I
HubSpot,aVgqW,vnSkj
Zendesk,-,-
Intercom,sD1Bm,d4DKg
Webflow,gW9Ei,xauBN
BigCommerce,DTFEz,r320g
Squarespace,FLWtk,FKOUg
Shopify,U6YVZ,Jq7Zc
Wix,zApOV,hppSy
OpenAI,XDpel,HoAnI
Anthropic,RHt4J,sH0Hx
Cohere,XPTKj,LteWA
Mistral AI,tVCPj,Qgp4w
Perplexity AI,GnUuR,BXrOo
x.ai,rsYE1,uE02O
Datadog,I7i3K,MjiKR
Grafana Labs,r8CfP,qQkQ3
Honeycomb io,mAw42,lZAiI
New Relic,SRs7l,Nsk4z
Timescale,3EWQF,hlnMY
Amplitude,ITklN,JAtuM
ChartMogul,Skx79,M0kYT
Mixpanel,eutRq,zEeGy
Baseten,LwzUr,VXHB6
Modal,LwzUr,VXHB6
Pinecone,TW1xJ,efpes
runpod.io,WW5oV,ZvMtd

IMPORTANT: Use this chart data to determine if the vendor has chart IDs available. 
- Look up the vendor name (exact match, case-insensitive) in the data above
- If found, extract the spending chart ID (line column) and target chart ID (pie column)
- Set spending_chart_id and target_chart_id in your JSON response  
- Include the chart IDs in your spending_analysis and target_analysis fields
- If no chart data is available for the vendor, set both chart ID fields to null
- If chart ID is "-" or empty, set to null
- Use the exact vendor names as listed above - no variations or fuzzy matching

JSON Response Schema Validation:
- spending_chart_id: string|null - Must be exact match from line column or null
- target_chart_id: string|null - Must be exact match from pie column or null

VALIDATION RULES:
1. Chart ID Format: Exactly 5 characters, alphanumeric (a-z, A-Z, 0-9)
2. Valid Examples: "Jcn93", "VxPqj", "5pyFF", "chjmD"
3. Invalid Examples: "Jcn934" (6 chars), "Jc93" (4 chars), "Jcn-3" (hyphen)
4. If CSV shows "-" or empty, set to null
5. MUST match exactly - no modifications or corrections
6. Case-sensitive matching required
7. Double-check vendor name lookup is exact match (case-insensitive)
8. If vendor not found in CSV, both chart IDs must be null

COMPANY SIZE VALIDATION:
The best_for_company_size field MUST be one of these exact values:
- "micro" - For micro/small businesses (1-10 employees)
- "smb" - For small to medium businesses (11-100 employees)
- "all" - For all company sizes (no specific restriction)
- "midMarketEnterprise" - For mid-market and enterprise companies (100+ employees)

CRITICAL: You MUST use one of these exact four values: "micro", "smb", "all", or "midMarketEnterprise"
DO NOT use: "small", "Small", "medium", "Medium", "large", "Large", "enterprise", or any other variations.
If the vendor targets multiple sizes or has "even distribution", use "all".
If unsure, default to "all".

GRAMMAR RULES for best_for_editorial:
- Always use "businesses that" instead of "businesses who"
- Example: "Best for businesses that need advanced project management"  
- NEVER write: "Best for businesses who need advanced project management"
- This field should describe the ideal customer profile in proper English
- Also apply this rule to all variations: "companies that", "teams that", "organizations that" (not "who")`,
  },
  {
    role: 'user',
    content: `Vendor: {{vendorName}}

Content:
{{content}}`,
  },
];
