name: <PERSON><PERSON>endor Comparison Pipeline
description: |
  Generates comprehensive vendor comparison content following editorial guidelines.
  Creates data-backed comparisons with proper parent/child vendor relationships,
  SEO-optimized content, and Sanity CMS integration.

input_schema:
  primaryVendorName:
    type: string
    position: 0
    description: Primary vendor name for comparison
    required: true
  comparisonVendorName:
    type: string
    position: 1
    description: Vendor to compare against primary vendor
    required: true
  category:
    type: string
    position: 2
    description: Product category (e.g., "project management", "CRM", "marketing automation")
    required: false
  primaryVendorRefID:
    type: string
    position: 3
    description: Sanity document ID of the primary vendor (for creating references)
    required: false
  comparisonVendorRefID:
    type: string
    position: 4
    description: Sanity document ID of the comparison vendor (for creating references)
    required: false
  researchUrls:
    type: string
    position: 5
    description: Comma-separated list of research URLs to scrape for vendor information
    required: false
  additionalContext:
    type: string
    position: 6
    description: Additional context or specific requirements for the comparison
    required: false

output_schema:
  name:
    type: string
    position: 0
    description: Comparison page name/title
  slug:
    type: object
    position: 1
    description: URL slug for the comparison page
  heroTitle:
    type: string
    position: 2
    description: Hero section title following format "[Vendor 1] vs. [Vendor 2]: a data-backed comparison"
  heroBody:
    type: string
    position: 3
    description: Hero section body describing the comparison focus
  atAGlanceContent:
    type: string
    position: 4
    description: At-a-glance comparison content (60-80 words)
  primaryVendorOverview:
    type: string
    position: 5
    description: Primary vendor overview (60-70 words)
  comparisonVendorOverview:
    type: string
    position: 6
    description: Comparison vendor overview (60-70 words)
  useCaseSummary:
    type: string
    position: 7
    description: Summary of how the two vendors differ in use cases
  primaryVendorUseCases:
    type: array
    position: 8
    description: Array of use cases where primary vendor is better choice
  comparisonVendorUseCases:
    type: array
    position: 9
    description: Array of use cases where comparison vendor is better choice
  metaTitle:
    type: string
    position: 10
    description: SEO meta title (hero title in title case)
  metaDescription:
    type: string
    position: 11
    description: SEO meta description (max 160 characters)
  hubTopic:
    type: string
    position: 12
    description: Hub topic for content organization
  publishingTeam:
    type: string
    position: 13
    description: Publishing team responsible for content
  primaryVendorRefID:
    type: string
    position: 14
    description: Sanity reference ID for primary vendor
  comparisonVendorRefID:
    type: string
    position: 15
    description: Sanity reference ID for comparison vendor
  category:
    type: string
    position: 16
    description: Product category
  publishedAt:
    type: string
    position: 17
    description: Publication timestamp
  source:
    type: string
    position: 18
    description: Content source workflow name

steps:
  - id: generateContent
    function_name: rampVendorComparisonWorkflow
    timeout: 300000
    retry_policy:
      startToCloseTimeout: 300000
      initialInterval: 1000
      maximumInterval: 10000
      backoffCoefficient: 2
      maximumAttempts: 3
      nonRetryableErrorTypes: ['UNRECOVERABLE_ERROR', 'INVALID_INPUT']
    inputs:
      primaryVendorName: ${pipeline.input.primaryVendorName}
      comparisonVendorName: ${pipeline.input.comparisonVendorName}
      category: ${pipeline.input.category}
      researchData: "Research data for ${pipeline.input.primaryVendorName} vs ${pipeline.input.comparisonVendorName} comparison"
      context: "${artifacts.ramp_vendor_comparison_guidelines}"
    outputs:
      name: ${steps.generateContent.output.name}
      slug: ${steps.generateContent.output.slug}
      heroTitle: ${steps.generateContent.output.heroTitle}
      heroBody: ${steps.generateContent.output.heroBody}
      atAGlanceContent: ${steps.generateContent.output.atAGlanceContent}
      primaryVendorOverview: ${steps.generateContent.output.primaryVendorOverview}
      comparisonVendorOverview: ${steps.generateContent.output.comparisonVendorOverview}
      useCaseSummary: ${steps.generateContent.output.useCaseSummary}
      primaryVendorUseCases: ${steps.generateContent.output.primaryVendorUseCases}
      comparisonVendorUseCases: ${steps.generateContent.output.comparisonVendorUseCases}
      metaTitle: ${steps.generateContent.output.metaTitle}
      metaDescription: ${steps.generateContent.output.metaDescription}
      hubTopic: ${steps.generateContent.output.hubTopic}
      publishingTeam: ${steps.generateContent.output.publishingTeam}
      primaryVendorRefID: ${steps.generateContent.output.primaryVendorRefID}
      comparisonVendorRefID: ${steps.generateContent.output.comparisonVendorRefID}
      category: ${steps.generateContent.output.category}
      publishedAt: ${steps.generateContent.output.publishedAt}
      source: ${steps.generateContent.output.source}

  - id: publishToSanity
    function_name: sanityPublisherWorkflow
    timeout: 120000
    retry_policy:
      startToCloseTimeout: 120000
      initialInterval: 1000
      maximumInterval: 5000
      backoffCoefficient: 2
      maximumAttempts: 3
      nonRetryableErrorTypes: ['UNRECOVERABLE_ERROR', 'INVALID_INPUT']
    inputs:
      document:
        name: ${steps.generateContent.output.name}
        slug: ${steps.generateContent.output.slug}
        heroTitle: ${steps.generateContent.output.heroTitle}
        heroBody: ${steps.generateContent.output.heroBody}
        atAGlanceContent: ${steps.generateContent.output.atAGlanceContent}
        primaryVendorOverview: ${steps.generateContent.output.primaryVendorOverview}
        comparisonVendorOverview: ${steps.generateContent.output.comparisonVendorOverview}
        useCaseSummary: ${steps.generateContent.output.useCaseSummary}
        primaryVendorUseCases: ${steps.generateContent.output.primaryVendorUseCases}
        comparisonVendorUseCases: ${steps.generateContent.output.comparisonVendorUseCases}
        metaTitle: ${steps.generateContent.output.metaTitle}
        metaDescription: ${steps.generateContent.output.metaDescription}
        hubTopic: ${steps.generateContent.output.hubTopic}
        publishingTeam: ${steps.generateContent.output.publishingTeam}
        primaryVendorRefID: ${steps.generateContent.output.primaryVendorRefID}
        comparisonVendorRefID: ${steps.generateContent.output.comparisonVendorRefID}
        category: ${steps.generateContent.output.category}
        publishedAt: ${steps.generateContent.output.publishedAt}
        source: ${steps.generateContent.output.source}
      options:
        instanceName: ramp
        dataset: production
        documentType: vendorComparison
        asDraft: true
        createIfNotExists: true
    outputs:
      documentId: ${steps.publishToSanity.output.documentId}
      documentUrl: ${steps.publishToSanity.output.documentUrl}
      publishedAt: ${steps.publishToSanity.output.publishedAt}

tags:
  - vendor-comparison
  - content-generation
  - sanity-cms
  - seo-optimized

metadata:
  version: "1.0.0"
  author: "Ramp Vendor Comparison Workflow"
  created_date: "2025-01-11"
  editorial_template: "VENDOR_COMPARISON_EDITORIAL_TEMPLATE.md"
  sanity_document_type: "vendorComparison"
  reference_pattern: "convention-based (RefID/RefIDs suffixes)"