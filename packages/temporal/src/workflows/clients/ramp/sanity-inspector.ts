#!/usr/bin/env node

/**
 * Ramp Sanity CMS Inspector <PERSON>
 *
 * This script connects to <PERSON><PERSON>'s Sanity CMS to inspect:
 * - Vendor records
 * - Vendor Alternative records
 * - Vendor Comparison records
 * - Their associations and relationships
 *
 * Usage: node sanity-inspector.ts [command] [options]
 *
 * Commands:
 * - vendors: List all vendor records
 * - alternatives: List all vendor alternative records
 * - comparisons: List all vendor comparison records
 * - schema: Show document type schemas
 * - vendor [slug]: Show specific vendor details
 * - relationships [slug]: Show vendor relationships
 */

import { workerModuleFactory } from '@flow/sdk/lib/worker-module.js';
import { ensurePresent } from '@flow/sdk/lib/ensure-present-helper.js';
import type { SanityConfig } from '@flow/sdk/apis/sanity/types.js';

interface VendorDocument {
  _id: string;
  _type: string;
  name: string;
  slug?: { current: string };
  category?: string;
  website?: string;
  developer?: string;
  description?: string;
  hero?: {
    title?: string;
    body?: string;
  };
  overview?: {
    content?: string;
  };
  features?: {
    content?: string;
  };
  pricing?: {
    table?: string;
    hasFreeTier?: boolean;
  };
  prosAndCons?: {
    pros?: string;
    cons?: string;
  };
  g2?: {
    score?: number;
    link?: string;
  };
  recommendations?: {
    companySize?: string;
    editorial?: string;
  };
  publishedAt?: string;
  source?: string;
  _createdAt?: string;
  _updatedAt?: string;
}

interface VendorAlternativeDocument {
  _id: string;
  _type: string;
  name: string;
  slug?: { current: string };
  primaryVendor?: {
    _ref: string;
    _type: string;
  };
  alternatives?: Array<{
    _ref: string;
    _type: string;
  }>;
  _createdAt?: string;
  _updatedAt?: string;
}

interface VendorComparisonDocument {
  _id: string;
  _type: string;
  name: string;
  slug?: { current: string };
  vendors?: Array<{
    _ref: string;
    _type: string;
  }>;
  comparisonContent?: string;
  _createdAt?: string;
  _updatedAt?: string;
}

class RampSanityInspector {
  private client: ReturnType<ReturnType<typeof workerModuleFactory>['createSanityApiClient']>;

  constructor(dataset: string = 'production') {
    const config = this.getSanityConfig(dataset);
    const workerModule = workerModuleFactory();
    this.client = workerModule.createSanityApiClient(config);
  }

  private getSanityConfig(dataset: string): SanityConfig {
    const projectId = ensurePresent(process.env.RAMP_SANITY_PROJECT_ID, 'RAMP_SANITY_PROJECT_ID');
    const token = ensurePresent(process.env.RAMP_SANITY_API_TOKEN, 'RAMP_SANITY_API_TOKEN');
    const apiVersion = process.env.RAMP_SANITY_API_VERSION || 'v2021-10-21';

    return {
      projectId,
      dataset,
      apiVersion,
      token,
    };
  }

  async listVendors(limit = 10): Promise<VendorDocument[]> {
    const query = `*[_type == "vendor"] | order(_createdAt desc) [0...${limit}] {
      _id,
      _type,
      name,
      slug,
      category,
      website,
      developer,
      description,
      hero,
      overview,
      features,
      pricing,
      prosAndCons,
      g2,
      recommendations,
      publishedAt,
      source,
      _createdAt,
      _updatedAt
    }`;

    const response = await this.client.query<VendorDocument[]>(query);
    return response.result;
  }

  async listVendorAlternatives(limit = 10): Promise<VendorAlternativeDocument[]> {
    const query = `*[_type == "vendorAlternative"] | order(_createdAt desc) [0...${limit}] {
      _id,
      _type,
      name,
      slug,
      primaryVendor,
      alternatives,
      _createdAt,
      _updatedAt
    }`;

    const response = await this.client.query<VendorAlternativeDocument[]>(query);
    return response.result;
  }

  async listVendorComparisons(limit = 10): Promise<VendorComparisonDocument[]> {
    const query = `*[_type == "vendorComparison"] | order(_createdAt desc) [0...${limit}] {
      _id,
      _type,
      name,
      slug,
      vendors,
      comparisonContent,
      _createdAt,
      _updatedAt
    }`;

    const response = await this.client.query<VendorComparisonDocument[]>(query);
    return response.result;
  }

  async getVendorBySlug(slug: string): Promise<VendorDocument | null> {
    const query = `*[_type == "vendor" && slug.current == $slug][0] {
      _id,
      _type,
      name,
      slug,
      category,
      website,
      developer,
      description,
      hero,
      overview,
      features,
      pricing,
      prosAndCons,
      g2,
      recommendations,
      publishedAt,
      source,
      _createdAt,
      _updatedAt
    }`;

    const response = await this.client.query<VendorDocument>(query, { slug });
    return response.result;
  }

  async getVendorBrandInfo(slug: string): Promise<any> {
    const query = `*[_type == "vendor" && slug.current == $slug][0] {
      _id,
      name,
      slug,
      brand {
        logo,
        logoAlt,
        colors,
        socialLinks
      },
      company {
        size,
        foundedYear,
        industry,
        location
      }
    }`;

    const params = { slug };
    const response = await this.client.query<any>(query, params);
    return response.result;
  }

  async getVendorRelationships(slug: string): Promise<{
    vendor: VendorDocument | null;
    alternatives: VendorAlternativeDocument[];
    comparisons: VendorComparisonDocument[];
    relatedVendors: VendorDocument[];
  }> {
    const vendor = await this.getVendorBySlug(slug);

    if (!vendor) {
      return {
        vendor: null,
        alternatives: [],
        comparisons: [],
        relatedVendors: [],
      };
    }

    // Find alternatives that reference this vendor
    const alternativesQuery = `*[_type == "vendorAlternative" && (primaryVendor._ref == $vendorId || $vendorId in alternatives[]._ref)] {
      _id,
      _type,
      name,
      slug,
      primaryVendor,
      alternatives,
      _createdAt,
      _updatedAt
    }`;

    const alternatives = await this.client.query<VendorAlternativeDocument[]>(alternativesQuery, {
      vendorId: vendor._id,
    });

    // Find comparisons that reference this vendor
    const comparisonsQuery = `*[_type == "vendorComparison" && $vendorId in vendors[]._ref] {
      _id,
      _type,
      name,
      slug,
      vendors,
      comparisonContent,
      _createdAt,
      _updatedAt
    }`;

    const comparisons = await this.client.query<VendorComparisonDocument[]>(comparisonsQuery, {
      vendorId: vendor._id,
    });

    // Get related vendors from alternatives and comparisons
    const relatedVendorIds = new Set<string>();

    alternatives.result.forEach((alt) => {
      if (alt.primaryVendor?._ref && alt.primaryVendor._ref !== vendor._id) {
        relatedVendorIds.add(alt.primaryVendor._ref);
      }
      alt.alternatives?.forEach((altRef) => {
        if (altRef._ref !== vendor._id) {
          relatedVendorIds.add(altRef._ref);
        }
      });
    });

    comparisons.result.forEach((comp) => {
      comp.vendors?.forEach((vendorRef) => {
        if (vendorRef._ref !== vendor._id) {
          relatedVendorIds.add(vendorRef._ref);
        }
      });
    });

    // Fetch related vendors
    const relatedVendors =
      relatedVendorIds.size > 0
        ? await this.client.query<VendorDocument[]>(
            `*[_type == "vendor" && _id in $ids] { _id, _type, name, slug, category, website }`,
            { ids: Array.from(relatedVendorIds) }
          )
        : { result: [] };

    return {
      vendor,
      alternatives: alternatives.result,
      comparisons: comparisons.result,
      relatedVendors: relatedVendors.result,
    };
  }

  async getDocumentTypeSchemas(): Promise<unknown> {
    // Get sample documents to understand schema structure
    const vendorSample = await this.client.query<VendorDocument[]>(
      `*[_type == "vendor"][0...1] { ... }`
    );

    const altSample = await this.client.query<VendorAlternativeDocument[]>(
      `*[_type == "vendorAlternative"][0...1] { ... }`
    );

    const compSample = await this.client.query<VendorComparisonDocument[]>(
      `*[_type == "vendorComparison"][0...1] { ... }`
    );

    return {
      vendor: vendorSample.result[0] || null,
      vendorAlternative: altSample.result[0] || null,
      vendorComparison: compSample.result[0] || null,
    };
  }

  async getDocumentCounts(): Promise<{
    vendors: number;
    vendorAlternatives: number;
    vendorComparisons: number;
  }> {
    const vendorCount = await this.client.query<number>(`count(*[_type == "vendor"])`);
    const altCount = await this.client.query<number>(`count(*[_type == "vendorAlternative"])`);
    const compCount = await this.client.query<number>(`count(*[_type == "vendorComparison"])`);

    return {
      vendors: vendorCount.result,
      vendorAlternatives: altCount.result,
      vendorComparisons: compCount.result,
    };
  }

  async getDocumentById(id: string): Promise<any> {
    const query = `*[_id == $id][0] { 
      ...,
      primaryVendor-> {
        _id,
        name,
        slug,
        category,
        website
      },
      alternatives[]-> {
        _id,
        name,
        slug,
        category,
        website
      }
    }`;

    const response = await this.client.query<any>(query, { id });
    return response.result;
  }

  async getDistinctFieldValues(type: string, field: string): Promise<any[]> {
    const query = `array::unique(*[_type == $type && defined(${field})].${field})`;
    const response = await this.client.query<any[]>(query, { type });
    return response.result;
  }

  async getFieldValueDistribution(type: string, field: string): Promise<Record<string, number>> {
    const query = `*[_type == $type && defined(${field})]{
      "${field}": ${field}
    }`;
    const response = await this.client.query<any[]>(query, { type });

    const distribution: Record<string, number> = {};
    response.result.forEach((doc) => {
      const value = JSON.stringify(doc[field]);
      distribution[value] = (distribution[value] || 0) + 1;
    });

    return distribution;
  }

  async searchByFieldValue(type: string, field: string, value: string): Promise<any[]> {
    const query = `*[_type == $type && ${field} == $value] | order(_createdAt desc) [0...20] {
      _id,
      name,
      slug,
      ${field},
      _createdAt,
      _updatedAt
    }`;
    const response = await this.client.query<any[]>(query, { type, value });
    return response.result;
  }

  async validateDocument(id: string): Promise<{
    isValid: boolean;
    issues: string[];
    document: any;
  }> {
    const doc = await this.getDocumentById(id);
    const issues: string[] = [];

    if (!doc) {
      return { isValid: false, issues: ['Document not found'], document: null };
    }

    // Validate based on document type
    if (doc._type === 'vendor') {
      // Check required fields
      if (!doc.name) issues.push('Missing required field: name');
      if (!doc.category) issues.push('Missing required field: category');

      // Check bestForCompanySize values
      if (doc.bestForCompanySize) {
        const validSizes = ['micro', 'small', 'medium', 'large', 'enterprise'];
        const lowerValue = doc.bestForCompanySize.toLowerCase();
        if (!validSizes.includes(lowerValue)) {
          issues.push(
            `Invalid bestForCompanySize: "${doc.bestForCompanySize}" (expected: ${validSizes.join(', ')})`
          );
        }
      }

      // Check content is blocks
      if (doc.content && !Array.isArray(doc.content)) {
        issues.push('Content should be an array of blocks');
      }

      // Check pros/cons are blocks
      if (doc.pros && !Array.isArray(doc.pros)) {
        issues.push('Pros should be an array of blocks');
      }
      if (doc.cons && !Array.isArray(doc.cons)) {
        issues.push('Cons should be an array of blocks');
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      document: doc,
    };
  }

  async compareDocuments(
    id1: string,
    id2: string
  ): Promise<{
    doc1: any;
    doc2: any;
    differences: string[];
  }> {
    const doc1 = await this.getDocumentById(id1);
    const doc2 = await this.getDocumentById(id2);
    const differences: string[] = [];

    if (!doc1 || !doc2) {
      return { doc1, doc2, differences: ['One or both documents not found'] };
    }

    // Get all fields
    const allFields = new Set([
      ...Object.keys(doc1).filter((k) => !k.startsWith('_')),
      ...Object.keys(doc2).filter((k) => !k.startsWith('_')),
    ]);

    allFields.forEach((field) => {
      const val1 = JSON.stringify(doc1[field]);
      const val2 = JSON.stringify(doc2[field]);

      if (val1 !== val2) {
        differences.push(`${field}: ${val1?.substring(0, 50)}... vs ${val2?.substring(0, 50)}...`);
      }
    });

    return { doc1, doc2, differences };
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';

  // Check for --dataset flag
  let dataset = 'production';
  const datasetFlag = args.find((arg) => arg.startsWith('--dataset='));
  if (datasetFlag) {
    dataset = datasetFlag.split('=')[1];
    // Remove dataset flag from args
    args.splice(args.indexOf(datasetFlag), 1);
  }

  const inspector = new RampSanityInspector(dataset);
  console.log(`🗄️  Using dataset: ${dataset}`);

  try {
    switch (command) {
      case 'vendors': {
        const limit = parseInt(args[1]) || 10;
        const vendors = await inspector.listVendors(limit);
        console.log(`\n📋 Found ${vendors.length} vendors:\n`);
        vendors.forEach((vendor, i) => {
          console.log(`${i + 1}. ${vendor.name} (${vendor.slug?.current || 'no-slug'})`);
          console.log(`   Category: ${vendor.category || 'N/A'}`);
          console.log(`   Website: ${vendor.website || 'N/A'}`);
          console.log(
            `   Created: ${vendor._createdAt ? new Date(vendor._createdAt).toLocaleDateString() : 'N/A'}`
          );
          console.log(`   ID: ${vendor._id}\n`);
        });
        break;
      }

      case 'alternatives': {
        const altLimit = parseInt(args[1]) || 10;
        const alternatives = await inspector.listVendorAlternatives(altLimit);
        console.log(`\n🔄 Found ${alternatives.length} vendor alternatives:\n`);
        alternatives.forEach((alt, i) => {
          console.log(`${i + 1}. ${alt.name} (${alt.slug?.current || 'no-slug'})`);
          console.log(`   Primary Vendor: ${alt.primaryVendor?._ref || 'N/A'}`);
          console.log(`   Alternatives: ${alt.alternatives?.length || 0} vendors`);
          console.log(
            `   Created: ${alt._createdAt ? new Date(alt._createdAt).toLocaleDateString() : 'N/A'}`
          );
          console.log(`   ID: ${alt._id}\n`);
        });
        break;
      }

      case 'comparisons': {
        const compLimit = parseInt(args[1]) || 10;
        const comparisons = await inspector.listVendorComparisons(compLimit);
        console.log(`\n⚖️  Found ${comparisons.length} vendor comparisons:\n`);
        comparisons.forEach((comp, i) => {
          console.log(`${i + 1}. ${comp.name} (${comp.slug?.current || 'no-slug'})`);
          console.log(`   Vendors: ${comp.vendors?.length || 0} vendors`);
          console.log(
            `   Created: ${comp._createdAt ? new Date(comp._createdAt).toLocaleDateString() : 'N/A'}`
          );
          console.log(`   ID: ${comp._id}\n`);
        });
        break;
      }

      case 'vendor': {
        const slug = args[1];
        if (!slug) {
          console.error('❌ Please provide a vendor slug');
          process.exit(1);
        }
        const vendor = await inspector.getVendorBySlug(slug);
        if (!vendor) {
          console.log(`❌ Vendor with slug "${slug}" not found`);
          process.exit(1);
        }
        console.log(`\n🏢 Vendor: ${vendor.name}\n`);
        console.log(`Slug: ${vendor.slug?.current || 'N/A'}`);
        console.log(`Category: ${vendor.category || 'N/A'}`);
        console.log(`Website: ${vendor.website || 'N/A'}`);
        console.log(`Developer: ${vendor.developer || 'N/A'}`);
        console.log(`Description: ${vendor.description || 'N/A'}`);
        console.log(`G2 Score: ${vendor.g2?.score || 'N/A'}`);
        console.log(`Has Free Tier: ${vendor.pricing?.hasFreeTier ? 'Yes' : 'No'}`);
        console.log(
          `Published: ${vendor.publishedAt ? new Date(vendor.publishedAt).toLocaleDateString() : 'N/A'}`
        );
        console.log(`Source: ${vendor.source || 'N/A'}`);
        console.log(
          `Created: ${vendor._createdAt ? new Date(vendor._createdAt).toLocaleDateString() : 'N/A'}`
        );
        console.log(
          `Updated: ${vendor._updatedAt ? new Date(vendor._updatedAt).toLocaleDateString() : 'N/A'}`
        );
        break;
      }

      case 'relationships': {
        const relSlug = args[1];
        if (!relSlug) {
          console.error('❌ Please provide a vendor slug');
          process.exit(1);
        }
        const relationships = await inspector.getVendorRelationships(relSlug);
        if (!relationships.vendor) {
          console.log(`❌ Vendor with slug "${relSlug}" not found`);
          process.exit(1);
        }
        console.log(`\n🔗 Relationships for: ${relationships.vendor.name}\n`);
        console.log(`🔄 Alternatives (${relationships.alternatives.length}):`);
        relationships.alternatives.forEach((alt) => {
          console.log(`  - ${alt.name} (${alt.slug?.current || 'no-slug'})`);
        });
        console.log(`\n⚖️  Comparisons (${relationships.comparisons.length}):`);
        relationships.comparisons.forEach((comp) => {
          console.log(`  - ${comp.name} (${comp.slug?.current || 'no-slug'})`);
        });
        console.log(`\n🏢 Related Vendors (${relationships.relatedVendors.length}):`);
        relationships.relatedVendors.forEach((vendor) => {
          console.log(`  - ${vendor.name} (${vendor.slug?.current || 'no-slug'})`);
        });
        break;
      }

      case 'schema': {
        const schemas = await inspector.getDocumentTypeSchemas();
        console.log('\n📋 Document Type Schemas:\n');
        console.log('🏢 Vendor Schema:');
        console.log(JSON.stringify((schemas as any).vendor, null, 2));
        console.log('\n🔄 Vendor Alternative Schema:');
        console.log(JSON.stringify((schemas as any).vendorAlternative, null, 2));
        console.log('\n⚖️  Vendor Comparison Schema:');
        console.log(JSON.stringify((schemas as any).vendorComparison, null, 2));
        break;
      }

      case 'brand': {
        const slug = args[1];
        if (!slug) {
          console.error('Usage: brand <slug>');
          return;
        }

        const brandInfo = await inspector.getVendorBrandInfo(slug);
        if (!brandInfo) {
          console.error(`❌ Vendor with slug "${slug}" not found`);
          return;
        }

        console.log(`\n🎨 Brand Information for: ${brandInfo.name}`);
        console.log('');

        if (brandInfo.brand) {
          console.log('🖼️  Logo Information:');
          console.log(`Primary Logo: ${brandInfo.brand.logo || 'N/A'}`);
          console.log(`Alternative Logo: ${brandInfo.brand.logoAlt || 'N/A'}`);
          console.log(`Brand Colors: ${brandInfo.brand.colors || 'N/A'}`);
          console.log(`Social Links: ${brandInfo.brand.socialLinks || 'N/A'}`);
        } else {
          console.log('❌ No brand information found');
        }

        if (brandInfo.company) {
          console.log('\n🏢 Company Information:');
          console.log(`Company Size: ${brandInfo.company.size || 'N/A'}`);
          console.log(`Founded Year: ${brandInfo.company.foundedYear || 'N/A'}`);
          console.log(`Industry: ${brandInfo.company.industry || 'N/A'}`);
          console.log(`Location: ${brandInfo.company.location || 'N/A'}`);
        } else {
          console.log('\n❌ No company information found');
        }
        break;
      }

      case 'stats': {
        const counts = await inspector.getDocumentCounts();
        console.log('\n📊 Document Statistics:\n');
        console.log(`🏢 Vendors: ${counts.vendors}`);
        console.log(`🔄 Vendor Alternatives: ${counts.vendorAlternatives}`);
        console.log(`⚖️  Vendor Comparisons: ${counts.vendorComparisons}`);
        console.log(
          `📝 Total Documents: ${counts.vendors + counts.vendorAlternatives + counts.vendorComparisons}`
        );
        break;
      }

      case 'document': {
        const documentId = args[1];
        if (!documentId) {
          console.error('❌ Please provide a document ID');
          process.exit(1);
        }

        const document = await inspector.getDocumentById(documentId);
        if (!document) {
          console.log(`❌ Document with ID "${documentId}" not found`);
          process.exit(1);
        }

        console.log(`\n📄 Document: ${document._id}\n`);
        console.log(`Type: ${document._type}`);
        console.log(`Name: ${document.name || 'N/A'}`);
        console.log(`Slug: ${document.slug?.current || 'N/A'}`);

        if (document._type === 'vendorAlternative') {
          console.log(`\n🔗 Primary Vendor:`);
          if (document.primaryVendor) {
            console.log(`  Name: ${document.primaryVendor.name || 'N/A'}`);
            console.log(`  Slug: ${document.primaryVendor.slug?.current || 'N/A'}`);
            console.log(`  Category: ${document.primaryVendor.category || 'N/A'}`);
            console.log(`  Website: ${document.primaryVendor.website || 'N/A'}`);
            console.log(`  ID: ${document.primaryVendor._id}`);
          } else {
            console.log(`  ❌ No primary vendor found`);
          }

          console.log(`\n🔄 Alternatives (${document.alternatives?.length || 0}):`);
          if (document.alternatives && document.alternatives.length > 0) {
            document.alternatives.forEach((alt: any, i: number) => {
              console.log(`  ${i + 1}. ${alt.name || 'N/A'} (${alt.slug?.current || 'no-slug'})`);
              console.log(`     Category: ${alt.category || 'N/A'}`);
              console.log(`     Website: ${alt.website || 'N/A'}`);
              console.log(`     ID: ${alt._id}`);
            });
          } else {
            console.log(`  ❌ No alternatives found`);
          }
        }

        console.log(`\n📋 Full Document Structure:`);
        console.log(JSON.stringify(document, null, 2));
        break;
      }

      case 'values': {
        const type = args[1];
        const field = args[2];
        if (!type || !field) {
          console.error('❌ Usage: values <type> <field>');
          process.exit(1);
        }

        const values = await inspector.getDistinctFieldValues(type, field);
        console.log(`\n🔢 Distinct values for ${type}.${field}:\n`);

        if (values.length === 0) {
          console.log('❌ No values found');
        } else {
          values.forEach((value, i) => {
            console.log(`${i + 1}. ${JSON.stringify(value)}`);
          });

          // Get distribution
          const distribution = await inspector.getFieldValueDistribution(type, field);
          console.log(`\n📊 Value distribution:`);
          Object.entries(distribution).forEach(([value, count]) => {
            console.log(`  ${value}: ${count} documents`);
          });
        }
        break;
      }

      case 'search': {
        const type = args[1];
        const field = args[2];
        const value = args[3];
        if (!type || !field || !value) {
          console.error('❌ Usage: search <type> <field> <value>');
          process.exit(1);
        }

        const results = await inspector.searchByFieldValue(type, field, value);
        console.log(`\n🔎 Documents where ${type}.${field} = "${value}":\n`);

        if (results.length === 0) {
          console.log('❌ No documents found');
        } else {
          results.forEach((doc, i) => {
            console.log(`${i + 1}. ${doc.name || doc._id}`);
            console.log(`   ID: ${doc._id}`);
            console.log(`   Slug: ${doc.slug?.current || 'N/A'}`);
            console.log(`   ${field}: ${JSON.stringify(doc[field])}`);
            console.log(`   Created: ${new Date(doc._createdAt).toLocaleDateString()}\n`);
          });
        }
        break;
      }

      case 'validate': {
        const documentId = args[1];
        if (!documentId) {
          console.error('❌ Please provide a document ID');
          process.exit(1);
        }

        const validation = await inspector.validateDocument(documentId);
        console.log(`\n✓ Validating document: ${documentId}\n`);

        if (validation.isValid) {
          console.log('✅ Document is valid!');
        } else {
          console.log(`❌ Found ${validation.issues.length} validation issues:`);
          validation.issues.forEach((issue, i) => {
            console.log(`${i + 1}. ${issue}`);
          });
        }

        if (validation.document) {
          console.log(`\n📄 Document summary:`);
          console.log(`Type: ${validation.document._type}`);
          console.log(`Name: ${validation.document.name || 'N/A'}`);
          console.log(`Best For Company Size: ${validation.document.bestForCompanySize || 'N/A'}`);
        }
        break;
      }

      case 'compare': {
        const id1 = args[1];
        const id2 = args[2];
        if (!id1 || !id2) {
          console.error('❌ Usage: compare <id1> <id2>');
          process.exit(1);
        }

        const comparison = await inspector.compareDocuments(id1, id2);
        console.log(`\n🔄 Comparing documents:\n`);

        if (!comparison.doc1 || !comparison.doc2) {
          console.log('❌ One or both documents not found');
        } else {
          console.log(`Document 1: ${comparison.doc1.name || comparison.doc1._id}`);
          console.log(`Document 2: ${comparison.doc2.name || comparison.doc2._id}`);

          if (comparison.differences.length === 0) {
            console.log('\n✅ Documents are identical');
          } else {
            console.log(`\n⚠️  Found ${comparison.differences.length} differences:`);
            comparison.differences.forEach((diff, i) => {
              console.log(`${i + 1}. ${diff}`);
            });
          }
        }
        break;
      }

      case 'help':
      default:
        console.log(`
🔍 Ramp Sanity CMS Inspector

Usage: node sanity-inspector.ts [command] [options] [--dataset=production|dev]

Commands:
  vendors [limit]           List vendor records (default: 10)
  alternatives [limit]      List vendor alternative records (default: 10)
  comparisons [limit]       List vendor comparison records (default: 10)
  vendor <slug>            Show specific vendor details
  brand <slug>             Show brand information (logos, colors, social links)
  relationships <slug>     Show vendor relationships
  document <id>            Show specific document by ID with full structure
  schema                   Show document type schemas
  stats                    Show document counts
  values <type> <field>    Get distinct values for a field (e.g., bestForCompanySize)
  search <type> <field> <value>  Search documents by field value
  validate <id>            Validate a document against schema rules
  compare <id1> <id2>      Compare two documents side by side
  help                     Show this help message

Options:
  --dataset=production     Use production dataset (default)
  --dataset=dev           Use development dataset

Environment Variables Required:
  RAMP_SANITY_PROJECT_ID   - Ramp's Sanity project ID
  RAMP_SANITY_API_TOKEN    - Ramp's Sanity API token

Examples:
  node sanity-inspector.ts vendors 20
  node sanity-inspector.ts vendor modal --dataset=production
  node sanity-inspector.ts values vendor bestForCompanySize
  node sanity-inspector.ts search vendor bestForCompanySize Small
  node sanity-inspector.ts validate drafts.modal
  node sanity-inspector.ts compare drafts.modal pipedrive
  node sanity-inspector.ts document drafts.vendor-1753193555637
        `);
        break;
    }
  } catch (error) {
    console.error('❌ Error:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  void main();
}

export { RampSanityInspector };
