<!-- Generated on 2025-07-07T19:25:01.084Z -->
<workflow>
  <functionName>clientsSmithAiWebflowPublishingWorkflow</functionName>
  <displayName>Smith.ai Webflow Publishing</displayName>
  <path>clients/smith-ai/webflow-publishing</path>
  <overview>Publishes content to Smith.ai's Webflow CMS by converting markdown to HTML, mapping content fields to Webflow's schema, and creating collection items via the Webflow API.</overview>
  <flowChart><![CDATA[```mermaid
graph TD
  A[Start] --> B[Validate Input]
  B --> C[Convert Markdown to HTML]
  C --> D[Map Fields to Webflow Format]
  D --> E[Publish Content to Webflow]
  E --> F[Return Publishing Result]
  E --> G{Error?}
  G --> |Yes| H[Handle Error]
  G --> |No| F
```]]></flowChart>
  <activities>
    <activity>
      <functionName>convertMarkdownToHtml</functionName>
      <displayName>Convert Markdown to HTML</displayName>
      <description>Converts markdown content to HTML format using Showdown converter with GitHub Flavored Markdown settings. Configures headers to start at h2 level and enables features like tables, strikethrough, and task lists.</description>
    </activity>
    <activity>
      <functionName>mapFieldsToWebflow</functionName>
      <displayName>Map Fields to Webflow Format</displayName>
      <description>Maps input content fields to Webflow's collection schema. Handles special field types like images, categories, tags, and authors. Converts text values to corresponding enum IDs and generates URL slugs.</description>
    </activity>
    <activity>
      <functionName>publishContent</functionName>
      <displayName>Publish Content to Webflow</displayName>
      <description>Creates a new collection item in Webflow using the mapped field data. Uses Webflow API client to publish content and returns success/failure status with item details or error message.</description>
    </activity>
  </activities>
</workflow>