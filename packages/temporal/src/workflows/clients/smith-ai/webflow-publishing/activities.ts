import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { ensurePresent } from '@flow/sdk/lib/ensure-present-helper.js';
import showdown from 'showdown';
import {
  type BlogInput,
  type PublishingResult,
  type CollectionType,
  collectionConfig,
  type WebflowImage,
  BlogCategory,
  BlogTags,
  Authors,
  normalTextToEnumKey,
} from './types.js';

const webflowApiClient = workerModule.createWebflowApiClient(
  ensurePresent(process.env.SMITH_AI_WEBFLOW_API_KEY, 'SMITH_AI_WEBFLOW_API_KEY')
);

// Helper function to convert URL to Webflow image format
export function createWebflowImage(url: string): WebflowImage {
  // Extract or generate a fileId from the URL
  // For external URLs, we can use a hash or the last part of the URL path
  const urlParts = url.split('/');
  const fileName = urlParts[urlParts.length - 1];
  const fileId = fileName.split('.')[0] || 'external-image';

  return {
    fileId,
    url,
  };
}

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

export async function convertMarkdownToHtml(item: BlogInput): Promise<BlogInput> {
  // Configure Showdown with GitHub Flavored Markdown
  const converter = new showdown.Converter({
    flavor: 'github',
    tables: true,
    strikethrough: true,
    tasklists: true,
    openLinksInNewWindow: true,
    headerLevelStart: 2, // Start headers at h2 since h1 should be the title
  });

  if (item.content) {
    const htmlContent = converter.makeHtml(item.content);
    return {
      ...item,
      content: htmlContent,
    };
  }
  return item;
}

// Helper function to resolve enum values from text input
function resolveEnumValue(value: any, enumObject: Record<string, string>): string | undefined {
  if (typeof value !== 'string') return value;

  // First check if it's already an enum value
  if (Object.values(enumObject).includes(value)) {
    return value;
  }

  // Try to find by enum key match
  const enumKey = normalTextToEnumKey(value);

  const enumValue = Object.entries(enumObject).find(([key]) => key === enumKey)?.[1];

  return enumValue || undefined; // Return undefined if no match found
}

// Helper function to resolve multiple enum values (for MultiReference fields)
function resolveMultiEnumValues(values: any, enumObject: Record<string, string>): string[] {
  if (!values) return [];

  const valuesArray = Array.isArray(values) ? values : [values];
  const resolvedValues: string[] = [];

  for (const value of valuesArray) {
    const resolved = resolveEnumValue(value, enumObject);
    if (resolved) {
      resolvedValues.push(resolved);
    }
  }

  return resolvedValues;
}

export async function mapFieldsToWebflow(
  itemToPublish: BlogInput,
  collectionType: CollectionType
): Promise<Record<string, any>> {
  const config = collectionConfig[collectionType];
  const mapping = config.fieldMapping;

  const webflowItem: Record<string, any> = {};

  // Map each field using the predefined mapping
  for (const [inputField, webflowFields] of Object.entries(mapping)) {
    let value = (itemToPublish as any)[inputField];
    if (value !== undefined) {
      // Special handling for different field types
      if (inputField === 'coverImage' && typeof value === 'string') {
        value = createWebflowImage(value);
      } else if (inputField === 'category') {
        value = resolveMultiEnumValues(value, BlogCategory);
      } else if (inputField === 'tags') {
        value = resolveMultiEnumValues(value, BlogTags);
      } else if (inputField === 'author') {
        value = resolveEnumValue(value, Authors);
      }

      // Map the processed value to multiple Webflow fields
      for (const webflowField of webflowFields) {
        webflowItem[webflowField] = value;
      }
    }
  }

  // Generate slug if not provided
  if (!webflowItem.slug && webflowItem.name) {
    webflowItem.slug = generateSlug(webflowItem.name);
  }

  return webflowItem;
}

export async function publishContent(
  webflowItem: Record<string, any>,
  collectionType: CollectionType
): Promise<PublishingResult> {
  const config = collectionConfig[collectionType];

  try {
    const createdItem = await webflowApiClient.createCollectionItems(config.collectionId, {
      fieldData: {
        name: webflowItem.name || 'Untitled',
        slug: webflowItem.slug || generateSlug(webflowItem.name || 'untitled'),
        'article-link': `https://www.smith.ai/blog/${webflowItem.slug}`,
        'publication-date': new Date().toISOString(),
        'seo-publication-date': new Date().toISOString(),
        ...webflowItem,
      },
    });

    return {
      success: true,
      itemId: createdItem.id,
      originalIndex: 0,
      webflowUrl: `https://webflow.com/design/${config.siteId}/editor/${createdItem.id}`,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      originalIndex: 0,
    };
  }
}

export default WorkflowScope.register(import.meta.url, {
  convertMarkdownToHtml,
  mapFieldsToWebflow,
  publishContent,
});
