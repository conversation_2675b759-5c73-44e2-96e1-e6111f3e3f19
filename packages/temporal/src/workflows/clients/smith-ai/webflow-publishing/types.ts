import { z } from 'zod';

const SMITH_SITE_ID = '5b15ca3d02dd414c89ecc3ed';

// Utility function for text to enum conversion
export function normalTextToEnumKey(text: string): string {
  return text
    .trim()
    .toUpperCase()
    .replace(/[^A-Z0-9]+/g, '_')
    .replace(/^_+|_+$/g, '')
    .replace(/_+/g, '_');
}

// Predefined collection types
export enum CollectionType {
  BLOG_POSTS = 'blog-posts',
}

export enum BlogCategory {
  AI = '64e0024743a19b1d320bbab5',
  AI_RECEPTIONIST = '652d4794d1c670bc8944c80c',
  BUSINESS_EDUCATION = '63ffe7639daa175bce83ced4',
  BUSINESS_SOLUTIONS = '63ffed27449b651464a65bdc',
  CHAT = '63ffe7621681b71497bd9bd7',
  CLIENT_SPOTLIGHT = '63b49d17f112a9fe04f9e274',
  COMPANY_NEWS = '63ffe7610539cf98a183fa8c',
  INTEGRATIONS = '63b49d17f112a95b6af9df5d',
  KEYPAD = '63b49d17f112a9130ff9df6f',
  OUTREACH_CAMPAIGNS = '63b49d17f112a93223f9e241',
  PARTNERSHIPS = '63ffe761f166bc5bc3405ab5',
  PRODUCT_UPDATES = '645d9f329820721956c194a6',
  PROMOTIONS_AND_OFFERS = '645d9f438db94236ab2afa7e',
  VIRTUAL_RECEPTIONISTS = '63ffe761f166bc76b0405ab7',
}

export enum BlogTags {
  CALL_ANSWERING = '63ffe4cff166bc178a3fea68',
  CALL_ANSWERING_COMPARISON = '63ffe4cfcda5722fe52bef74',
  ACCOUNTING = '63ffe4cb7d68f82ea6723b31',
  AFTER_HOURS_ANSWERING = '63ffe4cfe809f5dcaf4cc4b9',
  AGENCY = '63ffe4ceae42133d4261c1af',
  AI = '64e00265adf760339fc50ce9',
  AI_RECEPTIONIST = '652d47a95a63b68ed37b20a5',
  APPOINTMENT_BOOKING = '63ffe4cfabda1214bf0ca7fa',
  APPS_AND_SOFTWARE = '645aa77cf10b092706b7ca39',
  ARCHITECTURE = '63ffe4cbbbe0995b96886b90',
  AUTOMATION = '645aa8852b62e965c238d220',
  AUTOMOTIVE = '63ffe4cbe525be7f18ee934a',
  BILINGUAL_ANSWERING = '63ffe4cfaa4174c5aecef1c8',
  BUSINESS_CONSULTING = '63ffe4cb449b65f9f0a562e0',
  BUSINESS_EDUCATION = '645aa73c84a5323a5b84fe22',
  CALL_INTELLIGENCE = '63ffe4cf449b651691a562fc',
  CALL_RECORDING = '63ffe4cfce03bf334fc25bff',
  CAR_REPAIR = '63ffe4cccda57277912bef05',
  CHAT = '63ffe4c89d6a135de1506b40',
  CHAT_COMPARISON = '63ffe4c8dba05872216a0c54',
  CHILDCARE = '63ffe4cccda572e94e2bef06',
  CHIROPRACTOR = '63ffe4ccebf17e06a95563e6',
  CLEANING_SERVICE = '63ffe4cc1b8a056143caee79',
  COMPANY = '645aa8c7f299ad7d9213068d',
  COMPANY_NEWS = '645aa83791e70d732f5b8183',
  CONSTRUCTION = '63ffe4cce525be0080ee934d',
  CONSULTANT = '63ffe4ce449b655249a562f8',
  CRM = '63ffe4cfae421376af61c1b5',
  E_COMMERCE_AND_RETAIL = '63ffe4cdcda572fd6a2bef45',
  ECOMMERCE_AND_RETAIL = '645aa8f2a0f17b3918c017a1',
  EDUCATION_AND_TUTORING = '645aa9023082e79c883b362a',
  EMAIL_AND_SMS_FOLLOW_UP = '645aaa003e36c07edc052e46',
  EMAIL_SMS_FOLLOW_UP = '63ffe4cf9daa17b88f839aff',
  ENTERPRISE = '63ffe4ce23c1a76c57e22372',
  EVENT_PLANNING_AND_WEDDING = '645aa91684a532adbc8521d0',
  FACEBOOK_MESSENGER = '645aa8a53082e7f10f3b2998',
  FINANCE = '63ffe4cd1681b75a33bd6b9c',
  FLOORING = '63ffe4cde809f552104cc4ab',
  FOOD_DELIVERY_AND_CATERING = '63ffe4ce1db0df5a062c5d9c',
  FRANCHISE = '63ffe4cf2bd1b6561674cd17',
  GRAPHIC_DESIGN = '645aa93249627e65788017e9',
  HOME_AND_GARDEN = '63ffe4cdf166bc577a3fea66',
  HOME_SERVICES = '6489ceb566abf1fa4e01b52f',
  HVAC = '63ffe4cd7d68f828f3723b4c',
  INSURANCE = '63ffe4cd793dbf3b384ec5ac',
  INTEGRATIONS = '63ffe4cb13dd1b855fe74d69',
  IT_AND_MANAGED_SERVICES = '645aa9573441dea9288fd233',
  IT_MANAGED_SERVICES = '63ffe4ce4081d20c2aeeb5d3',
  LAW_FIRM = '63ffe4ce0324c87efc50d259',
  LEAD_GENERATION = '645aa8604261ad759437694d',
  LEAD_SCREENING = '63ffe4cf9d6a13ea26506c0a',
  MARKETING_ADVICE = '645aa8bb2b62e9869138d4c7',
  MARKETING_AGENCY = '63ffe4ce449b65a2c5a562eb',
  MEDICAL_AND_WELLNESS = '63ffe4ce1b8a05feaccaee86',
  MID_MARKET = '63ffe4cfcda5721e6c2bef67',
  OUTBOUND_CALLING = '63ffe4cf0539cf851683bcb8',
  OUTREACH_CAMPAIGNS = '63ffe4c823c1a7dd2ee221fc',
  OUTREACH_CAMPAIGNS_COMPARISON = '63ffe4c87d68f87d30723b25',
  OUTSOURCING = '645aa878fd9f9a8e5aa668b5',
  OVERFLOW_ANSWERING = '63ffe4cfce03bf1c08c25c01',
  PARTNERSHIPS = '63ffe4cb2bd1b656ff74cc86',
  PAYMENT_COLLECTION = '63ffe4cf9daa176b43839b01',
  PEOPLE_AND_PROJECT_MANAGEMENT = '645aa97395ada6500e4f80bd',
  PET_CARE = '63ffe4ce1b8a054917caee88',
  PHOTOGRAPHY = '645aa988a485ec58d2499ed8',
  PLUMBERS = '63ffe4ceebf17e446e556444',
  POOL_AND_HOT_TUB = '63ffe4ce1b8a0537a7caee8a',
  PRIMARY = '656fa899e751ce6469c7fed4',
  PRINT_SHOP = '63ffe4ce0324c8798150d25b',
  PRODUCT_UPDATES = '645d9f37c12d1c474e1f7769',
  PROMOTIONS_OFFERS = '645d9f47452436a3061db2d4',
  PROPERTY_MANAGEMENT = '63ffe4ce2bd1b6256f74cd10',
  REAL_ESTATE = '63ffe4ce2bd1b61e7e74cd11',
  ROOFING = '63ffe4ce13dd1b1a2ce74d77',
  SALES_DEVELOPMENT = '63ffe4d09daa178748839b03',
  SALES_TIPS = '645aa86a928556056e1e0f02',
  SMALL_BUSINESS = '63ffe4cf449b650034a562fa',
  SMS_TEXT_MESSAGING = '645aa89717ffc85fc7a923d5',
  SOFTWARE_SAAS = '63ffe4ce793dbf240c4ec5b3',
  SOLO_BUSINESS = '63ffe4cf4081d25006eeb60d',
  STAFFING_AND_RECRUITING = '63ffe4ce449b653369a562f6',
  TAXI_SERVICES = '63ffe4ceae42131b3361c1ad',
  TEMPORARY_RECEPTIONIST = '63ffe4d00324c825b850d263',
  VETERINARY = '63ffe4ce0539cf37d483bcab',
  VIRTUAL_RECEPTIONISTS = '63ffe4c8ebf17e20b95563c1',
  VIRTUAL_RECEPTIONISTS_COMPARISON = '63ffe4c89d6a1314f2506b3f',
  WARM_PHONE_TRANSFERS = '63ffe4d0e809f528914cc4bb',
}

export enum Authors {
  AARON_HAYNES_1 = '63b49d17f112a96cdbf9e83b',
  AARON_HAYNES_2 = '63b49d17f112a954eaf9e83d',
  AARON_LEE = '64e3c3cc7eeeb2601d654842',
  AARON_MASON = '63b49d17f112a97ecdf9e441',
  AARON_WINSTON = '63b49d17f112a9667af9ef56',
  ADAM_FACHLER = '63b49d17f112a9e66df9e437',
  ADAM_STEELE = '63b49d17f112a91aa6f9e40a',
  ALANA_ISAACS = '63b49d17f112a92abbf9e042',
  ALEX_RYAN = '63b49d17f112a9135cf9ee1e',
  ALEXANDREA_LENZ = '63b49d17f112a91f4af9ede4',
  ALISON_PACUSKA = '63b49d17f112a93a91f9e434',
  ALISTER_ESAM = '63b49d17f112a96aa0f9ede9',
  ALLISON_SHIELDS = '63b49d17f112a92708f9e426',
  ALLISON_SMITH = '63b49d17f112a90cb3f9e413',
  ALYSSA_RILEY = '63b49d17f112a9152cf9e91e',
  ANDREJ_FEDEK = '63b49d17f112a94524f9e8dc',
  ANDREW_PETERS = '63b49d17f112a907c1f9e427',
  ANGELA_SARTAIN = '63b49d17f112a9400df9ee40',
  ANGIE_WILSON = '63b49d17f112a91200f9ee42',
  ANTHONY_MARTIN = '63b49d17f112a98fcdf9ef43',
  ATREYEE_CHOWDHURY = '63b49d17f112a91af9f9e7cb',
  AUSTIN_ANDRUKAITIS = '63ee8810b66ceb64c0b3f7a6',
  BARRY_BLASSOPLES = '63b49d17f112a92a9bf9e432',
  BENCH_CO = '63b49d17f112a963e0f9ee2f',
  BETSY_BARNETT = '63b49d17f112a9cbfdf9e42a',
  BOB_MELTZER = '63b49d17f112a9601ef9e44c',
  BRETT_KNOWLES = '63b49d17f112a97198f9e6d4',
  BRIANNA_BRAILEY = '63b49d17f112a94468f9e455',
  BRYCE_WARNES = '63b49d17f112a92e9ff9e3f7',
  BURKHARD_BERGER = '63b49d17f112a964fdf9eee1',
  CHAD_FAITH = '63b49d17f112a955a6f9e404',
  CHARLES_ALLNUT = '63b49d17f112a9c9b3f9e40f',
  CHELSEA_LAMBERT = '63b49d17f112a972e0f9e6d1',
  CHERYL_KNIGHT = '63b49d17f112a99b43f9ee30',
  CHRIS_MAKARA = '63b49d17f112a908d5f9e40b',
  CHRISTIAN_CABALUNA = '63b49d17f112a93de0f9e456',
  CLARA_JONES = '63b49d17f112a9727bf9e3fe',
  CLAY_HELLER = '63b49d17f112a92050f9e3fa',
  CONOR_MALLOY = '63b49d17f112a9b01ff9e41f',
  DANIEL_WENDEROTH = '63b49d17f112a97318f9e290',
  DANIELLE_WHYTE = '67a0d8350c23196b067a9f20',
  DAVE_GRANT = '63b49d17f112a91580f9e425',
  DAVID_BRENTON = '668d866db159f11727718e21',
  DEAN_FOSTER = '63b49d17f112a934a2f9e3fb',
  DEAN_LESTER = '63b49d17f112a94064f9e3fd',
  DENNIS_VU = '63b49d17f112a939f7f9e9b1',
  DINA_EISENBERG = '63b49d17f112a93958f9e436',
  DORNA_MOINI = '63b49d17f112a91847f9e454',
  ELEA_ANDREA_ALMAZORA_1 = '63b49d17f112a92c50f9e418',
  ELEA_ANDREA_ALMAZORA_2 = '63b49d17f112a94c31f9e419',
  ELIZA_MEDLEY = '63b49d17f112a9bd42f9eece',
  ELIZABETH_LOCKWOOD = '63b49d17f112a99b7ef9e44d',
  EMILY_MILLER = '6838750e2a24d6c3d7e01aed',
  ERIK_PICKERING = '63b49d17f112a90428f9e3f6',
  ERIN_PAGE = '63b49d17f112a92079f9e42b',
  EVE_EPSTEIN = '63b49d17f112a952daf9e412',
  EZRA_SANDZER_BELL = '63b49d17f112a98811f9e9ad',
  GARRETT_OUTLAND = '63b49d17f112a9511af9ee31',
  GERRID_SMITH = '63b49d17f112a9225ff9e42e',
  GRACE_LAU = '63b49d17f112a95dd3f9eda9',
  GRAHAM_NELSON_ZUTTER = '63b49d17f112a95210f9e44b',
  IOANA_WILKINSON = '63b49d17f112a908b8f9e9af',
  IRYNA_BILYK = '63b49d17f112a95d03f9e95a',
  JAKE_KENDELL = '6418838e8e6b3329d54fbf76',
  JAMES_GORSKI = '63b49d17f112a95541f9e400',
  JARED_CORREIA = '63b49d17f112a91b61f9e429',
  JAY_RUANE = '63b49d17f112a9d25cf9e402',
  JEFF_ARMSTRONG = '63b49d17f112a9c00af9edd8',
  JENN_MARIE = '63b49d17f112a9255ff9e4eb',
  JENNA_DINH = '64499af9cb8ecc809dfcc99e',
  JENNIFER_JONES_KELLEY = '63b49d17f112a97d82f9ee0c',
  JEREMY_MOSER = '63b49d17f112a9f6f3f9e291',
  JESS_BIRKIN = '63b49d17f112a97666f9e446',
  JESSICA_DAY = '63b49d17f112a90b3af9ee72',
  JESSICA_KELLEY = '63b49d17f112a923e8f9ed84',
  JESSICA_PAXTON = '63b49d17f112a91bbff9ee7f',
  JIMMY_RODRIGUEZ = '63b49d17f112a96e0af9ee80',
  JOE_DAWSON = '63b49d17f112a93572f9e687',
  JOEL_RUNYON = '63b49d17f112a9f08ff9e41a',
  JOHN_ALLEN_1 = '63b49d17f112a90624f9e43e',
  JOHN_ALLEN_2 = '63b49d17f112a9730af9edea',
  JOLYN_AND_JIM_ARMSTRONG = '63b49d17f112a9236ff9e3f0',
  JON_STROZ = '63b49d17f112a911fcf9e447',
  JON_TOBIN = '63b49d17f112a9a9b2f9e453',
  JORDAN_OSTROFF = '63b49d17f112a98f99f9edec',
  JOSH_YOUNGBLOOD = '63b49d17f112a93ac9f9e449',
  JUSTIE_NICOL = '63b49d17f112a90573f9e41c',
  JUSTIN_AKIMOFF = '63b49d17f112a90e4ef9e43f',
  JUSTIN_MAXWELL = '63b49d17f112a92606f9e8f4',
  KATE_PRIESTMAN = '63b49d17f112a94a21f9e958',
  KATHLEEN_MORAN = '63b49d17f112a93a7af9ee41',
  KELLY_MOSER = '63b49d17f112a975e9f9ee79',
  KELSEY_JOHNSON = '63b49d17f112a9f05ff9e406',
  KENNETH_BURKE = '63b49d17f112a94287f9e452',
  KERI_NORRIS = '63b49d17f112a9f802f9e44a',
  KOA_FREDERICK = '63b49d17f112a982b3f9edab',
  KRISTA_COGGINS = '63b49d17f112a9e966f9e42c',
  KRISTI_CARIGNAN = '63b49d17f112a97482f9e97d',
  KRISTIE_HOLDEN = '63b49d17f112a94763f9e415',
  LIDIA_S_HOVHAN = '63b49d17f112a9b374f9e718',
  LISA_MICHAELS = '63b49d17f112a92a8af9e438',
  LUKA_PIPIRAITE = '63b49d17f112a983aaf9ee8d',
  LYNN_MITCHELL = '63b49d17f112a993fff9e7a0',
  MAC_MISCHKE = '63b49d17f112a98a14f9e45b',
  MADDY_MARTIN = '63b49d17f112a9ff88f9e407',
  MAIA_WELLS = '63b49d17f112a95368f9e3f8',
  MAMIE_NEELY_LEE = '65149074bafbd143541ab30f',
  MARIA_FINTANIDOU = '65f209fa54ae07b89cc7dc21',
  MARIA_MLADENOVSKA = '63b49d17f112a96736f9e409',
  MARIEL_MONTOYA = '63b49d17f112a912c0f9eee6',
  MARK_BURKE = '63b49d17f112a98828f9e40e',
  MARLA_DICARLO = '63b49d17f112a9d35ff9e41d',
  MATT_BUCHANAN = '63b49d17f112a92ef8f9e433',
  MATT_BURKE = '63b49d17f112a9fcb4f9e481',
  MAYA_BIELINSKI = '63b49d17f112a9a5fbf9e3ff',
  MICHAEL_MEYER = '63b49d17f112a918b6f9e50b',
  MICHAEL_NEEDHAM = '63b49d17f112a9898ef9eef3',
  MICKY_DEMING = '63b49d17f112a933fef9e551',
  MIKE_GRANER = '63b49d17f112a9becef9ef36',
  MINDAUGAS_SKURVYDAS = '63b49d17f112a998aff9e450',
  NADICA_METULEVA = '63b49d17f112a9888ef9e8a9',
  NALINI_PRASAD = '63b49d17f112a939c3f9e408',
  NICHOLE_SHAHVERDI = '63b49d17f112a91141f9ef38',
  NICK_EPSON = '63b49d17f112a9036bf9eded',
  NICK_SHAW = '63b49d17f112a95e3ef9ee60',
  OLI_GRAHAM = '63b49d17f112a9290af9e416',
  PATTY_YAN = '63b49d17f112a93e0df9ee90',
  PAUL_GARIBIAN = '63b49d17f112a97b4bf9e431',
  PETER_WAHLBERG = '63b49d17f112a92570f9e401',
  PHIL_PEARCE = '63b49d17f112a94628f9eff2',
  PUJA_SACHDEV = '63b49d17f112a9b97df9e41b',
  RICHARD_CONN_1 = '63b49d17f112a9077bf9e405',
  RICHARD_CONN_2 = '63b49d17f112a92b78f9e7c0',
  RICHARD_FENDLER = '63b49d17f112a97cbcf9e854',
  RICHARD_ROSEN = '63b49d17f112a9ba1bf9e417',
  RITIKA_TIWARI = '63b49d17f112a9725bf9ee1d',
  RITU_GOSWAMY = '63b49d17f112a931faf9e44e',
  ROBERT_BERNAL = '63b49d17f112a9c47bf9e79e',
  ROBERT_WEISS = '63b49d17f112a9fe0bf9e403',
  ROBIN_MICHEAL = '63b49d17f112a944cef9e25b',
  RUMZZ_BAJWA = '63b49d17f112a917cdf9e9a9',
  RYAN_DORE = '63b49d17f112a90ff7f9e44f',
  RYAN_NELL = '63b49d17f112a9ebc0f9edee',
  SAM_MOLONY = '63b49d17f112a99b41f9e97b',
  SAM_OBRIEN = '63b49d17f112a9129af9e423',
  SAMIR_SAMPAT = '63b49d17f112a9c899f9e422',
  SARA_GEPP = '63b49d17f112a99227f9e3f1',
  SARAH_JOHNSON = '63b49d17f112a95825f9e442',
  SARAH_MILLS = '63b49d17f112a9ed9df9e414',
  SARAH_SCHAAF = '63b49d17f112a9e113f9e443',
  SEAN_LUND_BROWN = '63b49d17f112a94aa8f9e444',
  SEVERINE_HIERSO = '63b49d17f112a9413ef9ee2e',
  SHUVRODIP_GHOSH = '63b49d17f112a99be4f9e42d',
  STEFAN_SMULDERS = '63b49d17f112a99fbcf9ef48',
  STEPHANIE_EVERETT = '63b49d17f112a9b327f9e410',
  STEVE_RYAN = '63b49d17f112a99283f9e430',
  SU_KAYGUN_SAYRAN = '63b49d17f112a9ba8ff9e458',
  SUZANNE_HEADON = '63b49d17f112a94b40f9e7af',
  TALITHA_GRAY_KOZLOWSKI_AND_KRISTIN_TYLER = '63b49d17f112a9e269f9e3f2',
  TAMMY_WOOD = '63b49d17f112a9881af9e4bf',
  TANNER_SCOTT = '63b49d17f112a930e1f9eede',
  TOM_ARMITAGE = '63ed4d3b70c8013564a48ed9',
  TOM_LAMBOTTE = '63b49d17f112a9a06ef9e420',
  TRAVIS_CORRIGAN = '64cd5865bbc05fd937a2c991',
  TYLER_ROBERTS = '63b49d17f112a97007f9e3f9',
  VICTOR_BLASCO = '63b49d17f112a945e7f9e448',
  VICTORIO_DURAN_III = '63b49d17f112a9525af9e445',
  VIKAS_KALWANI = '63b49d17f112a9dd92f9ee46',
  WES_LUNGWITZ = '63b49d17f112a9c1ecf9e457',
  XENA_MENSINK = '63b49d17f112a97cadf9e40c',
  YAUHEN_ZAREMBA = '63b49d17f112a9f011f9e292',
  ZACK_DUNCAN = '63b49d17f112a953c3f9e411',
}

// Webflow image format
export interface WebflowImage {
  fileId: string;
  url: string;
}

// Input schema for blog content
export const BlogInputSchema = z.object({
  title: z.string(),
  content: z.string(),
  keywords: z.string().nullish(),
  slug: z.string().nullish(),
  category: z.union([z.string(), z.array(z.string())]).nullish(),
  tags: z.union([z.string(), z.array(z.string())]).nullish(),
  author: z.string().nullish(),
  metaTitle: z.string().nullish(),
  coverImage: z.string().nullish(), // Input as URL string, will be converted to Webflow format
  metaDescription: z.string().nullish(),
});

export type BlogInput = z.infer<typeof BlogInputSchema>;

export const WorkflowInputSchema = z.object({
  collectionType: z.nativeEnum(CollectionType),
  itemToPublish: BlogInputSchema,
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export type WorkflowOutput = PublishingResult;

// Webflow field mappings based on the actual collection fields
const blogFieldMapping = {
  // Single field mappings
  title: ['name', 'seo-title'],
  slug: ['slug'],
  content: ['post-body'],
  author: ['author-reference-ced-5-15'],
  category: ['blog-post-categories'],
  tags: ['tags'],
  keywords: ['keywords'],
  // Multiple field mappings - these input fields populate multiple Webflow fields
  coverImage: ['main-image-700px-2', 'seo-image'],
  publicationDate: ['publication-date'],
  metaDescription: ['seo-description', 'post-summary'],
} as const;

// Collection configurations
export const collectionConfig = {
  [CollectionType.BLOG_POSTS]: {
    siteId: SMITH_SITE_ID,
    collectionId: '63b49d17f112a9695ef9dc59',
    fieldMapping: blogFieldMapping,
  },
} as const;

export interface PublishingResult {
  success: boolean;
  itemId?: string;
  error?: string;
  originalIndex: number;
  webflowUrl?: string;
}
