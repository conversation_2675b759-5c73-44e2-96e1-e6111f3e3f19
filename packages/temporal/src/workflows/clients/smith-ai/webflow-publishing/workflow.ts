import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { handleWorkflowError, validateWorkflowInput } from '@flow/sdk/lib/workflow-utils.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { WorkflowInputSchema } from './types.js';

const { convertMarkdownToHtml, mapFieldsToWebflow, publishContent } = WorkflowScope.use<
  typeof activities
>(import.meta.url);

export async function clientsSmithAiWebflowPublishingWorkflow(
  rawInput: WorkflowInput
): Promise<WorkflowOutput> {
  // Validate input
  const input = validateWorkflowInput(rawInput, WorkflowInputSchema);

  try {
    // Convert markdown content to HTML
    const itemWithHtml = await convertMarkdownToHtml(input.itemToPublish);

    // Map fields to Webflow format
    const webflowItem = await mapFieldsToWebflow(itemWithHtml, input.collectionType);

    // Publish content
    const publishingResult = await publishContent(webflowItem, input.collectionType);

    // Return the result
    return publishingResult;
  } catch (error) {
    handleWorkflowError(error);
  }
}
