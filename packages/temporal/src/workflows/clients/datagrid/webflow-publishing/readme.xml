<!-- Generated on 2025-07-07T19:24:26.646Z -->
<workflow>
  <functionName>clientsDatagridWebflowPublishingWorkflow</functionName>
  <displayName>Datagrid Webflow Publishing</displayName>
  <path>clients/datagrid/webflow-publishing</path>
  <overview>Automates the process of publishing content to Webflow by converting markdown content to HTML, mapping fields to Webflow's format, and handling the publishing process through the Webflow API</overview>
  <flowChart><![CDATA[```mermaid
graph TD
  A[Start] --> B[Validate Input]
  B --> C[Convert Markdown to HTML]
  C --> D[Map Fields to Webflow Format]
  D --> E[Publish Content to Webflow]
  E --> F[Return Publishing Result]
  F --> G[End]
```]]></flowChart>
  <activities>
    <activity>
      <functionName>convertMarkdownToHtml</functionName>
      <displayName>Convert Markdown to HTML</displayName>
      <description>Converts markdown formatted content to HTML using Showdown converter with GitHub Flavored Markdown settings. Preserves content structure while making it compatible with Webflow's rich text format.</description>
    </activity>
    <activity>
      <functionName>mapFieldsToWebflow</functionName>
      <displayName>Map Fields to Webflow Format</displayName>
      <description>Transforms input fields to match Webflow's content structure. Handles special field types like images, enums for categories/topics/industries, and generates slugs. Includes field cleaning and validation.</description>
    </activity>
    <activity>
      <functionName>publishContent</functionName>
      <displayName>Publish Content to Webflow</displayName>
      <description>Creates new collection items in Webflow using their API. Handles field data formatting, sets default values, and manages publishing status. Returns success/failure status with relevant URLs or error messages.</description>
    </activity>
  </activities>
</workflow>