import { z } from 'zod';

const DATAGRID_SITE_ID = '65e1c00e1996606efc48b209';

// Utility function for text to enum conversion
export function normalTextToEnumKey(text: string): string {
  return text
    .trim()
    .toUpperCase()
    .replace(/[^A-Z0-9]+/g, '_')
    .replace(/^_+|_+$/g, '')
    .replace(/_+/g, '_');
}

// Predefined collection types
export enum CollectionType {
  BLOGS = 'blogs',
}

// Blog use cases mapping
export enum BlogUseCases {
  RFI_ROUTING_INTELLIGENCE_INDUSTRY = '681db7ea3875d4290a48e04c',
  THREE_D_MODEL_COORDINATION = '68012f2ac138211351782d14',
  ACC_ANALYTICS = '681db26f04aad3090d643fb8',
  AI_ADOPTION_TRENDS = '67eedd8382ac7f898be4cc55',
  AI_EDUCATION = '67f44897e336bcfeff0d8012',
  AI_SALES_ENHANCEMENT = '67f76bf77e65d815e7c86201',
  AI_DRIVEN_SALES_AUTOMATION = '67f0287c14448fb7ca5474e2',
  AS_BUILT_DRAWING_VERIFICATION = '680a5bdbed71e84e2c2d84cb',
  BIM_INTEGRATION = '67f58c8a79f58e76194c50fd',
  BID_ANALYSIS = '67f58f9e47bfd24585911351',
  BID_MANAGEMENT = '67f5902cdcd76da22f15d737',
  BID_PACKAGE_ASSEMBLY = '680a573a840159193462b832',
  BID_STRATEGY = '67f232b03fcfae53e5b17822',
  BLUEPRINT_ANALYSIS = '67f232e090c68aa2f6d4de8d',
  BOTTLENECK_IDENTIFICATION = '680f94eeba1123a11e29971f',
  BRAND_MANAGEMENT = '67f2286e2c96e4c56f526518',
  BUDGET_TRACKING = '67f586c5b7a6443b475549d9',
  BUILDING_CODE_EXTRACTION = '680a63922717551509642d43',
  CRM_DATA_ENTRY = '68095dedf738ca8be73af6c3',
  CRM_INTEGRATION = '67ed68c088bc96c89b1259d5',
  CALENDAR_MANAGEMENT = '67f4099dd647053b5d8f5656',
  CAMPAIGN_PERFORMANCE_ANALYSIS = '67ef666e5a680f46db8522ae',
  CAMPAIGN_PLANNING = '67f44438c16f256bc2606d91',
  CAMPAIGN_REPORT_STRUCTURING = '67ef662c39945c6e882066d3',
  CAMPAIGN_REPORTING = '67ef639945a5eeef7ee980c1',
  CASE_TIMELINE_CREATION = '681473257754b1e4a707bede',
  CHANGE_ORDER_MANAGEMENT = '6802ab99862da8fd4c43f9b2',
  CHEMICAL_COMPLIANCE_MONITORING = '681def209ec8874558e667eb',
  CLAIMS_PROCESSING = '67f75fc9dd82969e05171c5c',
  CLIENT_MANAGEMENT = '67f234b0394c00c0b19f43c4',
  CLIENT_MEETING_PREPARATION = '681b549541fbfce21b7fd627',
  CLIENT_PORTFOLIO_MANAGEMENT = '681b6c9e10fedfbe89c2c09e',
  CLIENT_RISK_ASSESSMENT = '68095d789b8de5fb29b1b869',
  CLINICAL_HEALTHCARE_AUTOMATION = '67fd52fa3f1b879205dec483',
  COMMERCIAL_REAL_ESTATE = '680934791d4018a0cadff4dc',
  COMPANY_OPERATIONS = '67e265bcd0a25c5193afcd39',
  COMPETITIVE_ANALYSIS = '67f758ecaf1798377511e895',
  COMPETITIVE_MARKET_ANALYSIS = '681da4be62c623c5b67e69df',
  COMPLIANCE = '67f229c9769bb7e82bf8e421',
  COMPLIANCE_MANAGEMENT = '67f765a9dd82969e051c1d3d',
  COMPLIANCE_RECORD_KEEPING = '681df57c3109101c882a762b',
  COMPLIANCE_TRACKING = '68012de6ec6880aba24f5ca2',
  CONFINED_SPACE_DOCUMENTATION = '681477961b2a08d15bca9db3',
  CONSTRUCTIBILITY_ISSUE_RESOLUTION = '681dbc685d7431af45076642',
  CONSTRUCTION_MANAGEMENT = '67e2657f73b62a64c1475eb5',
  CONTACT_INFORMATION_VERIFICATION = '68095e19ede060cbd7c8cbec',
  CONTACT_MANAGEMENT = '67f7673529be37044a38daab',
  CONTENT_ANALYTICS_REPORTING = '680a55d5fee349ae288cd837',
  CONTENT_AUTOMATION = '67fd4e7ad167e687247677b5',
  CONTENT_BRIEF_CREATION = '681137f2480be9627fcf30e5',
  CONTENT_CREATION = '67f7552efa47b33e78ee8229',
  CONTENT_MANAGEMENT = '67f75a085636542b5591252a',
  CONTENT_PERSONALIZATION = '67f751b56c0baa12649f8e75',
  CONTENT_QUALITY_AUDITS = '681df6cfa607ddca059f82a0',
  CONTENT_REPURPOSING = '680a5f0d33161962176baa68',
  CONTRACT_MANAGEMENT = '67f2382e2f435065941f425b',
  CONVERSION_FUNNEL_ANALYSIS = '680f98d80e98390d10e450ac',
  COST_ESTIMATION = '67f230fbaf6e43269db58b50',
  COVERAGE_GAP_ANALYSIS = '680fe13e814045ba58e808c5',
  CREW_COMPOSITION_OPTIMIZATION = '681daf1fc8957707d842fcf4',
  CUSTOMER_PROFILING = '67f40871a54fca06feaa5368',
  DAMAGE_ASSESSMENT = '67f23406bca55282385249cc',
  DATA_ANALYSIS = '67ef62dd656e6c30d0048c11',
  DATA_EXTRACTION = '67eaf9082fcb4a15fe0880d7',
  DATA_INTEGRATION = '67ed69af3e827883bba54419',
  DATA_MANAGEMENT = '67f22d53ac971640cd994734',
  DATA_MIGRATION = '67ed641be5f99397e5642e49',
  DATA_PROCESSING = '67f7693967d694305da42b9a',
  DATA_VALIDATION = '67eafe7c23f39df4bac2d41f',
  DEAL_DOCUMENTATION = '681621e79dae68c9fe298ba8',
  DEPOSITION_SUMMARY_CREATION = '680a58b8151eaf8cd701b2ec',
  DESIGN_MANAGEMENT = '67f445a45c0117f58b685a70',
  DOCUMENT_CLASSIFICATION = '67f6da9bb369f414b2197f72',
  DOCUMENT_CONVERSION = '67eafa6de50f03199b4bffd0',
  DOCUMENT_EDITING = '67f76b107e65d815e7c7c01e',
  DOCUMENT_MANAGEMENT = '67eaffc11890f29d47ceb809',
  DOCUMENT_PROCESSING = '67eb00f0c44a85973ac6a721',
  DOCUMENT_REVIEW = '67ef66c87b2bf746b24e2f6e',
  DOCUMENT_VERIFICATION = '67f583f0aafe673ec47d9fea',
  E_COMMERCE = '6825a40fc61bb1daef05dee5',
  ERP_DATA_SYNCHRONIZATION = '680fe2ad3a43d957676c1814',
  EMAIL_AUTOMATION = '67ed6f8696c297a7bbab2157',
  EMAIL_MANAGEMENT = '67f589afc99244bdb578e79d',
  ENERGY_SYSTEMS_MONITORING = '67f2354080e9ecbd46bb4a58',
  EQUIPMENT_MANAGEMENT = '67f22f4d65ba73798101b232',
  EVIDENCE_CATALOGING = '68147254b72ce2edd01f2f6f',
  FALL_PROTECTION_INSPECTION = '681df0273a4740c9eb10c6b6',
  FATIGUE_DETECTION = '681478cfffb54e35751b4de9',
  FINANCE_AND_COMPLIANCE = '68264465b660782098b3caa6',
  FINANCE_AND_LEGAL = '67e265eae3e3ea99cfc4c4eb',
  FINANCIAL_MANAGEMENT = '67f58aef5c70b98da6150be9',
  FINANCIAL_PLAN_DOCUMENTATION = '68095dbeaad639b68de6067a',
  FINANCIAL_PLANNING = '67f58d78d72d70cd472704a5',
  FINANCIAL_PROCESS_AUTOMATION = '67fd548ac83669bac88e2061',
  FINANCIAL_REPORTING = '67f234f46c3199a9e8d2827c',
  FINANCIAL_STABILITY_ASSESSMENT = '6801307b811e8b41a1ba8848',
  HISTORICAL_DATA_INTEGRATION = '681deda913b18496e0104995',
  INCIDENT_REPORT_ANALYSIS = '68095e46d2e4cd55d8b565fe',
  INFORMATION_MANAGEMENT = '67f23390a9dbf77f0f2a26da',
  INSURANCE_CERTIFICATE_TRACKING = '681deea72f8f1d166161d80b',
  INVENTORY_MANAGEMENT = '67f22e0af71b33ff4c9905de',
  INVESTMENT_PROPERTY_SCREENING = '6814716a0347a3ec7b95de24',
  IOT_INTEGRATION = '67f44be5bd7a8a92c949adf6',
  KEYWORD_INTEGRATION = '68113d89ad0b7833ca62580c',
  KNOWLEDGE_MANAGEMENT = '67f2335ce64cd5a5f9ac16a9',
  LANDING_PAGE_MONITORING = '681476cdeaa128fb454700f1',
  LEAD_ENRICHMENT = '67f767f5aa11ce4fc578c9c5',
  LEAD_GENERATION = '67ed674e79f9c4ca5ea7b0a1',
  LEAD_MANAGEMENT = '67f58cc5c99244bdb57b4b14',
  LEAD_SCORING = '67f751f1193b5ceff9221def',
  LEASE_ABSTRACT_CREATION = '681da69c229fa2318715b6a1',
  LEGAL_AUTOMATION = '680125fd8a58f914f31acac2',
  LEGAL_BRIEF_PREPARATION = '680a5ac8c53ad640e997f42e',
  LEGAL_COMPLIANCE = '682595a4c7a9707e07ee28b3',
  MANUFACTURING_AUTOMATION = '680129a5f0fac7149550f277',
  MARKET_RESEARCH = '67f7531bb6128e5e8289ebbb',
  MARKETING = '67e2653f4300766f3a640dbf',
  MARKETING_AND_SALES = '68264667ad66ffe82f3947ba',
  MARKETING_WORKFLOW_AUTOMATION = '67ef5c972be47c8420183e62',
  MARKUP_CONSOLIDATION = '680131c2a6c1ecb238aa9388',
  MATERIAL_TEST_REPORT_VALIDATION = '681623ba623192bfdb51d8a7',
  MEETING_MANAGEMENT = '67f44b8cbd7a8a92c9495134',
  MULTI_PHASE_PROPOSAL_CREATION = '6814752b4e4ca8296271c288',
  MULTI_SITE_PERFORMANCE_COMPARISON = '680a68e3c0a0d66edb44999e',
  NEWSLETTER_CREATION = '681b57da343a57ea5c8e50d8',
  NOISE_LEVEL_MONITORING_ANALYSIS = '680a67d01a8f1f155f22e293',
  OCR_PROCESSING = '67f76b5791f36c865d7e8904',
  OBJECTION_RESEARCH = '681db9ea9ec8874558c41f4f',
  ORDER_MANAGEMENT = '67f446482b7e509a2dfb3ad4',
  PATENT_PRIOR_ART_SEARCH = '681b6de2cdf583721504e65d',
  PATTERN_ANALYSIS = '67f22a476c3199a9e8cd12a4',
  PERFORMANCE_MANAGEMENT = '67f2293ebca55282384c89e2',
  PERFORMANCE_METRIC_TRACKING = '681b58df8aff395ca0b770ed',
  PERMIT_MANAGEMENT = '67f592a84a89dff09503dcbd',
  PERMIT_VERIFICATION = '6802a9d2b0990dcdbf75763d',
  POLICY_MANAGEMENT = '67f22c7819e6c7de0a1071e1',
  PRESENTATION_MANAGEMENT = '67f4431e91c07a59c8dae75d',
  PRODUCT_ATTRIBUTE_STANDARDIZATION = '68205916172a418ac8d94e0b',
  PRODUCT_DESCRIPTION_GENERATION = '680fe41c149d60a28c4f59c4',
  PRODUCT_DEVELOPMENT = '67e26557ec940d63a254edf1',
  PRODUCTIVITY_ANALYSIS = '68013348aaf2227536ffb84f',
  PROJECT_COMMUNICATION = '67f2300a2bf82cdb0ad7581d',
  PROJECT_MANAGEMENT = '67f58dbe17b6dafba7334953',
  PROJECT_PLANNING = '67f40c83fb5af95d8055e831',
  PROJECT_SCHEDULING = '67f58d08b6074b4a011c60b3',
  PROPERTY_FINANCIAL_MODELING = '68095d0d9b8de5fb29b15041',
  PROPOSAL_AUTOMATION = '67f40b0183c513e4528d15d9',
  PROPOSAL_MANAGEMENT = '67f591ef7a4488a468a3a6fa',
  PROTOCOL_ADHERENCE_MONITORING = '68113ea179e8c499789816fc',
  PUNCH_LIST_PRIORITIZATION = '680fe00c2cc26e9882fc6942',
  QUALITY_ASSURANCE = '67f22f895bbe52d7a086388b',
  QUOTE_MANAGEMENT = '67f58332ef5d2da2d0e77896',
  RFI_MANAGEMENT = '67f22db33b071e2e8599804f',
  RFQ_EVALUATION = '68064cff05dece72c587849d',
  REAL_ESTATE_AUTOMATION = '68012458c8a060c77d98a083',
  RENEWAL_DOCUMENTATION = '680f9a5518707a9741c849f6',
  REPORTING_AUTOMATION = '67f231e43f5e55db67545082',
  RESEARCH_AUTOMATION = '67f7536a694284ac8bf01af8',
  RESOURCE_ALLOCATION = '67f40f652d69ab059287c3a6',
  RESOURCE_MANAGEMENT = '67f58f072652b9c9dc9198a0',
  RESOURCE_UTILIZATION_ANALYSIS = '681b5a142c563cf3e02f2e38',
  RESPONSE_PRIORITIZATION = '68113c25e5e05f0b470b0c80',
  RETIREMENT_SCENARIO_MODELING = '68146fc0f31081659bc081da',
  RISK_ASSESSMENT = '682ae9362c04847a97619637',
  RISK_MANAGEMENT = '67f22e46973e5fa8ac9ce504',
  SEO_CONTENT_OPTIMIZATION = '681b55c1e2d823952294157f',
  SEO_CONTENT_OPTIMIZATION_2 = '681b56165523819ab4a0668b',
  SAFETY_COMPLIANCE = '67f44c24467c6f57c1dc29e6',
  SALES_AND_CRM = '67e265d90c0581e637fed464',
  SALES_AUTOMATION = '67fd5137dca990dace47665f',
  SALES_ENABLEMENT = '67f447289b1c340227721494',
  SALES_FORECASTING = '67f02a072b3e5fd414dd24f8',
  SALES_INTELLIGENCE = '67f449b28344786ab3414736',
  SALES_PIPELINE_MANAGEMENT = '67f752ae6c0baa1264a02cb4',
  SETTLEMENT_ANALYSIS = '681daa5d2e79f327ba85320b',
  SITE_MONITORING = '67f44c94f25485bb272e69f6',
  SOCIAL_MEDIA_MONITORING = '67f7678631d56d203aacc4f6',
  SOFTWARE_DEVELOPMENT = '67e2656e6a9f8d796d01ed75',
  SOFTWARE_INTEGRATION = '67f58c04b7a6443b4759aab5',
  SPECIFICATION_VERIFICATION = '68064e79d222786462bc58ba',
  STAFF_SCHEDULING = '68095ccdd765aecd0e543b07',
  STAKEHOLDER_COMMUNICATION = '680f9270bad937e6cd13de37',
  STRATEGIC_SOURCING_EVALUATION = '681df0e6440d33e71ae32996',
  STRUCTURAL_ASSESSMENT = '67f2343fbba583171cbb60a7',
  SUBCONTRACTOR_QUALIFICATION_VERIFICATION = '680a60903c59fb5e4a8fbc5d',
  SUBMITTAL_REVIEW_PRIORITIZATION = '6816228bad8a1a6ce1fe3731',
  SUPPLIER_RISK_ASSESSMENT = '681620b66bddfb2447c2a0db',
  SUPPLY_CHAIN_MANAGEMENT = '67e265fb1be49e8b01355735',
  SYSTEM_INTEGRATION = '67f2367b973e5fa8aca153ba',
  TASK_ASSIGNMENT_OPTIMIZATION = '680a61e1f1a26ff908279fc5',
  TASK_MANAGEMENT = '67f22cfb62aa4c4c6862776d',
  TAX_PLANNING_DOCUMENTATION = '6814740c0b6c5dad8f334597',
  TEAM_COMMUNICATION = '67f58a7cb7a6443b47585b65',
  TENANT_PROSPECTING = '68095c5ee606346444868b1a',
  TOOLBOX_TALK_PERSONALIZATION = '680a654bdb3424390bd785ec',
  USER_BEHAVIOR_RECOGNITION = '680f93202a2cec3597b8c890',
  VALUE_ENGINEERING_IDENTIFICATION = '681136c9761e5ed06c4900a5',
  VENDOR_CONTRACT_ANALYSIS = '681df7d49412fb5520245e9a',
  VENDOR_MANAGEMENT = '67f58e9965f29be6ec4cc2b7',
  WARRANTY_DOCUMENT_ORGANIZATION = '681dec819ec8874558e4f0ce',
  WEATHER_FORECASTING = '6802aab9fcc2dd1ff1379603',
  WORKFLOW_AUTOMATION = '67ef604e2be47c84201af7b2',
  WORKFLOW_OPTIMIZATION = '67f22ece5bbe52d7a085ea0e',
  ECOMMERCE_OPTIMIZATION = '6801284fc0c9516d675439e7',
}

export enum BlogCategories {
  AI_FOUNDATION = '680cd61bdfac05f767f581e2',
  AGENTIC_AI_CONCEPTS = '680cd623e09130504473e8e8',
  DATA_PROCESSING_AND_ANALYSIS = '680cd63c113d0ea65d33d056',
  DOCUMENT_PROCESSING = '680cd62b4992177f78e916db',
  WORKFLOW_AUTOMATION = '680cd634113d0ea65d33cd83',
}

export enum BlogTopics {
  AI_AGENTS_FOR_FINANCE = '67fd546640c321cc5255169e',
  AI_AGENTS_FOR_HEALTHCARE = '67fd52e1f15a6c5d3728e20b',
  AI_AGENTS_FOR_INSURANCE = '68012d00a6c1ecb238a70d7c',
  AI_AGENTS_FOR_LAW = '680125e48a9c20feae165ea3',
  AI_AGENTS_FOR_MANUFACTURING = '68012989bb289ef92a54d3ad',
  AI_AGENTS_FOR_MARKETING = '67fd4e696e8e169cf4452e71',
  AI_AGENTS_FOR_REAL_ESTATE = '6801241e87732639738c097a',
  AI_AGENTS_FOR_SALES = '67fd5121e5d40eb336cc13d2',
  AI_AGENTS_FOR_E_COMMERCE = '6801282aef444dd7f03e843b',
  AI_FOUNDATIONS = '6640d02103a77bdd4a546845',
  AI_AGENTS = '67ed65122e56954e795c9de1',
  AI_AGENTS_FOR_CONSTRUCTION_STATS = '67f0276447eaf8253945839a',
  AI_AGENTS_FOR_INSURANCE_STATS = '67ef5b9ea1c56b50b4646663',
  AI_AGENTS_FOR_MARKETING_STATS = '67ef5c7edcf765bfcbf0f433',
  AI_AGENTS_FOR_SALES_STATS = '67f0285f14448fb7ca546462',
  AI_AGENTS_OVERALL_STATS = '67eedd56f35371c8c3382d77',
  AI_POWERED_AUTOMATION = '67f4499ba0791bd485d8edaa',
  AI_POWERED_DATA_PROCESSING = '67e267e88ca61cee3a15f5c1',
  AGENTIC_AI_CONCEPTS = '67e264f150f3e96d143e5f2e',
  CONSTRUCTION_AI_FOR_PROPOSAL_AND_VENDOR_MANAGEMENT = '67f58e2d79f9b1302234bec7',
  CONSTRUCTION_AI_DRIVEN_SOFTWARE_INTEGRATIONS = '67f58996db21fa9dc3206fb8',
  CONSTRUCTION_AI_POWERED_CONSTRUCTION_SAFETY_AND_COMPLIANCE = '67f44bce134a6fd68d034f09',
  CONSTRUCTION_AI_POWERED_DOCUMENT_AUTOMATION_AND_COMPLIANCE = '67f58fccd4facb9fe6dd73a8',
  CONSTRUCTION_AI_POWERED_PROJECT_AND_WORKFLOW_AUTOMATION = '67f22d9daa52ad4ca8d3cdb6',
  DOCUMENT_DATA_EXTRACTION_AND_HANDLING = '67eaf92064c8a3d1fbb993bc',
  INSURANCE_DOCUMENT_HANDLING = '67f22c63e64cd5a5f9a88b37',
  MARKETING_DOCUMENT_HANDLING = '67ef637f4194d31f89b5bbed',
  PDF_EXTRACTION = '67f76afdb93f9783c8eb7df8',
  PRODUCTIVITY_TOOLS_AND_TECHNIQUES = '67ef5e5185a23448b48493c3',
  SALES_TASKS = '67f76bb510253942244fc820',
  SALES_DOCUMENT_HANDLING = '67f2381889e5f475fcddf526',
  SOFTWARE_INTEGRATIONS = '67ed66799b9c551c6146add4',
  TUTORIALS = '6640d00fe3dfb0b8eb2d0259',
  UPDATES = '6640d00b684df22be7f49dcc',
}

export enum BlogIndustries {
  CRO = '680f93321635fe46e4a4bf2d',
  CONSTRUCTION = '67f22dc52c96e4c56f557d1c',
  CONSTRUCTION_AND_ENGINEERING = '67e26645e3e3ea99cfc51002',
  CUSTOMER_SUCCESS = '67e266a35e3a1782a812655b',
  ENVIRONMENTAL_COMPLIANCE = '680a67e600e9bf5074648523',
  FINANCE = '67eafe9d00af00cefca8c7b8',
  FINANCIAL_SERVICES = '67e266c88ca61cee3a14ed96',
  GENERAL_BUSINESS = '67eedd9ed25993753df765d4',
  HEALTHCARE = '67fd530cac594e546d6bb981',
  INSURANCE = '67e266b965f7e1f418fbaf4d',
  LEAD_GENERATION = '67ed655fe458c84b5078b280',
  LEGAL = '6801260c87732639738d7851',
  MANUFACTURING = '680129c5bd045dfffd4ddcf0',
  MARKETING = '67ef5cae78becf362763f680',
  MARKETING_AND_CONTENT = '67e2668787c9b5e965ca5704',
  OPERATIONS = '67f6d5e0b369f414b2153cb1',
  PERSONAL = '67e266f8ec940d63a25658d7',
  PROFESSIONAL_SERVICES = '67e3cf138d9f87bdbb30281e',
  PROJECT_MANAGEMENT = '67f40f753357279f8e31ac8e',
  REAL_ESTATE = '67e26652262b8ad37904020c',
  RISK_MANAGEMENT = '67e266e18b010c9fb4752a7b',
  SALES = '67e2669396ce25144013a32a',
  SOFTWARE_DEVELOPMENT = '67e26676922308b632ad7c74',
  TECHNOLOGY = '67e3cf3e7ea9a2598bfb184f',
  E_COMMERCE = '6801286126580fd839bf3e61',
}

// Webflow image format
export interface WebflowImage {
  fileId: string;
  url: string;
}

// Input schema for blog content
export const ProgrammaticInputSchema = z.object({
  title: z.string(),
  content: z.string(),
  slug: z.string(),
  category: z.string().nullish(),
  coverImage: z.string().nullish(),
  metaDescription: z.string().nullish(),
  blogUseCase: z.string().nullish(),
  blogTopic: z.string().nullish(),
  blogIndustry: z.string().nullish(),
});

export type ProgrammaticInput = z.infer<typeof ProgrammaticInputSchema>;

export const WorkflowInputSchema = z.object({
  collectionType: z.nativeEnum(CollectionType),
  itemToPublish: ProgrammaticInputSchema,
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export type WorkflowOutput = PublishingResult;

// Webflow field mappings based on the provided mapping table
const blogFieldMapping = {
  // Single field mappings
  title: ['name', 'meta-title'],
  slug: ['slug'],
  blogUseCase: ['blog-use-case'],
  blogTopic: ['blog-categories'],
  blogIndustry: ['blog-industry'],
  category: ['main-category'],
  content: ['post-body'],
  // Multiple field mappings - these input fields populate multiple Webflow fields
  coverImage: ['main-image', 'thumbnail-image'],
  metaDescription: ['meta-description', 'intro-text'],
} as const;

// Collection configurations
export const collectionConfig = {
  [CollectionType.BLOGS]: {
    siteId: DATAGRID_SITE_ID,
    collectionId: '65e1c00e1996606efc48b229',
    fieldMapping: blogFieldMapping,
  },
} as const;

export interface PublishingResult {
  success: boolean;
  itemId?: string;
  error?: string;
  originalIndex: number;
  webflowUrl?: string;
}
