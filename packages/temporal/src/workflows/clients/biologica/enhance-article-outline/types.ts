import { z } from 'zod';

export const WorkflowInputSchema = z.object({
  outline: z.string(),
  context: z.string(),
  reproductiveHealthDossier: z.string(),
  postmenopauseDossier: z.string(),
  perimenopauseDossier: z.string(),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export const EnhancedOutlineSchema = z.object({
  storytellingOpportunities: z.array(z.string()),
  outline: z.string(),
});

export type EnhancedOutline = z.infer<typeof EnhancedOutlineSchema>;

export type WorkflowOutput = {
  outline: string;
  storytellingOpportunities: string[];
};
