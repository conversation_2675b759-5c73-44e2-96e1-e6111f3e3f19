import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { enhanceOutlinePrompt } from './prompts.js';
import type { EnhancedOutline } from './types.js';
import { EnhancedOutlineSchema } from './types.js';

const { anthropicChatService } = workerModule;

export async function enhanceArticleOutline(
  outline: string,
  context: string,
  dossier: string
): Promise<EnhancedOutline> {
  const prompt = await threadTemplate(
    enhanceOutlinePrompt,
    { outline, context, dossier },
    { format: 'anthropic', strict: true }
  );

  const response = await anthropicChatService.request<EnhancedOutline>({
    messages: prompt.messages,
    system: prompt.system,
    thinking: { type: 'enabled', budget_tokens: 1500 },
    model: 'claude-opus-4-20250514',
    max_tokens: 32000,
    schema: EnhancedOutlineSchema,
  });

  return (typeof response === 'string' ? JSON.parse(response) : response) as EnhancedOutline;
}

export default WorkflowScope.register(import.meta.url, {
  enhanceArticleOutline,
});
