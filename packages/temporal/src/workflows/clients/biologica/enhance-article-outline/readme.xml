<!-- Generated on 2025-07-09T18:40:30.232Z -->
<workflow>
  <functionName>clientsBiologicaEnhanceArticleOutlineWorkflow</functionName>
  <displayName>Enhance Article Outline</displayName>
  <path>clients/biologica/enhance-article-outline</path>
  <overview>A workflow that takes an article outline as input and enhances it using GPT-4 to provide more detailed content suggestions, key points, research topics, and improvements.</overview>
  <flowChart><![CDATA[```mermaid
graph TD
  A[Start] --> B[Validate Input]
  B --> C[Enhance Article Outline Activity]
  C --> D[Return Enhanced Outline]
  D --> E[End]
```]]></flowChart>
  <activities>
    <activity>
      <functionName>enhanceArticleOutline</functionName>
      <displayName>Enhance Article Outline</displayName>
      <description>This activity processes an article outline through GPT-4 using a specialized prompt template. It enhances the outline by:
1. Expanding sections with detailed content suggestions
2. Adding key points for coverage
3. Suggesting additional research topics
4. Identifying potential improvements
5. Recommending additional relevant topics

The activity uses OpenAI's chat service with GPT-4 and validates the output against a schema that enforces structure for sections, improvements, and additional topics. The enhancement maintains the original intent while making the outline more comprehensive.</description>
    </activity>
  </activities>
</workflow>