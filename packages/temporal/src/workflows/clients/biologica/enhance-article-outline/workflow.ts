import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { handleWorkflowError, validateWorkflowInput } from '@flow/sdk/lib/workflow-utils.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { WorkflowInputSchema } from './types.js';
import type * as sharedActivities from '../shared/activities.js';
import type { DossierCategory } from '../shared/types.js';

const { enhanceArticleOutline } = WorkflowScope.use<typeof activities>(import.meta.url);

const { getDossierCategory } = WorkflowScope.use<typeof sharedActivities>(
  '/workflows/clients/biologica/shared/workflow.ts'
);

export async function clientsBiologicaEnhanceArticleOutlineWorkflow(
  rawInput: WorkflowInput
): Promise<WorkflowOutput> {
  const input = validateWorkflowInput(rawInput, WorkflowInputSchema);
  const dossiers = {
    reproductiveHealth: input.reproductiveHealthDossier,
    postmenopause: input.postmenopauseDossier,
    perimenopause: input.perimenopauseDossier,
  };

  try {
    const dossierCategory: DossierCategory = await getDossierCategory(input.outline);

    if (!dossiers[dossierCategory]) {
      throw new Error(`Dossier category ${dossierCategory} not found`);
    }

    return await enhanceArticleOutline(input.outline, input.context, dossiers[dossierCategory]);
  } catch (error) {
    handleWorkflowError(error);
  }
}
