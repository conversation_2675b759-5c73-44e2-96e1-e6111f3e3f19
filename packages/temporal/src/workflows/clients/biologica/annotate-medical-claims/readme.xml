<!-- Generated on 2025-07-13T17:46:14.245Z -->
<workflow>
  <functionName>clientsBiologicaAnnotateMedicalClaimsWorkflow</functionName>
  <displayName>Annotate Medical Claims</displayName>
  <path>clients/biologica/annotate-medical-claims</path>
  <overview>A workflow that processes article content to identify and annotate medical claims for peer review, excluding company-specific and safety-related claims. The workflow splits the article into sections and adds [MD] annotations to relevant medical claims.</overview>
  <flowChart><![CDATA[```mermaid
graph TD
  A[Start] --> B[Validate Input]
  B --> C[Split Article into Sections]
  C --> D[Annotate Medical Claims]
  D --> E[Join Sections]
  E --> F[Return Result]
  F --> G[End]
```]]></flowChart>
  <activities>
    <activity>
      <functionName>splitArticleIntoSections</functionName>
      <displayName>Split Article into Sections</displayName>
      <description>Divides the input article content into logical sections for processing. This is a shared activity imported from the workflows shared library.</description>
    </activity>
    <activity>
      <functionName>annotateMedicalClaims</functionName>
      <displayName>Annotate Medical Claims</displayName>
      <description>Uses Claude-sonnet-4 to analyze article sections and annotate medical claims with [MD] markers. The activity:
- Excludes claims related to Biologica company
- Excludes safety-related warnings and disclaimers
- Uses ultrathink to identify and validate medical claims
- Preserves original text while only adding [MD] annotations
- Processes claims through Anthropic chat service with a 3500 token thinking budget</description>
    </activity>
  </activities>
</workflow>