import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { splitArticleIntoSections } from '@flow/workflows/shared/activities.js';
import { annotationPrompt } from './prompts.js';

const { anthropicChatService } = workerModule;

export { splitArticleIntoSections };

export async function annotateMedicalClaims(section: string): Promise<string> {
  const prompt = await threadTemplate(
    annotationPrompt,
    {
      section,
    },
    { format: 'anthropic', strict: true }
  );

  const result = await anthropicChatService.request<string>({
    messages: prompt.messages,
    system: prompt.system,
    thinking: { type: 'enabled', budget_tokens: 3500 },
    model: 'claude-sonnet-4-20250514',
  });

  return result;
}

export default WorkflowScope.register(import.meta.url, {
  annotateMedicalClaims,
  splitArticleIntoSections,
});
