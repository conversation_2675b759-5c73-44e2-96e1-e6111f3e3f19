import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { handleWorkflowError, validateWorkflowInput } from '@flow/sdk/lib/workflow-utils.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { WorkflowInputSchema } from './types.js';

const { annotateMedicalClaims, splitArticleIntoSections } = WorkflowScope.use<typeof activities>(
  import.meta.url
);

export async function clientsBiologicaAnnotateMedicalClaimsWorkflow(
  rawInput: WorkflowInput
): Promise<WorkflowOutput> {
  const input = validateWorkflowInput(rawInput, WorkflowInputSchema);
  const sections = await splitArticleIntoSections({ article: input.articleContent });

  try {
    const annotatedSections = await Promise.all(
      sections.map((section) => annotateMedicalClaims(section.content))
    );

    return { articleContent: annotatedSections.join('\n\n') };
  } catch (error) {
    handleWorkflowError(error);
  }
}
