<!-- Generated on 2025-07-10T17:17:07.651Z -->
<workflow>
  <functionName>clientsBiologicaBiologicaMentionsReviewWorkflow</functionName>
  <displayName>Biologica Mentions Review</displayName>
  <path>clients/biologica/biologica-mentions-review</path>
  <overview>Reviews and improves mentions of the company "Biologica" within article text by analyzing sections, identifying relevant mentions, and suggesting improvements while maintaining consistency with the company category and original content style.</overview>
  <flowChart><![CDATA[```mermaid
graph TD
  A[Start] --> B[Validate Input]
  B --> C[Get Dossier Category]
  C --> D[Split Article into Sections]
  D --> E[Process Each Section]
  E --> F[Check for Biologica Mentions]
  F --> G{Contains Biologica?}
  G -->|Yes| H[Review and Update Mentions]
  G -->|No| I[Keep Original Section]
  H --> J[Combine Sections]
  I --> J
  J --> K[Return Updated Article]
  K --> L[End]
```]]></flowChart>
  <activities>
    <activity>
      <functionName>getDossierCategory</functionName>
      <displayName>Get Dossier Category</displayName>
      <description>Determines the category of the article dossier to provide context for mention reviews.</description>
    </activity>
    <activity>
      <functionName>splitArticleIntoSections</functionName>
      <displayName>Split Article into Sections</displayName>
      <description>Splits the input article into manageable sections by paragraphs for granular analysis.</description>
    </activity>
    <activity>
      <functionName>checkSectionForBiologica</functionName>
      <displayName>Check Section for Biologica Mentions</displayName>
      <description>Uses OpenAI GPT-4 to analyze a section of text and determine if it contains any mentions of Biologica, considering direct mentions and variations of the company name.</description>
    </activity>
    <activity>
      <functionName>reviewBiologicaMentions</functionName>
      <displayName>Review Biologica Mentions</displayName>
      <description>Uses OpenAI GPT-4 to review mentions of Biologica in identified sections, suggesting improvements based on accuracy, context, category consistency, and clarity while preserving the original style and tone.</description>
    </activity>
  </activities>
</workflow>