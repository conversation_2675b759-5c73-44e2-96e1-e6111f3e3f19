import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import type { ArticleSection } from '@flow/workflows/shared/type.js';
import { checkBiologicaPrompt, reviewMentionsPrompt, rewriteMentionPrompt } from './prompts.js';
import type { MentionReview } from './types.js';
import { CheckBiologicaSchema, MentionReviewSchema } from './types.js';

const { anthropicChatService } = workerModule;

export async function splitArticleIntoSections(input: {
  article: string;
}): Promise<ArticleSection[]> {
  // Split on ## headers, keeping the header with each section
  const sections = input.article.split(/(?=^## )/m).filter((section) => section.trim());

  let currentIndex = 0;

  return sections.map((sectionContent) => {
    const section = {
      content: sectionContent.trim(),
      startIndex: currentIndex,
      endIndex: currentIndex + sectionContent.length - 1,
    };
    currentIndex += sectionContent.length;

    return section;
  });
}

export async function checkSectionForBiologica(section: string): Promise<boolean> {
  const prompt = await threadTemplate(
    checkBiologicaPrompt,
    { articleSection: section },
    { format: 'anthropic', strict: true }
  );

  const response = await anthropicChatService.request<{ mentionsBiologica: boolean }>({
    messages: prompt.messages,
    system: prompt.system,
    thinking: { type: 'enabled', budget_tokens: 1500 },
    model: 'claude-sonnet-4-20250514',
    schema: CheckBiologicaSchema,
  });

  return typeof response === 'string'
    ? JSON.parse(response).mentionsBiologica
    : response.mentionsBiologica;
}

export async function reviewBiologicaMentions(input: {
  articleSection: string;
  dossier: string;
  context: string;
}): Promise<MentionReview> {
  const prompt = await threadTemplate(
    reviewMentionsPrompt,
    {
      articleSection: input.articleSection,
      context: input.context,
      dossier: input.dossier,
    },
    { format: 'anthropic', strict: true }
  );

  const response = await anthropicChatService.request<MentionReview>({
    messages: prompt.messages,
    system: prompt.system,
    thinking: { type: 'enabled', budget_tokens: 5000 },
    model: 'claude-opus-4-20250514',
    max_tokens: 32000,
    schema: MentionReviewSchema,
  });

  return typeof response === 'string' ? JSON.parse(response) : response;
}

export async function rewriteBiologicaMention(input: {
  mentionReview: MentionReview;
  writingGuidelines: string;
}): Promise<MentionReview> {
  const prompt = await threadTemplate(
    rewriteMentionPrompt,
    {
      articleSection: input.mentionReview.content,
      originalMention: input.mentionReview.originalMention,
      suggestedMention: input.mentionReview.suggestedMention,
      writingGuidelines: input.writingGuidelines,
    },
    { format: 'anthropic', strict: true }
  );

  const response = await anthropicChatService.request<MentionReview>({
    messages: prompt.messages,
    system: prompt.system,
    thinking: { type: 'enabled', budget_tokens: 5000 },
    model: 'claude-opus-4-20250514',
    max_tokens: 32000,
    schema: MentionReviewSchema,
  });

  return typeof response === 'string' ? JSON.parse(response) : response;
}

export default WorkflowScope.register(import.meta.url, {
  splitArticleIntoSections,
  checkSectionForBiologica,
  reviewBiologicaMentions,
  rewriteBiologicaMention,
});
