import { z } from 'zod';

export const WorkflowInputSchema = z.object({
  article: z.string(),
  context: z.string(),
  outline: z.string(),
  reproductiveHealthDossier: z.string(),
  postmenopauseDossier: z.string(),
  perimenopauseDossier: z.string(),
  writingGuidelines: z.string(),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export type WorkflowOutput = {
  article: string;
};

export const CheckBiologicaSchema = z.object({
  mentionsBiologica: z.boolean(),
});

export const MentionReviewSchema = z.object({
  content: z.string(),
  originalMention: z.string(),
  reasoning: z.string(),
  suggestedMention: z.string(),
});

export type MentionReview = z.infer<typeof MentionReviewSchema>;
