import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { handleWorkflowError, validateWorkflowInput } from '@flow/sdk/lib/workflow-utils.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { WorkflowInputSchema } from './types.js';
import type * as sharedActivities from '../shared/activities.js';

const {
  reviewBiologicaMentions,
  checkSectionForBiologica,
  splitArticleIntoSections,
  rewriteBiologicaMention,
} = WorkflowScope.use<typeof activities>(import.meta.url);

const { getDossierCategory } = WorkflowScope.use<typeof sharedActivities>(
  '/workflows/clients/biologica/shared/workflow.ts'
);

export async function clientsBiologicaBiologicaMentionsReviewWorkflow(
  rawInput: WorkflowInput
): Promise<WorkflowOutput> {
  const input = validateWorkflowInput(rawInput, WorkflowInputSchema);

  const dossiers = {
    reproductiveHealth: input.reproductiveHealthDossier,
    postmenopause: input.postmenopauseDossier,
    perimenopause: input.perimenopauseDossier,
  };

  try {
    const dossierCategory = await getDossierCategory(input.outline);

    const sections = await splitArticleIntoSections({ article: input.article });

    // Process all sections in parallel
    const processedSections = await Promise.all(
      sections.map(async (section, index) => {
        const hasBiologica = await checkSectionForBiologica(section.content);

        if (hasBiologica) {
          const mentionReview = await reviewBiologicaMentions({
            articleSection: section.content,
            dossier: dossiers[dossierCategory as keyof typeof dossiers],
            context: input.context,
          });

          const rewrittenSection = await rewriteBiologicaMention({
            mentionReview,
            writingGuidelines: input.writingGuidelines,
          });

          return {
            content: rewrittenSection.content,
            originalIndex: index,
          };
        } else {
          return {
            content: section.content,
            originalIndex: index,
          };
        }
      })
    );

    const reassembledArticle = processedSections
      .sort((a, b) => a.originalIndex - b.originalIndex)
      .map((section) => section.content)
      .join('\n\n');

    return {
      article: reassembledArticle,
    };
  } catch (error) {
    handleWorkflowError(error);
  }
}
