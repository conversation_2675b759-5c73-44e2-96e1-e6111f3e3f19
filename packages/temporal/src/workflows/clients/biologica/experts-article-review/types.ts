import { z } from 'zod';

export const WorkflowInputSchema = z.object({
  content: z.string(),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export const RevisionSchema = z.object({
  revisedContent: z.string(),
});

export type Revision = z.infer<typeof RevisionSchema>;

export type WorkflowOutput = {
  originalContent: string;
  revisedContent: string;
  revisions: ExpertReview[];
};

export type ExpertReview = {
  type: 'scientific_error' | 'ambiguous_claim' | 'unclear_statement' | 'overly_simplified_claim';
  reason: string;
  text: string;
  suggestion: string;
};

export const ExpertReviewSchema = z.object({
  reviews: z.array(
    z.object({
      type: z.enum([
        'scientific_error',
        'ambiguous_claim',
        'unclear_statement',
        'overly_simplified_claim',
      ]),
      reason: z.string(),
      text: z.string(),
      suggestion: z.string(),
    })
  ),
});
