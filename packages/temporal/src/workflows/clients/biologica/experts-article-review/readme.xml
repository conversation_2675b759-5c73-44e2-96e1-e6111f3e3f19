<!-- Generated on 2025-07-07T17:00:14.400Z -->
<?xml version="1.0" encoding="UTF-8"?>
<workflow>
  <functionName>clientsBiologicaExpertsArticleReviewWorkflow</functionName>
  <displayName>Expert Article Review</displayName>
  <path>clients/biologica/experts-article-review</path>
  <overview>Analyzes and improves scientific article content by splitting it into sections, running expert reviews for accuracy and clarity, and revising content based on expert feedback using AI language models.</overview>
  <flowChart><![CDATA[```mermaid
graph TD
  A[Start] --> B[Validate Input]
  B --> C[Split Article into Sections]
  C --> D[Run Expert Reviews]
  D --> E[Process Reviews Per Section]
  E --> F[Revise Article Sections]
  F --> G[Combine Revised Sections]
  G --> H[Return Results]
  H --> I[End]
```]]></flowChart>
  <activities>
    <activity>
      <functionName>splitArticleIntoSections</functionName>
      <displayName>Split Article Content</displayName>
      <description>Breaks down the input article into smaller sections of approximately 2500 characters for easier processing and review.</description>
    </activity>
    <activity>
      <functionName>runExpertReviews</functionName>
      <displayName>Expert Content Review</displayName>
      <description>Uses Anthropic's Claude model to analyze article sections for scientific errors, ambiguous claims, and unclear statements. Generates structured feedback with specific suggestions for improvement using a specialized prompt template.</description>
    </activity>
    <activity>
      <functionName>reviseArticleSection</functionName>
      <displayName>Content Revision</displayName>
      <description>Utilizes OpenAI's GPT-4 model to revise article sections based on expert reviews. Implements suggested improvements while maintaining the original voice and tone of the content.</description>
    </activity>
  </activities>
</workflow>