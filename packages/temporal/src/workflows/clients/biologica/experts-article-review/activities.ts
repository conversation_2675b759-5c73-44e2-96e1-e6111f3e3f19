import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { splitArticleIntoSections } from '@flow/workflows/shared/activities.js';
import { expertReviewPrompt, articleRevisionPrompt } from './prompts.js';
import type { ExpertReview, Revision } from './types.js';
import { RevisionSchema, ExpertReviewSchema } from './types.js';

const { openaiChatService, anthropicChatService } = workerModule;

export async function runExpertReviews(input: { content: string }): Promise<ExpertReview[]> {
  const prompt = await threadTemplate(
    expertReviewPrompt,
    { content: input.content },
    { format: 'anthropic', strict: true }
  );

  const response = await anthropicChatService.request<{ reviews: ExpertReview[] }>({
    messages: prompt.messages,
    system: prompt.system,
    thinking: { type: 'enabled', budget_tokens: 1500 },
    model: 'claude-sonnet-4-20250514',
    schema: ExpertReviewSchema,
  });

  return typeof response === 'string' ? JSON.parse(response).reviews : response.reviews;
}

export async function reviseArticleSection(input: {
  section: string;
  reviews: ExpertReview[];
}): Promise<string> {
  if (input.reviews.length === 0) {
    return input.section;
  }

  const messages = await threadTemplate(articleRevisionPrompt, {
    content: input.section,
    issues: input.reviews,
  });

  const revision = await openaiChatService.request<Revision>({
    messages,
    model: 'o3',
    schema: RevisionSchema,
    schemaName: 'revision_schema',
  });

  return revision.revisedContent;
}

export { splitArticleIntoSections };

export default WorkflowScope.register(import.meta.url, {
  reviseArticleSection,
  runExpertReviews,
  splitArticleIntoSections,
});
