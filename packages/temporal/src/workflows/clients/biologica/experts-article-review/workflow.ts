import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { handleWorkflowError } from '@flow/sdk/lib/workflow-utils.js';
import { ApplicationFailure } from '@temporalio/workflow';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { WorkflowInputSchema } from './types.js';

const { runExpertReviews, reviseArticleSection, splitArticleIntoSections } = WorkflowScope.use<
  typeof activities
>(import.meta.url, {
  startToCloseTimeout: '15 minute',
  retry: {
    initialInterval: '1 second',
    maximumInterval: '1 minute',
    backoffCoefficient: 2,
    maximumAttempts: 100,
    nonRetryableErrorTypes: ['UNRECOVERABLE_ERROR', 'INVALID_INPUT'],
  },
});

export async function clientsBiologicaExpertsArticleReviewWorkflow(
  input: WorkflowInput
): Promise<WorkflowOutput> {
  // Validate input
  const safeInput = WorkflowInputSchema.safeParse(input);
  if (!safeInput.success) {
    throw ApplicationFailure.create({
      message: `Invalid input: ${safeInput.error.message}`,
      type: 'INVALID_INPUT',
    });
  }

  try {
    const sections = await splitArticleIntoSections({
      article: safeInput.data.content,
      splitterOptions: { chunkSize: 2500 },
    });

    const processedSections = await Promise.all(
      sections.map(async (section) => {
        const expertReviews = await runExpertReviews({
          content: section.content,
        });

        return {
          content: section.content,
          reviews: expertReviews,
        };
      })
    );

    const revisedSections = await Promise.all(
      processedSections.map(async (section) => {
        return await reviseArticleSection({
          section: section.content,
          reviews: section.reviews,
        });
      })
    );

    const revisedContent = revisedSections.join('\n');

    return {
      originalContent: safeInput.data.content,
      revisedContent,
      revisions: processedSections.flatMap((section) => section.reviews),
    };
  } catch (error) {
    handleWorkflowError(error);
  }
}
