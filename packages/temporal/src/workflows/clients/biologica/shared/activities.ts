import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { getDossierCategoryPrompt } from './prompts.js';
import type { DossierCategoryResponse, DossierCategory } from './types.js';
import { DossierCategorySchema } from './types.js';

const { openaiChatService } = workerModule;

export async function getDossierCategory(outline: string): Promise<DossierCategory> {
  const messages = await threadTemplate(getDossierCategoryPrompt, { outline });

  const response = await openaiChatService.request<DossierCategoryResponse>({
    messages,
    model: 'gpt-4o',
    schema: DossierCategorySchema,
    schemaName: 'dossier_category_schema',
  });

  return response.category;
}

export default WorkflowScope.register(import.meta.url, {
  getDossierCategory,
});
