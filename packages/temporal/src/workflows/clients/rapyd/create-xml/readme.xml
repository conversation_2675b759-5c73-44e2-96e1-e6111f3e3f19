<!-- Generated on 2025-07-03T14:56:10.774Z -->
<workflow>
  <functionName>clientsRapydCreateXmlWorkflow</functionName>
  <displayName>Rapyd Create XML</displayName>
  <path>clients/rapyd/create-xml</path>
  <overview>Converts markdown content into WordPress-compatible XML format with Visual Composer shortcodes, handling metadata, formatting, and proper XML structure for blog posts.</overview>
  <flowChart><![CDATA[```mermaid
graph TD
  A[Start] --> B[Validate Input]
  B --> C[Format XML]
  C --> D[Convert Markdown to HTML]
  D --> E[Generate Timestamp]
  E --> F[Process Topics]
  F --> G[Handle Images]
  G --> H[Create XML Structure]
  H --> I[End]
```]]></flowChart>
  <activities>
    <activity>
      <functionName>convertMarkdownToHtml</functionName>
      <displayName>Convert Markdown to HTML</displayName>
      <description>Converts markdown content to HTML using OpenAI chat service. Applies Visual Composer shortcodes, formats headers, maintains link structure.</description>
    </activity>
    <activity>
      <functionName>formatXml</functionName>
      <displayName>Format XML Structure</displayName>
      <description>Creates complete XML structure with post metadata. Handles timestamp generation, topic array conversion, image URL processing, and XML special character escaping. Wraps content in CDATA to preserve HTML formatting.</description>
    </activity>
  </activities>
</workflow>