import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import { threadTemplate } from '@flow/sdk/lib/prompt-template.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { markdownToHtmlPrompt } from './prompts.js';
import type { WorkflowInput } from './types.js';

const { openaiChatService } = workerModule;

/**
 * Converts markdown content to HTML using OpenAI
 */
export async function convertMarkdownToHtml(content: string): Promise<string> {
  const messages = await threadTemplate(markdownToHtmlPrompt, {
    content,
  });

  const response = await openaiChatService.request<string>({
    messages,
    model: 'o3',
  });

  // Ensure we have a string response
  const htmlContent = typeof response === 'string' ? response : String(response);

  // Clean up any markdown code block markers that might have slipped through
  return htmlContent.replace(/^```html\n?/, '').replace(/```$/, '');
}

/**
 * Formats the input into the complete XML structure
 */
export async function formatXml(input: WorkflowInput): Promise<string> {
  // Convert markdown to HTML
  const htmlContent = await convertMarkdownToHtml(input.content);

  // Generate current timestamp in YYYY-MM-DD HH:MM:SS format
  const now = new Date();
  const postDate = now
    .toISOString()
    .replace('T', ' ')
    .replace(/\.\d{3}Z$/, '');

  // Convert topics to array if it's a comma-separated string
  const topicsArray = convertTopicsToArray(input.topics);

  // Handle imageUrl - take first URL if it's an array
  const imageUrl = Array.isArray(input.imageUrl) ? input.imageUrl[0] || '' : input.imageUrl || '';

  // Handle metaDescription - extract the actual description if it's an object
  const metaDescription =
    typeof input.metaDescription === 'object' && input.metaDescription !== null
      ? (input.metaDescription as any).metaDescription || ''
      : input.metaDescription || '';

  return `<?xml version="1.0" encoding="UTF-8"?>
<data>
  <post>
    <title>${escapeXml(input.title)}</title>
    <slug>${escapeXml(input.slug)}</slug>
    <resource_topics>${topicsArray.length > 0 ? topicsArray.join('|') : ''}</resource_topics>
    <authors><EMAIL></authors>
    <meta_description>${escapeXml(metaDescription)}</meta_description>
    <date>${postDate}</date>
    <image_url>${imageUrl}</image_url>
    <image_alt_text>${escapeXml(input.imageAlt || '')}</image_alt_text>
    <content><![CDATA[${htmlContent}]]></content>
  </post>
</data>`;
}

/**
 * Converts topics input to array format
 * Supports both comma-separated strings and arrays
 */
function convertTopicsToArray(topics: string | string[] | undefined): string[] {
  if (!topics) {
    return [];
  }

  if (typeof topics === 'string') {
    return topics
      .split(',')
      .map((topic) => topic.trim())
      .filter((topic) => topic.length > 0);
  }

  return topics;
}

/**
 * Escapes XML special characters
 */
function escapeXml(text: string | undefined | null): string {
  if (!text) {
    return '';
  }

  const stringText = String(text);
  return stringText
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

export default WorkflowScope.register(import.meta.url, {
  convertMarkdownToHtml,
  formatXml,
});
