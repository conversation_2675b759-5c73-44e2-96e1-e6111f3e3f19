import { z } from 'zod';

export const WorkflowInputSchema = z.object({
  title: z.string(),
  content: z.string(),
  slug: z.string(),
  topics: z.union([z.string(), z.array(z.string())]).optional(),
  authorFirstName: z.string().optional(),
  authorLastName: z.string().optional(),
  metaDescription: z.string().optional(),
  imageUrl: z.string().optional(),
  imageAlt: z.string().optional(),
});

export type WorkflowInput = z.infer<typeof WorkflowInputSchema>;

export type WorkflowOutput = string;
