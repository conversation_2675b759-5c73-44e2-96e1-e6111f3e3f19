import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { handleWorkflowError, validateWorkflowInput } from '@flow/sdk/lib/workflow-utils.js';
import { WorkflowInputSchema } from './types.js';

const { formatXml } = WorkflowScope.use<typeof activities>(import.meta.url);

export async function clientsRapydCreateXmlWorkflow(
  rawInput: WorkflowInput
): Promise<WorkflowOutput> {
  // Validate input
  const input = validateWorkflowInput(rawInput, WorkflowInputSchema);

  try {
    // Format all posts as XML
    return await formatXml(input);
  } catch (error) {
    handleWorkflowError(error);
  }
}
