export const markdownToHtmlPrompt = [
  {
    role: 'system',
    content: `
      You are an expert at converting markdown content into HTML format for a WordPress site using Visual Composer.
      Your task is to convert markdown content to clean, well-formatted HTML with proper shortcode structure.
      
      Requirements:
      - Wrap ALL content in Visual Composer shortcodes: [vc_row][vc_column][vc_column_text]...[/vc_column_text][/vc_column][/vc_row]
      - Format h2 headers as: <h2>Header Text</h2>
      - Format h3 headers as: <h3>Header Text</h3>
      - Preserve all links and their targets
      - Convert lists to proper <ul> and <ol> elements
      - Maintain paragraph structure with <p> tags
      - Preserve emphasis (bold, italic) with appropriate HTML tags
      - Do NOT include the title in the content - it will be handled separately
      - Do NOT add any additional content or commentary
      - Do NOT include any markdown syntax in the output
      - Return ONLY the converted HTML with shortcodes
      `,
  },
  {
    role: 'user',
    content: `
      Please convert this markdown content into HTML with Visual Composer shortcodes.
      Make sure to wrap everything in [vc_row][vc_column][vc_column_text] at the start.
      Format all h2 and h3 headers with <strong> tags inside them.
      Do NOT include the title in the content.

      <content>
      {{content}}
      </content>
    `,
  },
];
