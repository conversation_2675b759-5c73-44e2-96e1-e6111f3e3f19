import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type * as activities from './activities.js';
import type { WorkflowInput, WorkflowOutput } from './types.js';
import { handleWorkflowError } from '@flow/sdk/lib/workflow-utils.js';

const { generateCovers } = WorkflowScope.use<typeof activities>(import.meta.url);

export async function clientsRapydArticleCoverWorkflow(
  input: WorkflowInput
): Promise<WorkflowOutput> {
  try {
    return await generateCovers(input);
  } catch (error) {
    handleWorkflowError(error);
  }
}
