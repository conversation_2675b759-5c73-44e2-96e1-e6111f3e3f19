import { WorkflowScope } from '@flow/sdk/lib/workflow-scope.js';
import type { WorkflowInput } from './types.js';

const PLACID_TEMPLATES = ['ctoqwwr4etyxy', '3d48ocmqdzulc', 'yr3djqumuec3t', '3t90obysrhv5t'];

export async function generateCovers(input: WorkflowInput): Promise<string[]> {
  const encodedImage = encodeURIComponent(input.image);

  return PLACID_TEMPLATES.map(
    (template) => `https://api.placid.app/u/${template}?img[image]=${encodedImage}`
  );
}

export default WorkflowScope.register(import.meta.url, {
  generateCovers,
});
