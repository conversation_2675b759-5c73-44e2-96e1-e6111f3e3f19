<?xml version="1.0" encoding="UTF-8"?>
<workflow>
  <functionName>clientsRapydArticleCoverWorkflow</functionName>
  <displayName>Rapyd Article Cover Generator</displayName>
  <path>clients/rapyd/article-cover</path>
  <overview>
    Generates multiple variations of article cover images using Placid's image generation service. The workflow takes a base image URL as input and produces four different cover designs using predefined Placid templates, returning an array of generated image URLs.
  </overview>
  <flowChart>
    ```mermaid
    graph TD
      A[Start] --> B[Validate Input Image URL]
      B --> C[URL Encode Image]
      C --> D[Generate Cover Variations]
      D --> E[Map Templates to URLs]
      E --> F[Return Generated URLs]
      F --> G[End]
    ```
  </flowChart>
  <activities>
    <activity>
      <functionName>generateCovers</functionName>
      <displayName>Generate Cover Images</displayName>
      <description>
        **External Services:**
        - Placid API for image generation
        
        **Key Processing Steps:**
        1. URL encode the input image URL for safe transmission
        2. Map over predefined Placid templates (4 templates):
           - Template IDs: ctoqwwr4etyxy, 3d48ocmqdzulc, yr3djqumuec3t, 3t90obysrhv5t
        3. Generate image URLs by combining template IDs with encoded image
        4. Return array of Placid API URLs

        **Implementation Details:**
        - Uses fixed set of Placid templates for consistent branding
        - Handles URL encoding for special characters in image URLs
        - Returns array of 4 unique cover image URLs
        - Implements retry logic with exponential backoff
        - Maximum 100 retry attempts
        - Non-retryable errors marked as UNRECOVERABLE_ERROR
      </description>
    </activity>
  </activities>
</workflow>