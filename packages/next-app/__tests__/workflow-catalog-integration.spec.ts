import { describe, it, expect } from 'vitest';
import catalog from '../../temporal/src/workflows/catalog.json';

interface WorkflowCatalogEntry {
  functionName: string;
  path: string;
  displayName?: string;
  inputSchema: any;
  outputSchema: any;
}

describe('Workflow Catalog Integration Tests', () => {
  it('should have valid catalog structure', () => {
    expect(catalog).toBeInstanceOf(Array);
    expect(catalog.length).toBeGreaterThan(0);
    console.log(`✅ Catalog contains ${catalog.length} workflows`);

    (catalog as WorkflowCatalogEntry[]).forEach((workflow, index) => {
      expect(workflow).toHaveProperty('functionName');
      expect(workflow).toHaveProperty('path');
      expect(workflow).toHaveProperty('inputSchema');
      expect(workflow).toHaveProperty('outputSchema');

      expect(typeof workflow.functionName).toBe('string');
      expect(typeof workflow.path).toBe('string');
      expect(workflow.inputSchema).toBeInstanceOf(Object);
      expect(workflow.outputSchema).toBeInstanceOf(Object);

      // Check for duplicate function names
      const duplicateIndex = (catalog as WorkflowCatalogEntry[]).findIndex(
        (w, i) => i !== index && w.functionName === workflow.functionName
      );
      expect(duplicateIndex).toBe(-1);
    });

    console.log('✅ All workflows have valid structure');
  });

  it('should have unique function names', () => {
    const functionNames = (catalog as WorkflowCatalogEntry[]).map((w) => w.functionName);
    const uniqueNames = new Set(functionNames);
    expect(uniqueNames.size).toBe(catalog.length);
    console.log('✅ All function names are unique');
  });

  it('should have valid JSON schemas', () => {
    (catalog as WorkflowCatalogEntry[]).forEach((workflow) => {
      // Check that inputSchema has required JSON Schema properties
      expect(workflow.inputSchema).toHaveProperty('type');
      expect(workflow.inputSchema).toHaveProperty('$schema');

      // Check that outputSchema has required JSON Schema properties
      expect(workflow.outputSchema).toHaveProperty('$schema');

      // Validate that schemas are valid JSON Schema
      expect(workflow.inputSchema.$schema).toContain('json-schema.org');
      expect(workflow.outputSchema.$schema).toContain('json-schema.org');
    });

    console.log('✅ All schemas are valid JSON Schema');
  });

  it('should have consistent path structure', () => {
    (catalog as WorkflowCatalogEntry[]).forEach((workflow) => {
      // Paths should not be empty
      expect(workflow.path.length).toBeGreaterThan(0);

      // Paths should not start or end with slashes
      expect(workflow.path).not.toMatch(/^\/|\/$/);

      // Paths should not contain double slashes
      expect(workflow.path).not.toContain('//');
    });

    console.log('✅ All paths have consistent structure');
  });
});
