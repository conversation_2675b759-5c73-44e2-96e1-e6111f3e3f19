import { NextRequest } from 'next/server';
import { describe, expect, it } from 'vitest';
import { GET as GET_INDEX } from '../app/api/workflows-catalog/route';
import { GET as GET_WORKFLOW } from '../app/api/workflows-catalog/[functionName]/route';

describe('Workflows Catalog API Route', () => {
  it('returns 200 and valid JSON data when workflow exists', async () => {
    const request = new NextRequest(`http://localhost:2000/api/workflows-catalog`);
    const response = await GET_INDEX(request);
    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data).toBeDefined();
    expect(data).not.toEqual({});

    const hasContent = data.flowChart !== undefined || Object.keys(data).length > 0;
    expect(hasContent).toBe(true);
  });

  it('returns 200 and valid JSON data when workflow is found', async () => {
    const request = new NextRequest(
      `http://localhost:2000/api/workflows-catalog/seoAssignmentsCreationWorkflow`
    );
    const params = Promise.resolve({ functionName: 'seoAssignmentsCreationWorkflow' });
    const response = await GET_WORKFLOW(request, { params });
    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data).toBeDefined();
    expect(data.workflow.activities.activity.length).toBeGreaterThan(0);
  });

  it('returns 404 when workflow is not found', async () => {
    const request = new NextRequest(
      `http://localhost:2000/api/workflows-catalog/nonExistentWorkflow`
    );
    const params = Promise.resolve({ functionName: 'nonExistentWorkflow' });
    const response = await GET_WORKFLOW(request, { params });
    expect(response.status).toBe(404);
  });
});
