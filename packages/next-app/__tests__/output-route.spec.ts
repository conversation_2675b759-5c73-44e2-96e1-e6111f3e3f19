import { NextRequest } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { afterAll, beforeAll, describe, expect, it, vi } from 'vitest';
import { POST } from '../app/api/workflow/route';
import { GET } from '../app/api/workflows/[workflowId]/[runId]/output/route';
import { TestTemporalWorker } from '__tests__/test-temporal-worker.js';
import { workerModule } from '@flow/sdk/lib/worker-module.js';

export const activities = {
  hello: (name: string) => `Hello, ${name}!`,
  noop: async () => {
    // Simulate a small delay if you want to stretch out the workflow history
    await new Promise((res) => setTimeout(res, 5));
  },
};

const workflowCode = `
import { proxyActivities } from '@temporalio/workflow';

export async function testWorkflow(input: string) {
  const { hello, noop } = proxyActivities({ startToCloseTimeout: '30 seconds' });
  for (let i = 0; i < 100; i++) {
    await noop(); // generates one event pair: schedule -> complete
  }
  return hello(input);
}
`;

describe('Workflow input/output API Route', () => {
  const testTemporalWorker = new TestTemporalWorker({ workflowCode });

  beforeAll(async () => {
    await testTemporalWorker.setUp(activities);
  });

  afterAll(async () => {
    await testTemporalWorker.tearDown();
  });

  it('should return workflow history with pagination', async () => {
    const { workflowEventHistory } = workerModule;
    const workflowName = 'testWorkflow';
    const workflowId = uuidv4();
    let request = new NextRequest('http://localhost:2000/api/workflow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        workflowName,
        workflowId,
        taskQueue: 'test-queue',
        operation: 'execute',
        args: ['Roberto Gomez Bolanos'],
      }),
    });
    // simulate an error from getFullWorkflowHistory to force the paginated history fallback path
    vi.spyOn(workflowEventHistory, 'getWorkflowDetailsOrNull').mockResolvedValue(null);
    const postResponse = await POST(request);
    expect(postResponse.status).toBe(200);
    let data = await postResponse.json();
    const runId = data.runId;
    const url = new URL(`http://localhost:2000/api/workflows/${data.workflowId}/${runId}/output`);
    request = new NextRequest(url);
    const params = Promise.resolve({ workflowId, runId, pageSize: 10 });
    const getResponse = await GET(request, { params });
    expect(getResponse.status).toBe(200);
    data = await getResponse.json();
    expect(data).toMatchObject({
      workflow: {
        id: workflowId,
        runId,
        status: { code: 2, name: 'COMPLETED' },
        startTime: expect.any(String),
        closeTime: expect.any(String),
        executionTime: expect.any(String),
        historyLength: expect.any(Number),
        taskQueue: 'test-queue',
        output: 'Hello, Roberto Gomez Bolanos!',
        input: 'Roberto Gomez Bolanos',
      },
    });
  }, 10_000);

  it('should return 404 if the workflow does not exist', async () => {
    const workflowId = uuidv4();
    const runId = uuidv4();
    const url = new URL(`http://localhost:2000/api/workflows/${workflowId}/${runId}/output`);
    const request = new NextRequest(url);
    const params = Promise.resolve({ workflowId, runId, pageSize: 10 });
    const getResponse = await GET(request, { params });
    expect(getResponse.status).toBe(404);
    const data = await getResponse.json();
    expect(data).toMatchObject({ error: 'Workflow not found' });
  });
});
