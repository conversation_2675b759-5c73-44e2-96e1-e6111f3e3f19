import { NextRequest } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { POST } from '../app/api/workflow/route';
import { GET } from '../app/api/workflows/[workflowId]/[runId]/paginated-history/route';
import { TestTemporalWorker } from '__tests__/test-temporal-worker.js';

export const activities = {
  hello: () => 'Hello, world!',
};

describe('Workflow history API Route', () => {
  const testTemporalWorker = new TestTemporalWorker();

  beforeAll(async () => {
    await testTemporalWorker.setUp(activities);
  });

  afterAll(async () => {
    await testTemporalWorker.tearDown();
  });

  it('should return workflow history with pagination', async () => {
    const workflowName = 'testWorkflow';
    const workflowId = uuidv4();
    let request = new NextRequest('http://localhost:2000/api/workflow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        workflowName,
        workflowId,
        taskQueue: 'test-queue',
        operation: 'execute',
        args: [],
      }),
    });
    const postResponse = await POST(request);
    expect(postResponse.status).toBe(200);
    let data = await postResponse.json();
    const runId = data.runId;
    const url = new URL(`http://localhost:2000/api/workflows/${data.workflowId}/${runId}/history`);
    request = new NextRequest(url);
    const params = Promise.resolve({ workflowId, runId });
    const getResponse = await GET(request, { params });
    expect(getResponse.status).toBe(200);
    data = await getResponse.json();
    expect(data).toMatchObject({
      workflow: {
        id: workflowId,
        runId,
        status: { code: 2, name: 'COMPLETED' },
        startTime: expect.any(String),
        closeTime: expect.any(String),
        executionTime: expect.any(String),
        historyLength: expect.any(Number),
        taskQueue: 'test-queue',
      },
      history: expect.arrayContaining([
        expect.objectContaining({
          eventId: expect.any(String),
          eventType: expect.any(String),
        }),
      ]),
      nextPageToken: expect.any(String),
      historyError: null,
    });
  });
});
