import { WorkflowBundler } from '@flow/sdk/lib/workflow-bundler.js';
import { TestWorkflowEnvironment } from '@temporalio/testing';
import type { LogEntry } from '@temporalio/worker';
import { DefaultLogger, Runtime, Worker } from '@temporalio/worker';

// we need to use a fixed port for the test environment otherwise it will try to use a random port
const DEFAULT_TEMPORAL_PORT = 7234;
process.env.TEMPORAL_ADDRESS = `127.0.0.1:${DEFAULT_TEMPORAL_PORT}`;

const DEFAULT_WORKFLOW_CODE = `
import { proxyActivities } from '@temporalio/workflow';
export async function testWorkflow() {
  const { hello } = proxyActivities({ startToCloseTimeout: '30 seconds' });
  return hello();
}
`;

// A test worker that can be used to run workflows in a test environment
// Accepts an optional port and workflow code to run, otherwise uses default values
export class TestTemporalWorker {
  private testEnv: TestWorkflowEnvironment | null = null;
  private worker: Worker | null = null;
  private workflowBundler = new WorkflowBundler();
  private readonly port: number;
  private readonly workflowCode: string;

  constructor({
    port = DEFAULT_TEMPORAL_PORT,
    workflowCode = DEFAULT_WORKFLOW_CODE,
  }: {
    port?: number;
    workflowCode?: string;
  } = {}) {
    this.port = port;
    this.workflowCode = workflowCode;
  }

  async setUp(activities: object) {
    Runtime.install({
      logger: new DefaultLogger('WARN', (entry: LogEntry) =>
        console.log(`[${entry.level}]`, entry.message)
      ),
    });
    this.testEnv = await TestWorkflowEnvironment.createLocal({ server: { port: this.port } });
    const bundleOptions = await this.workflowBundler.writeBundleFile(this.workflowCode, activities);
    this.worker = await Worker.create({
      connection: this.testEnv.nativeConnection,
      taskQueue: 'test-queue',
      ...bundleOptions,
    });
    // Don't await worker.run() as it's designed to run indefinitely
    // The worker needs to stay running in the background to process workflows
    void this.worker.run();
  }

  async tearDown() {
    this.worker?.shutdown();
    // wait for worker to gracefully shutdown
    while (this.worker?.getState() !== 'STOPPED') {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
    await this.testEnv?.teardown();
    await this.workflowBundler.cleanupFiles();
  }
}
