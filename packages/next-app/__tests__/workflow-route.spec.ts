import { TestTemporalWorker } from '__tests__/test-temporal-worker.js';
import { v4 as uuidv4 } from 'uuid';
import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { POST } from '../app/api/workflow/route';

export const activities = {
  hello: () => 'Hello, world!',
};

describe('Workflow API Route', () => {
  const testTemporalWorker = new TestTemporalWorker();

  beforeAll(async () => {
    await testTemporalWorker.setUp(activities);
  });

  afterAll(async () => {
    await testTemporalWorker.tearDown();
  });

  it('executes workflow synchronously when operation is "execute"', async () => {
    const workflowName = 'testWorkflow';
    const workflowId = uuidv4();
    const request = new Request('http://localhost:2000/api/workflow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        workflowName,
        workflowId,
        taskQueue: 'test-queue',
        args: [],
        operation: 'execute',
      }),
    });

    const response = await POST(request);
    expect(response.status).toBe(200);

    const data = await response.json();
    expect(data).toMatchObject({
      workflowId: workflowId,
      // runId is a dynamic uuid generated by temporal, so we can't assert it
      runId: expect.any(String),
      status: 'completed',
      workflowName,
      result: 'Hello, world!',
    });
  });
});
