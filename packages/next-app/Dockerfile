FROM node:24.3-alpine

WORKDIR /app

# Copy root package files
COPY package.json yarn.lock ./
COPY packages/next-app/package.json packages/next-app/
COPY packages/temporal/package.json packages/temporal/
COPY packages/sdk/package.json packages/sdk/

RUN yarn install --frozen-lockfile --ignore-scripts

COPY . .

# Set working directory to the next-app package
WORKDIR /app/packages/next-app

RUN yarn build

EXPOSE 10000

CMD ["yarn", "start"]
