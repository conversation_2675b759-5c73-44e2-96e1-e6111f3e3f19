import { TASK_QUEUE_INTERNAL } from '@flow/sdk/shared';
import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { workerModule } from '@flow/sdk/lib/worker-module.js';

export const maxDuration = 600; // 10 minutes in seconds used for our long running workflows

export async function POST(req: Request) {
  // Add headers for long-running requests
  const headers = new Headers({
    Connection: 'keep-alive',
    'Keep-Alive': 'timeout=600',
    'Content-Type': 'application/json',
  });

  try {
    const { workflowId = uuidv4(), args, operation = 'start' } = await req.json();
    const { temporalClient } = workerModule;

    // Force task queue to internal (low-priority)
    const taskQueue = TASK_QUEUE_INTERNAL;
    const workflowName = 'clientsCodelibraryRulesWorkflow';

    console.log('POST /api/codelibrary/rules-workflow', {
      operation,
      workflowId,
      taskQueue,
      workflowName,
    });

    // Always start the workflow asynchronously
    const handle = await temporalClient.startWorkflow(workflowName, {
      taskQueue,
      workflowId,
      args,
    });

    const description = await handle.describe();

    // Construct base response
    const response = {
      workflowId, // Use the original workflowId for backward compatibility
      runId: description.runId, // Include the runId from the workflow description
      status: operation === 'execute' ? 'completed' : 'started',
      workflowName,
    };

    // If operation is 'execute', wait for the result
    if (operation === 'execute') {
      const result = await handle.result();
      return NextResponse.json({ ...response, result }, { status: 200, headers });
    }

    // For 'start' operation, return immediately
    return NextResponse.json(response, { status: 202, headers });
  } catch (error: unknown) {
    console.error('Workflow error:', error);

    const err = error as Error & { cause?: { type?: string; message?: string } };

    if (err?.cause?.type === 'BAD_REQUEST') {
      return NextResponse.json({ error: err.cause.message }, { status: 400, headers });
    }

    if (err.cause?.message) {
      return NextResponse.json({ error: err.cause.message }, { status: 500, headers });
    }

    return NextResponse.json({ error: err.message }, { status: 500, headers });
  }
}
