import { workerModule } from '@flow/sdk/lib/worker-module.js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import type { History } from '@temporalio/common/lib/proto-utils';
import { temporal } from '@temporalio/proto/protos';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string; runId: string }> }
): Promise<NextResponse> {
  try {
    const { workflowId, runId } = await params;
    const { temporalClient } = workerModule;
    const handle = await temporalClient.getWorkflowHandle(workflowId, runId);

    // Try to get history, but don't fail if it errors
    let history: History | null = null;
    try {
      history = await handle.fetchHistory();
    } catch (error) {
      console.error('Error fetching workflow history:', error);
      // Continue execution with null history
    }

    const { status, startTime, closeTime, executionTime, historyLength, taskQueue } =
      await handle.describe();

    // Only try to parse I/O if we have history
    const { input, output } = history?.events
      ? parseWorkflowIO(history.events)
      : { input: null, output: null };

    // Combine the data
    const response = {
      workflow: {
        id: workflowId,
        runId,
        status,
        startTime,
        closeTime,
        executionTime,
        historyLength,
        taskQueue,
        input,
        output,
      },
      history: history?.events || [],
      historyError: history ? null : 'Failed to fetch workflow history',
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in workflow history endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to process workflow history request' },
      { status: 500 }
    );
  }
}

export function parseWorkflowIO(events: History['events']): {
  input: Record<string, any> | null;
  output: Record<string, any> | null;
} {
  if (!events) {
    return { input: null, output: null };
  }

  const startedEvent = events.find(
    (event) =>
      event.eventType === temporal.api.enums.v1.EventType.EVENT_TYPE_WORKFLOW_EXECUTION_STARTED
  );
  const completedEvent = events.find(
    (event) =>
      event.eventType === temporal.api.enums.v1.EventType.EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED
  );

  return {
    input: parseEventPayload(startedEvent?.workflowExecutionStartedEventAttributes, 'input'),
    output: parseEventPayload(completedEvent?.workflowExecutionCompletedEventAttributes, 'result'),
  };
}

function parseEventPayload(event: any, path: string): Record<string, any> | null {
  if (!event) return null;

  const payloads = event[path]?.payloads;
  if (!payloads?.[0]) return null;

  const { metadata, data } = payloads[0];
  const isBase64Encoded = metadata?.encoding
    ? Buffer.from(metadata.encoding, 'base64').toString() === 'json/plain'
    : false;

  return isBase64Encoded ? JSON.parse(Buffer.from(data, 'base64').toString()) : data;
}
