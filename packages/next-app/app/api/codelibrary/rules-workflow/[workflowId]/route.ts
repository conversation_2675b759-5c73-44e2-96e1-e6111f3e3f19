import { workerModule } from '@flow/sdk/lib/worker-module.js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
): Promise<NextResponse> {
  try {
    const { workflowId } = await params;
    const { temporalClient } = workerModule;
    const handle = await temporalClient.getWorkflowHandle(workflowId);
    const description = await handle.describe();

    if (description.runId) {
      return NextResponse.json({
        workflowId,
        runId: description.runId,
        status: description.status,
        historyUrl: `/api/codelibrary/rules-workflow/${workflowId}/${description.runId}/history`,
      });
    } else {
      return NextResponse.json(
        {
          error: 'Workflow not found',
          workflowId,
        },
        { status: 404 }
      );
    }
  } catch (error: any) {
    console.error('Error:', error);

    // Check for specific WorkflowNotFoundError or error message containing "not found"
    if (error.name === 'WorkflowNotFoundError') {
      return NextResponse.json(
        {
          error: 'Workflow not found',
          workflowId: await params.then((p) => p.workflowId),
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to process request',
        workflowId: await params.then((p) => p.workflowId),
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
): Promise<NextResponse> {
  try {
    const { workflowId } = await params;
    const searchParams = request.nextUrl.searchParams;
    const action = searchParams.get('action') || 'cancel'; // default to cancel
    const reason = searchParams.get('reason') || 'Cancelled via API';
    const runId = searchParams.get('runId') || undefined;

    const { temporalClient } = workerModule;

    console.log('DELETE /api/codelibrary/rules-workflow/[workflowId]', {
      workflowId,
      action,
      reason: action === 'terminate' ? reason : undefined,
      runId,
    });

    if (action === 'terminate') {
      await temporalClient.terminateWorkflow(workflowId, reason, runId);
      return NextResponse.json({
        workflowId,
        action: 'terminated',
        reason,
        message: 'Workflow terminated successfully',
      });
    } else if (action === 'cancel') {
      await temporalClient.cancelWorkflow(workflowId, runId);
      return NextResponse.json({
        workflowId,
        action: 'cancelled',
        message: 'Workflow cancellation requested successfully',
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid action. Use "cancel" or "terminate"' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Error stopping workflow:', error);

    const { workflowId } = await params;

    // Check for specific WorkflowNotFoundError or error message containing "not found"
    if (
      error.name === 'WorkflowNotFoundError' ||
      error.message?.includes('not found') ||
      error.message?.includes('WorkflowNotFoundError')
    ) {
      return NextResponse.json(
        {
          error: 'Workflow not found',
          workflowId,
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        error: `Failed to stop workflow: ${error.message}`,
        workflowId,
      },
      { status: 500 }
    );
  }
}
