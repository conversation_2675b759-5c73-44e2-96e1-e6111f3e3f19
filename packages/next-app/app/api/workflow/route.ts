import { TASK_QUEUE_DEFAULT, TASK_QUEUE_INTERNAL } from '@flow/sdk/shared';
import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import logger from 'logger';
import type { WorkflowHandle } from '@temporalio/client';

export const maxDuration = 600; // 10 minutes in seconds used for our long running workflows

const getWorkflowTaskQueue = (workflowPath: string) => {
  if (workflowPath.includes('hiring')) {
    return TASK_QUEUE_INTERNAL;
  } else {
    return TASK_QUEUE_DEFAULT;
  }
};

export async function POST(req: Request) {
  // Add headers for long-running requests
  const headers = new Headers({
    Connection: 'keep-alive',
    'Keep-Alive': 'timeout=600',
    'Content-Type': 'application/json',
  });

  try {
    const {
      workflowName,
      workflowId = uuidv4(),
      args,
      operation = 'start',
      taskQueue = getWorkflowTaskQueue(workflowName),
    } = await req.json();
    const { temporalClient } = workerModule;

    if (!workflowName) {
      return NextResponse.json({ error: 'workflowName is required' }, { status: 400, headers });
    }

    logger.info('POST /api/workflow', { operation, workflowName, workflowId, taskQueue });

    // Always start the workflow asynchronously
    const handle = await temporalClient.startWorkflow(workflowName, {
      taskQueue,
      workflowId,
      args,
    });

    const description = await handle.describe();

    // non-blocking
    logWorkflowExecution(handle).catch((error) =>
      logger.error('Failed to log workflow execution:', error)
    );

    // Construct base response
    const response = {
      workflowId, // Use the original workflowId for backward compatibility
      runId: description.runId, // Include the runId from the workflow description
      status: operation === 'execute' ? 'completed' : 'started',
      workflowName,
    };

    // If operation is 'execute', wait for the result
    if (operation === 'execute') {
      const result = await handle.result();
      return NextResponse.json({ ...response, result }, { status: 200, headers });
    }

    // For 'start' operation, return immediately
    return NextResponse.json(response, { status: 202, headers });
  } catch (error: unknown) {
    logger.error('Workflow error:', error);

    const err = error as Error & { cause?: { type?: string; message?: string } };

    if (err?.cause?.type === 'BAD_REQUEST') {
      return NextResponse.json({ error: err.cause.message }, { status: 400, headers });
    }

    if (err.cause?.message) {
      return NextResponse.json({ error: err.cause.message }, { status: 500, headers });
    }

    return NextResponse.json({ error: err.message }, { status: 500, headers });
  }
}

async function logWorkflowExecution(workflowHandle: WorkflowHandle) {
  const startTime = Date.now();
  const description = await workflowHandle.describe();
  logger.info(`workflow started`, {
    workflowId: workflowHandle.workflowId,
    runId: description.runId,
    workflowName: description.type ?? 'unknown',
    startTime: new Date(startTime).toISOString(),
  });

  const logWorkflowEnd = async (status: 'completed' | 'failed') => {
    const endTime = Date.now();
    const duration = endTime - startTime;
    const finalDescription = await workflowHandle.describe();

    logger.info(`workflow ${status}`, {
      workflowId: workflowHandle.workflowId,
      runId: finalDescription.runId,
      workflowName: finalDescription.type ?? 'unknown',
      endTime: new Date(endTime).toISOString(),
      duration: duration,
      status: finalDescription.status.name,
    });
  };

  try {
    await workflowHandle.result();
    await logWorkflowEnd('completed');
  } catch {
    // we assume the workflow error is handled by the workflow itself or somewhere else, so we don't log it here
    await logWorkflowEnd('failed');
  }
}
