import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { TASK_QUEUE_DEFAULT } from '@flow/sdk/shared';
import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

// Client configuration with tokens and allowed workflow paths
const ACCOUNT_CONFIG = {
  teero: {
    token: 'tk_teero_ZMjBG70sPBQeDMs4BSr3VqsBR1eugbjYALUyx2iM6KMl7j',
  },
} as const;

export async function POST(req: Request) {
  const headers = new Headers({
    Connection: 'keep-alive',
    'Keep-Alive': 'timeout=600',
    'Content-Type': 'application/json',
  });

  try {
    // Get authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401, headers }
      );
    }

    const token = authHeader.split(' ')[1];
    const account = Object.entries(ACCOUNT_CONFIG).find(([_, config]) => config.token === token);

    if (!account) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401, headers });
    }

    const [accountName] = account;
    const { workflowName, workflowId = uuidv4(), args } = await req.json();

    if (!workflowName) {
      return NextResponse.json({ error: 'workflowName is required' }, { status: 400, headers });
    }

    // Check if workflow is allowed for this client
    console.log(`Account ${accountName} executing workflow:`, workflowName);
    const { temporalClient } = workerModule;
    const result = await temporalClient.executeWorkflow(workflowName, {
      taskQueue: TASK_QUEUE_DEFAULT,
      workflowId,
      args,
    });

    return NextResponse.json(
      {
        runId: workflowId,
        status: 'completed',
        workflowName,
        result,
      },
      { headers }
    );
  } catch (error: any) {
    console.error('Workflow error:', error);

    if (error?.cause?.type === 'BAD_REQUEST') {
      return NextResponse.json({ error: error.cause.message }, { status: 400, headers });
    }

    return NextResponse.json({ error: error.message }, { status: 500, headers });
  }
}
