import { workerModule } from '@flow/sdk/lib/worker-module.js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string; runId: string }> }
): Promise<NextResponse> {
  const { workflowId, runId } = await params;
  const { workflowEventHistory } = workerModule;
  const workflowData = await workflowEventHistory.getFullWorkflowHistory(workflowId, runId);
  if (!workflowData) {
    return NextResponse.json({ historyError: 'Workflow not found' }, { status: 404 });
  }
  return NextResponse.json(workflowData);
}
