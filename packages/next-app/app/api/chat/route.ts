import type { MessageParam } from '@anthropic-ai/sdk/resources/index.mjs';
import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { sleep } from '@flow/sdk/lib/async-helpers.js';
import type { WorkflowHandle } from '@temporalio/client';
import { defineSignal, defineUpdate } from '@temporalio/workflow';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { randomUUID } from 'crypto';
import logger from 'logger';

export const ALLOWED_CHAT_ORIGINS = {
  development: ['http://localhost:3100', 'http://localhost:3000'],
  production: [
    'https://studio.growthx.ai',
    'https://atlas.growthx.ai',
    'http://atlas-docker:80',
    'http://atlas-docker:3000',
  ],
};

const NODE_ENV = process.env.NODE_ENV;

// TODO: BEGIN: These type definitions should probably live under `packages/sdk`.
// Keeping them here for now.
interface UserMessage {
  role: 'user';
  content: string;
}

interface SystemMessage {
  role: 'system';
  content: string;
}

export type TokenResponse = {
  token: string | null;
  done: boolean;
};

type MessageUpdate = {
  systemMessage: SystemMessage;
  userMessages: UserMessage[];
};

// Define signals to match the chat workflow
const sendMessage = defineSignal<[MessageUpdate]>('sendMessage');
const getNextToken = defineUpdate<TokenResponse, []>('getNextToken');
const unblockSignal = defineSignal('unblock');
const WORKFLOW_NAME = 'chatSessionWorkflow';
// TODO: END

/** * Add CORS headers to the response */
export function addCorsHeaders(
  response: NextResponse,
  request: NextRequest,
  methods?: string
): NextResponse {
  const nodeEnv = (NODE_ENV || 'development') as keyof typeof ALLOWED_CHAT_ORIGINS;
  const allowedOrigins = ALLOWED_CHAT_ORIGINS[nodeEnv] || [];

  // Check if the request origin is in our allowed list
  const origin =
    request.headers.get('origin') && allowedOrigins.includes(request.headers.get('origin')!)
      ? request.headers.get('origin')
      : allowedOrigins[0] || '*';

  response.headers.set('Access-Control-Allow-Origin', origin || '*');
  response.headers.set('Access-Control-Allow-Methods', methods || 'GET, POST, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  response.headers.set('Access-Control-Allow-Credentials', 'true');

  return response;
}

/** * Handle OPTIONS requests for CORS preflight */
export async function OPTIONS(req: NextRequest) {
  const response = NextResponse.json({}, { status: 200 });
  return addCorsHeaders(response, req);
}

export async function POST(req: NextRequest) {
  const { temporalClient } = workerModule;

  try {
    const body = await req.json();
    const { conversation, sessionId: providedSessionId } = body as {
      conversation: MessageParam[];
      sessionId?: string;
    };

    // Use provided session ID or generate a new one
    const sessionId = providedSessionId || `chat-session-${randomUUID()}`;

    const handle = await temporalClient.startWorkflow(WORKFLOW_NAME, {
      taskQueue: 'default',
      args: [{ sessionId }],
      workflowId: sessionId,
    });
    if (!conversation) {
      const errorResponse = NextResponse.json(
        { error: 'Missing conversation in request body' },
        { status: 400 }
      );
      return addCorsHeaders(errorResponse, req || undefined, 'GET, POST, OPTIONS');
    }

    let abortController: AbortController | null;
    // Create a ReadableStream for streaming the response
    const stream = new ReadableStream({
      async start(controller) {
        abortController = new AbortController();
        const system: SystemMessage = conversation.find(
          (message) => (message as unknown as { role: string }).role === 'system'
        ) as unknown as SystemMessage;
        const messages = conversation.filter(
          (message) => (message as unknown as { role: string }).role !== 'system'
        );
        try {
          // Unblock the workflow first
          await handle.signal(unblockSignal);

          for await (const token of streamTokens(handle, messages, system)) {
            if (abortController.signal.aborted) {
              break;
            }
            controller.enqueue(new TextEncoder().encode(token));
          }
          controller.close();
        } catch (err) {
          logger.error('Error in /api/chat:', err);
          controller.error(err);
        }
      },

      cancel() {
        abortController?.abort();
      },
    });

    // Return the response with the stream and CORS headers
    const response = new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    });

    return addCorsHeaders(response, req, 'GET, POST, OPTIONS');
  } catch (err) {
    logger.error('Error in /api/chat:', err);
    const errorResponse = NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    return addCorsHeaders(errorResponse, req, 'GET, POST, OPTIONS');
  }
}

async function* streamTokens(
  handle: WorkflowHandle,
  messages: MessageParam[],
  system: SystemMessage
) {
  await handle.signal(sendMessage, { userMessages: messages, systemMessage: system });
  let finished = false;
  while (!finished) {
    const result = (await handle.executeUpdate(getNextToken)) as TokenResponse;
    const { token, done } = result;
    if (token) {
      yield token;
    }
    finished = done;
    // avoid hammering the server
    await sleep(50);
  }
}
