import { workerModule } from '@flow/sdk/lib/worker-module.js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { addCorsHeaders } from '../route';

export async function OPTIONS(req: NextRequest) {
  const response = NextResponse.json({}, { status: 200 });
  return addCorsHeaders(response, req, 'GET, OPTIONS');
}

// GET /api/chat/sessions - List active chat sessions
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { temporalClient } = workerModule;

    // Get search parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || 'RUNNING'; // RUNNING, COMPLETED, FAILED, etc.

    // List workflows with chat session pattern
    const listResponse = await temporalClient.listWorkflowExecutions({
      query: `WorkflowType="chatSessionWorkflow"${status !== 'ALL' ? ` AND ExecutionStatus="${status}"` : ''}`,
    });

    const sessions = [];
    let count = 0;

    for (const workflow of listResponse.executions) {
      if (count >= limit) break;

      const workflowStatus = workflow.status?.toString() || '';
      try {
        // Try to get session state for running workflows
        let sessionState = null;
        if (workflowStatus === 'WORKFLOW_EXECUTION_STATUS_RUNNING') {
          try {
            const handle = await temporalClient.getWorkflowHandle(
              workflow.execution?.workflowId || ''
            );
            sessionState = await handle.query('getSessionState');
          } catch (queryError) {
            // Session might not be ready yet, skip state
            console.warn(
              `Error getting session state for session ${workflow.execution?.workflowId || 'unknown'}:`,
              queryError
            );
          }
        }

        sessions.push({
          sessionId: workflow.execution?.workflowId || '',
          status: workflowStatus,
          runId: workflow.execution?.runId || '',
          startTime: workflow.startTime,
          closeTime: workflow.closeTime,
          sessionState,
        });
        count++;
      } catch (error) {
        console.warn(
          `Error getting details for session ${workflow.execution?.workflowId || 'unknown'}:`,
          error
        );
        // Add basic info even if we can't get full details
        sessions.push({
          sessionId: workflow.execution?.workflowId || '',
          status: workflowStatus || 'UNKNOWN',
          runId: workflow.execution?.runId || '',
          startTime: workflow.startTime,
          closeTime: workflow.closeTime,
          sessionState: null,
          error: 'Could not fetch session details',
        });
        count++;
      }
    }

    const response = NextResponse.json({
      sessions,
      total: sessions.length,
      limit,
      status: status === 'ALL' ? 'ALL' : status,
    });

    return addCorsHeaders(response, request, 'GET, OPTIONS');
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error listing chat sessions:', error);
    const response = NextResponse.json(
      { error: `Failed to list sessions: ${errorMessage}` },
      { status: 500 }
    );
    return addCorsHeaders(response, request, 'GET, OPTIONS');
  }
}
