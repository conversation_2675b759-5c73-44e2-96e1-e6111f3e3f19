import { workerModule } from '@flow/sdk/lib/worker-module.js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { addCorsHeaders } from '../../route';

const CHAT_WORKFLOW_NAME = 'chatSessionWorkflow';
const TASK_QUEUE = 'default';

export async function OPTIONS(req: NextRequest) {
  const response = NextResponse.json({}, { status: 200 });
  return addCorsHeaders(response, req, 'GET, POST, DELETE, OPTIONS');
}

// GET /api/chat/sessions/[sessionId] - Get session status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;
    const { temporalClient } = workerModule;

    try {
      const handle = await temporalClient.getWorkflowHandle(sessionId);
      const description = await handle.describe();

      if (description.status) {
        // Get session state if workflow is running
        let sessionState = null;
        if (description.status.name === 'RUNNING') {
          try {
            sessionState = await handle.query('getSessionState');
          } catch (queryError) {
            console.log('Session state query failed (workflow may not be ready):', queryError);
          }
        }

        const response = NextResponse.json({
          sessionId,
          status: description.status.name,
          runId: description.runId,
          sessionState,
          isActive: description.status.name === 'RUNNING',
        });
        return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
      }
    } catch (error: any) {
      if (error.message?.includes('not found')) {
        const response = NextResponse.json(
          { error: 'Session not found', sessionId },
          { status: 404 }
        );
        return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
      }
      throw error;
    }

    const response = NextResponse.json({ error: 'Session not found', sessionId }, { status: 404 });
    return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
  } catch (error) {
    console.error('Error getting session status:', error);
    const response = NextResponse.json({ error: 'Failed to get session status' }, { status: 500 });
    return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
  }
}

// POST /api/chat/sessions/[sessionId] - Start/create session
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;
    const { temporalClient } = workerModule;

    // Parse config from request body (optional)
    let config = undefined;
    try {
      const body = await request.json();
      config = body.config;
    } catch {
      // No body or invalid JSON, use defaults
    }

    // Start workflow if not exists
    const handle = await temporalClient.startWorkflowIfNotExists(CHAT_WORKFLOW_NAME, {
      taskQueue: TASK_QUEUE,
      args: [{ sessionId, config }],
      workflowId: sessionId,
    });

    // Unblock the workflow to make it ready for messages
    await handle.signal('unblock');

    const description = await handle.describe();

    const response = NextResponse.json({
      sessionId,
      status: 'created',
      runId: description.runId,
      message: 'Chat session started successfully',
    });

    return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
  } catch (error: any) {
    console.error('Error creating session:', error);
    const response = NextResponse.json(
      { error: `Failed to create session: ${error.message}` },
      { status: 500 }
    );
    return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
  }
}

// DELETE /api/chat/sessions/[sessionId] - End session
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;
    const { temporalClient } = workerModule;

    try {
      const handle = await temporalClient.getWorkflowHandle(sessionId);

      // Try to end gracefully first
      await handle.signal('endSession');

      const response = NextResponse.json({
        sessionId,
        status: 'ended',
        message: 'Session ended successfully',
      });

      return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
    } catch (error: any) {
      if (error.message?.includes('not found')) {
        const response = NextResponse.json(
          { error: 'Session not found', sessionId },
          { status: 404 }
        );
        return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
      }
      throw error;
    }
  } catch (error: any) {
    console.error('Error ending session:', error);
    const response = NextResponse.json(
      { error: `Failed to end session: ${error.message}` },
      { status: 500 }
    );
    return addCorsHeaders(response, request, 'GET, POST, DELETE, OPTIONS');
  }
}
