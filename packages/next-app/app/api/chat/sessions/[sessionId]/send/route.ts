import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { defineSignal } from '@temporalio/workflow';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { addCorsHeaders } from '../../../route';

// Import types from our enhanced chat workflow
interface UserMessage {
  role: 'user';
  content: string;
}

interface SystemMessage {
  role: 'system';
  content: string;
}

type MessageUpdate = {
  systemMessage: SystemMessage;
  userMessages: UserMessage[];
};

const sendMessage = defineSignal<[MessageUpdate]>('sendMessage');

export async function OPTIONS(req: NextRequest) {
  const response = NextResponse.json({}, { status: 200 });
  return addCorsHeaders(response, req, 'POST, OPTIONS');
}

// POST /api/chat/sessions/[sessionId]/send - Send message to session
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;
    const { temporalClient } = workerModule;

    // Parse request body
    const body = await request.json();
    const { message, system } = body as {
      message: string;
      system?: string;
    };

    if (!message || typeof message !== 'string') {
      const response = NextResponse.json(
        { error: 'Missing or invalid message in request body' },
        { status: 400 }
      );
      return addCorsHeaders(response, request, 'POST, OPTIONS');
    }

    // Get workflow handle
    const handle = await temporalClient.getWorkflowHandle(sessionId);

    // Check if session exists and is running
    const description = await handle.describe();

    if (description.status.name !== 'RUNNING') {
      const response = NextResponse.json(
        {
          error: 'Session is not active',
          sessionId,
          status: description.status.name,
        },
        { status: 400 }
      );
      return addCorsHeaders(response, request, 'POST, OPTIONS');
    }

    // Prepare message format for the workflow
    const systemMessage: SystemMessage = {
      role: 'system',
      content: system || 'You are a helpful AI assistant. Be concise and helpful.',
    };

    const userMessage: UserMessage = {
      role: 'user',
      content: message,
    };

    // Send message to workflow
    await handle.signal(sendMessage, {
      systemMessage,
      userMessages: [userMessage],
    });

    const response = NextResponse.json({
      sessionId,
      status: 'sent',
      message: 'Message sent successfully',
      sentMessage: message,
    });

    return addCorsHeaders(response, request, 'POST, OPTIONS');
  } catch (error: unknown) {
    if (error instanceof Error && error.message?.includes('not found')) {
      const response = NextResponse.json(
        { error: 'Session not found. Please create the session first.' },
        { status: 404 }
      );
      return addCorsHeaders(response, request, 'POST, OPTIONS');
    }

    const response = NextResponse.json(
      {
        error: `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
      { status: 500 }
    );
    return addCorsHeaders(response, request, 'POST, OPTIONS');
  }
}
