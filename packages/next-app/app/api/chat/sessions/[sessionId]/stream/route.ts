import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { sleep } from '@flow/sdk/lib/async-helpers.js';
import { defineUpdate } from '@temporalio/workflow';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { addCorsHeaders } from '../../../route';

// Token response type from workflow
export type TokenResponse = {
  token: string | null;
  done: boolean;
};

const getNextToken = defineUpdate<TokenResponse, []>('getNextToken');

export async function OPTIONS(req: NextRequest) {
  const response = NextResponse.json({}, { status: 200 });
  return addCorsHeaders(response, req, 'GET, OPTIONS');
}

// GET /api/chat/sessions/[sessionId]/stream - SSE stream for real-time tokens
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
): Promise<NextResponse> {
  try {
    const { sessionId } = await params;
    const { temporalClient } = workerModule;

    // Get workflow handle
    const handle = await temporalClient.getWorkflowHandle(sessionId);

    // Check if session exists and is running
    const description = await handle.describe();
    if (description.status.name !== 'RUNNING') {
      const response = NextResponse.json(
        {
          error: 'Session is not active',
          sessionId,
          status: description.status.name,
        },
        { status: 400 }
      );
      return addCorsHeaders(response, request, 'GET, OPTIONS');
    }

    // Create SSE stream
    let abortController: AbortController | null;
    const stream = new ReadableStream({
      async start(controller) {
        abortController = new AbortController();
        const encoder = new TextEncoder();

        // Send initial connection event
        controller.enqueue(
          encoder.encode(`event: connected\ndata: {"sessionId":"${sessionId}"}\n\n`)
        );

        // Keep-alive every 15 seconds
        const keepAliveInterval = setInterval(() => {
          if (!abortController?.signal?.aborted) {
            controller.enqueue(encoder.encode(`:\n\n`)); // SSE comment
          }
        }, 15_000);

        try {
          let finished = false;
          let consecutiveEmptyPolls = 0;
          let totalAttempts = 0;
          const maxEmptyPolls = 200; // 10 seconds of empty polls (50ms * 200)
          const maxInitialRetries = 20; // Allow more initial retries before considering it an error

          // Give the workflow a moment to start processing the message
          await sleep(500);

          while (
            !finished &&
            consecutiveEmptyPolls < maxEmptyPolls &&
            !abortController.signal.aborted
          ) {
            try {
              totalAttempts++;
              const { token, done } = await handle.executeUpdate(getNextToken);

              if (token) {
                // Send token data
                const tokenData = JSON.stringify({ token, done });
                controller.enqueue(encoder.encode(`event: token\ndata: ${tokenData}\n\n`));
                consecutiveEmptyPolls = 0; // Reset counter on successful token
              } else if (done) {
                // Stream is complete
                controller.enqueue(encoder.encode(`event: complete\ndata: {"done":true}\n\n`));
                finished = true;
              } else {
                // No token available, increment empty poll counter
                consecutiveEmptyPolls++;
              }

              if (!finished) {
                // Small delay to avoid hammering the server
                await sleep(50);
              }
            } catch (updateError: unknown) {
              console.error(`Error getting next token (attempt ${totalAttempts}):`, updateError);

              // Check if workflow is still running
              const currentDescription = await handle.describe();
              if (currentDescription.status.name !== 'RUNNING') {
                controller.enqueue(
                  encoder.encode(
                    `event: session_ended\ndata: {"status":"${currentDescription.status.name}"}\n\n`
                  )
                );
                finished = true;
              } else if (totalAttempts <= maxInitialRetries) {
                // For initial attempts, just wait and retry without sending error events
                await sleep(250); // Wait a bit longer for initial retries
                consecutiveEmptyPolls++; // Count as empty poll to prevent infinite retries
              } else {
                // After initial retries, send error events but continue trying
                const errorData = JSON.stringify({
                  error: 'Token fetch error',
                  retrying: true,
                  attempt: totalAttempts,
                });
                controller.enqueue(encoder.encode(`event: error\ndata: ${errorData}\n\n`));
                await sleep(1000); // Wait longer on persistent errors
                consecutiveEmptyPolls++;
              }
            }
          }

          if (consecutiveEmptyPolls >= maxEmptyPolls) {
            // Timeout - no activity for too long
            controller.enqueue(
              encoder.encode(
                `event: timeout\ndata: {"reason":"no_activity","attempts":${totalAttempts}}\n\n`
              )
            );
          }
        } catch (error) {
          console.error('Error in SSE stream:', error);
          const errorData = JSON.stringify({ error: 'Stream error' });
          controller.enqueue(encoder.encode(`event: error\ndata: ${errorData}\n\n`));
        } finally {
          clearInterval(keepAliveInterval);
        }

        abortController?.signal?.addEventListener('abort', () => {
          clearInterval(keepAliveInterval);
        });
      },

      cancel() {
        abortController?.abort();
      },
    });

    // Return SSE response
    const response = new NextResponse(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Connection: 'keep-alive',
        'X-Accel-Buffering': 'no', // Disable nginx buffering
      },
    });

    return addCorsHeaders(response, request, 'GET, OPTIONS');
  } catch (error: unknown) {
    console.error('Error setting up SSE stream:', error);

    if (error instanceof Error && error.message?.includes('not found')) {
      const response = NextResponse.json(
        { error: 'Session not found. Please create the session first.' },
        { status: 404 }
      );
      return addCorsHeaders(response, request, 'GET, OPTIONS');
    }

    const response = NextResponse.json(
      {
        error: `Failed to start stream: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
      { status: 500 }
    );
    return addCorsHeaders(response, request, 'GET, OPTIONS');
  }
}
