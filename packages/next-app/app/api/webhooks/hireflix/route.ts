import { NextResponse } from 'next/server';
import crypto from 'crypto';

import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { TASK_QUEUE_INTERNAL } from '@flow/sdk/shared';

// Constants
const WORKFLOW_NAME = 'hiringVideoWorkflow';
const EVENT_STATUS_CHANGE = 'interview.status-change';

// Hireflix webhook configuration
const HIREFLIX_CONFIG = {
  secretKey: process.env.HIREFLIX_WEBHOOK_SECRET || '',
};

// Verify the Hireflix webhook signature
function verifyHireflixSignature(signature: string, payload: string): boolean {
  if (!HIREFLIX_CONFIG.secretKey) {
    console.error('Missing HIREFLIX_WEBHOOK_SECRET environment variable');
    return false;
  }

  try {
    // Step 1: Extract and decode the signature from the header
    const decodedSignature = Buffer.from(signature, 'base64');

    // Step 2: Determine the expected signature
    // Compute an HMAC with the SHA256 hash function using the webhook secret as key and payload as message
    const hmac = crypto.createHmac('sha256', HIREFLIX_CONFIG.secretKey);
    hmac.update(payload);
    const expectedSignature = hmac.digest();

    // Step 3: Compare the signatures
    return crypto.timingSafeEqual(decodedSignature, expectedSignature);
  } catch (error) {
    console.error('Error verifying Hireflix signature:', error);
    return false;
  }
}

// Interface for Hireflix webhook payload based on the example
interface HireflixWebhookPayload {
  event: string;
  data: {
    id: string;
    externalId?: string;
    status: string;
    hash: string;
    createdAt: number;
    completed?: number;
    deleted?: number | null;
    archived?: number | null;
    finalist?: number | null;
    answered: boolean;
    position: {
      id: string;
      name: string;
    };
    candidate: {
      name: string;
      firstName: string;
      lastName: string;
      email: string;
      phone: string | null;
    };
    score?: {
      value: number;
    };
    url: {
      short: string;
      private: string;
      public: string;
    };
    thumbnail?: string;
  };
  date: number;
}

export async function POST(req: Request) {
  const headers = new Headers({
    Connection: 'keep-alive',
    'Keep-Alive': 'timeout=600',
    'Content-Type': 'application/json',
  });

  try {
    // Get raw payload first
    const rawPayload = await req.text();

    // Get signature header from Hireflix
    const signature = req.headers.get('x-hireflix-signature');

    // Always require signature
    if (!signature) {
      return NextResponse.json(
        { error: 'Missing x-hireflix-signature header' },
        { status: 401, headers }
      );
    }

    // Verify signature
    if (!verifyHireflixSignature(signature, rawPayload)) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401, headers });
    }

    // Parse the webhook payload
    const payload = JSON.parse(rawPayload) as HireflixWebhookPayload;
    const { event, data, date } = payload;

    if (!event || !data) {
      return NextResponse.json({ error: 'Invalid webhook payload' }, { status: 400, headers });
    }

    // Log the webhook event
    console.log(`Received Hireflix webhook: ${event}`, {
      interviewId: data.id,
      candidateName: data.candidate.name,
      status: data.status,
      position: data.position.name,
      timestamp: new Date(date).toISOString(),
    });

    // Handle different event types
    if (event === EVENT_STATUS_CHANGE) {
      // Process status change events (completed, finalist, etc.)
      console.log(`Interview status changed to: ${data.status} for ${data.candidate.name}`);

      const { temporalClient } = workerModule;

      await temporalClient.startWorkflow(WORKFLOW_NAME, {
        taskQueue: TASK_QUEUE_INTERNAL,
        workflowId: `hireflix__${date}`,
        args: [{ interviewId: data.id, candidateEmail: data.candidate.email }],
      });
    }

    // For now, just acknowledge receipt of the webhook
    return NextResponse.json(
      {
        status: 'accepted',
        event,
        interviewId: data.id,
        message: 'Webhook received successfully',
      },
      { headers }
    );
  } catch (error: any) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: error.message }, { status: 500, headers });
  }
}
