import { workerModule } from '@flow/sdk/lib/worker-module.js';
import { TASK_QUEUE_INTERNAL } from '@flow/sdk/shared';
import crypto from 'crypto';
import { NextResponse } from 'next/server';
import logger from 'logger';

// Generate short ID using timestamp and random digits
function generateShortId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 5);
}

// Ashby webhook configuration
const ASHBY_CONFIG = {
  secretToken: process.env.ASHBY_WEBHOOK_SECRET || '',
  workflowName: 'ashbyWebhookRouter', // This workflow will route to specific handlers
  skipSignatureVerification: process.env.NODE_ENV === 'development',
};

// Verify the Ashby webhook signature
function verifyAshbySignature(signature: string, payload: string): boolean {
  if (!ASHBY_CONFIG.secretToken) {
    logger.warn('Skipping signature verification (development mode or no secret)');
    return true;
  }

  try {
    const hmac = crypto.createHmac('sha256', ASHBY_CONFIG.secretToken);
    hmac.update(payload);
    const digest = hmac.digest('hex');
    const expectedSignature = `sha256=${digest}`;

    return crypto.timingSafeEqual(Buffer.from(expectedSignature), Buffer.from(signature));
  } catch (error) {
    logger.error('Error verifying Ashby signature:', error);
    return false;
  }
}

export async function POST(req: Request) {
  const headers = new Headers({
    Connection: 'keep-alive',
    'Keep-Alive': 'timeout=600',
    'Content-Type': 'application/json',
  });

  try {
    // Get raw payload first
    const rawPayload = await req.text();

    // Get signature header from Ashby
    const signature = req.headers.get('ashby-signature');

    // Check for signature
    if (!signature) {
      return NextResponse.json(
        { error: 'Missing Ashby-Signature header' },
        { status: 401, headers }
      );
    }

    // Verify signature if present
    if (!verifyAshbySignature(signature, rawPayload)) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401, headers });
    }

    // Parse the webhook payload
    const payload = JSON.parse(rawPayload);
    const { action, data } = payload;

    if (!action) {
      return NextResponse.json({ error: 'Invalid webhook payload' }, { status: 400, headers });
    }

    if (action === 'ping') {
      logger.info('Received Ashby ping webhook for action type:', data?.webhookActionType);

      return NextResponse.json(
        {
          status: 'ok',
          message: 'Ping received successfully',
          webhookActionType: data?.webhookActionType,
        },
        { headers }
      );
    }

    // Use a random short string for the workflow ID instead of webhookActionId
    const shortId = generateShortId();
    const workflowId = `ashby__${shortId}`;
    logger.info(`Processing Ashby webhook: ${action}`, data);

    // For ping events, we can respond immediately without running a workflow

    // Execute the workflow router for non-ping events
    const { temporalClient } = workerModule;
    await temporalClient.startWorkflow(ASHBY_CONFIG.workflowName, {
      taskQueue: TASK_QUEUE_INTERNAL,
      workflowId,
      args: [{ action, data }],
    });

    return NextResponse.json(
      {
        status: 'accepted',
        action,
      },
      { headers }
    );
  } catch (error: any) {
    logger.error('Webhook error:', error);
    return NextResponse.json({ error: error.message }, { status: 500, headers });
  }
}
