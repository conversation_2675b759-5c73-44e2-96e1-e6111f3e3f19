# Run lint-staged to check staged files

if command -v yarn > /dev/null 2>&1; then
  yarn lint-staged || exit 1
else
  printf "\e[1;5;33m"
  printf "╭──────────────────────────────────────────╮\n"
  printf "│╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱│\n"
  printf "│╱╱╱\e[0;1;31m ╷    ╷╭───╮╭──╮ ╭╮  ╷╶┬╴╭╮  ╷╭───╮ \e[5;33m╱╱╱│\n"
  printf "│╱╱╱\e[0;1;31m │    ││   ││  │ │╰╮ │ │ │╰╮ ││   ╵ \e[5;33m╱╱╱│\n"
  printf "│╱╱╱\e[0;1;31m │ ╭╮ │├───┤├──┴╮│ ╰╮│ │ │ ╰╮││  ─╮ \e[5;33m╱╱╱│\n"
  printf "│╱╱╱\e[0;1;31m ╰─╯╰─╯╵   ╵╵   ╵╵  ╰╯╶┴╴╵  ╰╯╰───╯ \e[5;33m╱╱╱│\n"
  printf "│╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱╱│\n"
  printf "╰──────────────────────────────────────────╯\n\e[0m"
  printf "\e[1;33mPre-commit hooks not executed. Make sure you lint and test manually.\e[0m\n"
fi
